#!/bin/bash

# Source folder để sử dụng những folder trong file
source scripts/script-helpers.sh

#Số lượng file .spec được chạy trong trong 1 lần
MAX_NUMBER_FILE_RUN=10

# Số lần re-run test fail
MAX_NUMBER_RE_RUN=2

PATH_E2E_FOLDER="e2e"
PATH_TEMPT_E2E_FOLDER="e2e-tempt"
VIETNAM="a-vietnam"
THAILAND="b-thailand"
INDONESIA="c-indonesia"

# TODO: update run for malaysia
MALAYSIA="a-vietnam"

PATH_PORT="$PATH_TEMPT_E2E_FOLDER/port.js"
PATH_INIT_APP="$PATH_TEMPT_E2E_FOLDER/init-app.spec.js"
PATH_INIT_VN="$PATH_TEMPT_E2E_FOLDER/$VIETNAM/a-init/index.spec.js"
PATH_INIT_TH="$PATH_TEMPT_E2E_FOLDER/$THAILAND/a-init/index.spec.js"
PATH_INIT_INDO="$PATH_TEMPT_E2E_FOLDER/$INDONESIA/a-init/index.spec.js"
PATH_INIT_MY="$PATH_TEMPT_E2E_FOLDER/$MALAYSIA/a-init/index.spec.js"

PATH_FOLDER_TEST_VN="$PATH_TEMPT_E2E_FOLDER/$VIETNAM"
PATH_FOLDER_TEST_TH="$PATH_TEMPT_E2E_FOLDER/$THAILAND"
PATH_FOLDER_TEST_ID="$PATH_TEMPT_E2E_FOLDER/$INDONESIA"
PATH_FOLDER_TEST_MY="$PATH_TEMPT_E2E_FOLDER/$MALAYSIA"

ROOT_DIR_FOLDER=$(pwd)
RESULT_FOLDER="result-test"
RESULT="$RESULT_FOLDER/result.txt"
TOTAL="$RESULT_FOLDER/total-result.txt"
FAIL="$RESULT_FOLDER/test-fail.txt"
SCRIPT_FAIL="$ROOT_DIR_FOLDER/$RESULT_FOLDER/script-fail.txt"
PREV_PACKAGE_JSON="$RESULT_FOLDER/prev-package.json"
PACKAGE_JSON="package.json"
MAX_RE_TRY=3

SPECIAL_CHAR=" ## "

PORT_NODE_DEFAULT=3333
PORT_GO_DEFAULT=9000
PORT_METRO_DEFAULT=8000
PORT_DB_DEFAULT=2000
FEATURE_GO_NAME=''

# Biến đếm số lượng các file .spec
total_file_spec=0

# Biến để đếm số lượng từ "REQUIRED"
total_required_count=0

total_passing=0
total_pending=0
total_failing=0

pattern_passing="([0-9]+) passing"
pattern_pending="([0-9]+) pending"
pattern_failing="([0-9]+) failing"

number_re_run=0

# Khai báo các biến
iso_code=""
requires=""
paths_test_in_merge_request=""
asset_token_gitlab=""
project_id=""
merge_request_id=""
number_vm=1
index_vm=0
number_devices=1
length_paths_test_in_merge_request=0

DOMAIN_GIT_LAB_API="https://gitlab.com/api/v4/projects"
LIST_PORT_METRO=() #Biến cache tất cả các port metro đang chạy

list_command_failed=$SCRIPT_FAIL
if [ -e "$SCRIPT_FAIL" ]; then
  echo "" >"$list_command_failed"
else
  touch $list_command_failed
fi

cache_exit_failed() {
  echo $1 >>"$list_command_failed"
  exit 1
}

exit_has_failed() {
  if grep -q '[^[:space:]]' "$list_command_failed"; then
    yarn ci:multi:docker:end
    # shutdown watchman để clear cache trên ram -> tự động boot lại nêu start metro và khởi động app
    watchman shutdown-server
    log_color $RED "⬇️  SCRIPT FAILED $(cat "$list_command_failed")"
    kill_all_port_metro
    exit 1
  fi
}

get_path_from_title_test() {
  local path
  path=$(echo "$1" | awk -F 'FILE: | -' '{print $2}')
  echo "$(convert_path_e2e_to_e2e_tempt $path)"
}

convert_path_e2e_to_e2e_tempt() {
  local input_string="$1"
  local modified_string

  # Sử dụng sed để thay thế chữ "e2e/" đầu tiên bằng "e2e-tempt/"
  modified_string=$(echo "$input_string" | sed "s#${PATH_E2E_FOLDER}/#${PATH_TEMPT_E2E_FOLDER}/#")

  echo "$modified_string"
}

extract_line_from_title_test() {
  local line
  line=$(echo "$1" | grep "LINE ")
  line="$(echo "$line" | sed -e 's/:$//' -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')"
  echo "$line" | sed 's/[[\.*^$/]/\\&/g'
}

fix_build_new_xcode() {
  xcode_version=$(xcodebuild -version | grep Xcode | awk '{print $2}')
  log_color $GREEN "XCODE --VERSION $xcode_version"
  xcode_version_error="15.4"

  # Kiểm tra nếu phiên bản là 15.4
  if [[ "$xcode_version" == $xcode_version_error ]]; then
    file_path="ios/Pods/Flipper/xplat/Flipper/FlipperTransportTypes.h"

    # Kiểm tra xem file có tồn tại hay không
    if [[ -f "$file_path" ]]; then
      # Kiểm tra xem dòng '#include <functional>' đã tồn tại hay chưa
      if ! grep -q '#include <functional>' "$file_path"; then
        # Chèn dòng '#include <functional>' dưới dòng '#include <string>'
        sed -i '' '/#include <string>/a\'$'\n''#include <functional>' "$file_path"
      fi
      head -n 20 "$file_path"
      log_color $GREEN "FIX BUILD XCODE $xcode_version_error -> DONE"
    else
      log_color $RED "File không tồn tại: $file_path"
    fi
  fi
}

check_package_json() {
  # Check nếu file package.json hiện tại khác với file prev-package.json đã lưu trước đó
  # Kiểm tra nếu tệp chưa tồn tại
  if [ ! -e "$PREV_PACKAGE_JSON" ]; then
    # Nếu tệp chưa tồn tại, tạo tệp
    touch "$PREV_PACKAGE_JSON"
  fi

  if diff -q "$PACKAGE_JSON" "$PREV_PACKAGE_JSON" >/dev/null; then
    log_color $GREEN "===> $(pwd) PACKAGE.JSON NOT CHANGED"
    return 0
  else
    log_color $RED "===> $(pwd) PACKAGE.JSON CHANGED"
    rm -rf $PREV_PACKAGE_JSON
    return 1
  fi
}

cache_package_json() {
  cat $PACKAGE_JSON >$PREV_PACKAGE_JSON
  log_color $GREEN "$(pwd) CACHE $PACKAGE_JSON > $PREV_PACKAGE_JSON"
}

copy_btaskee_folder() {
  log_color $YELLOW "COPY_BTASKEE_FOLDER TO $1 -> RUNNING..."
  local start_time=$(date +%s)
  # Đường dẫn tới thư mục đích
  destination="../$1"

  # Nếu thư mục đích thì tạo
  if [ ! -d "$destination" ]; then
    mkdir -p "$destination"
  fi

  # Sao chép tất cả các tệp và thư mục trừ .git
  rsync -a --delete --exclude=$RESULT_FOLDER --exclude="node_modules" --exclude="ios/build" --exclude="ios/Pods" --exclude="android/build" --exclude="android/.gradle" --exclude="android/zpdk-release/build" --exclude=".DS_Store" . "$destination/"
  local end_time=$(date +%s)
  local elapsed_time=$((end_time - start_time))

  log_color $GREEN "COPY_BTASKEE_FOLDER TO $1 -> DONE (${elapsed_time}s)"
}

copy_artifacts_folder() {
  exit_has_failed

  # Đường dẫn tới thư mục nguồn
  source_dir="$(pwd)/artifacts"

  # Đường dẫn tới thư mục đích
  destination_dir="${ROOT_DIR_FOLDER}/artifacts"

  copy_directory $source_dir $destination_dir
}

copy_result_file() {
  exit_has_failed
  order=$1

  # Đường dẫn tới thư mục nguồn
  source_dir=$(pwd)/$RESULT

  # Đường dẫn tới thư mục đích
  destination_dir="${ROOT_DIR_FOLDER}/$RESULT"
  result_device="${ROOT_DIR_FOLDER}/${RESULT_FOLDER}/result-device-$order.log"

  copy_file $source_dir $destination_dir
  copy_file $source_dir $result_device
}

copy_e2e_to_e2e_tempt() {
  # Remove tempt-e2e folder if it exists
  if [ -d "$PATH_TEMPT_E2E_FOLDER" ]; then
    rm -rf "$PATH_TEMPT_E2E_FOLDER"
  fi

  # Create tempt-e2e folder
  mkdir -p "$PATH_TEMPT_E2E_FOLDER"

  # Copy the entire e2e folder to temp_e2e
  cp -r "$PATH_E2E_FOLDER"/. "$PATH_TEMPT_E2E_FOLDER"
}

command_run_e2e() {
  exit_has_failed

  order=$1
  shift
  list=$@
  echo "detox test -c ios.sim$order.debug --record-videos failing --take-screenshots failing $list"
  detox test -c ios.sim$order.debug --record-videos failing --take-screenshots failing $list
}

# Hàm chạy lệnh detox build với các thông số
command_build_e2e() {
  exit_has_failed
  # fix build xcode trước khi build
  fix_build_new_xcode

  # 2 tham số
  # $1 tên simualtor  1 2 3
  # $2 port
  local simulator=$1
  local port=$2
  local result_file="$RESULT_FOLDER/build_e2e_$simulator.txt"
  local number_re_try=0

  log_color $YELLOW "RCT_METRO_PORT=$port detox build -c ios.sim$simulator.debug -> RUNNING..."

  while true; do
    if [ $number_re_try -eq $MAX_RE_TRY ]; then
      log_color $RED "$(pwd)"
      log_color $RED "====================================================="
      # In ra terminal 50 dòng cuối cùng của file result
      tail -n 50 "$result_file"
      kill_metro_by_port $port
      log_color $RED "RCT_METRO_PORT=$port detox build -c ios.sim$simulator.debug -> FAILED"
      cache_exit_failed "RCT_METRO_PORT=$port detox build -c ios.sim$simulator.debug"
      # Xóa file result
      rm -rf $result_file
      break
    fi
    local start_time=$(date +%s)
    # Ghi kết quả vào result file và không in ra terminal
    RCT_METRO_PORT=$port detox build -c ios.sim$simulator.debug >$result_file 2>&1
    local exit_code=$? #Lưu lại kết quả lệnh ở trên

    local end_time=$(date +%s)
    local elapsed_time=$((end_time - start_time))m

    if [ $exit_code -ne 0 ]; then
      ((number_re_try++))
      log_color $RED "RCT_METRO_PORT=$port detox build -c ios.sim$simulator.debugt -> FAIL RETRY [$number_re_try] (${elapsed_time}s) "
    else
      # Khi build success thì cache lại file package.json
      cache_package_json

      log_color $GREEN "RCT_METRO_PORT=$port detox build -c ios.sim$simulator.debug -> DONE (${elapsed_time}s)"
      # Xóa file result
      rm -rf $result_file
      break
    fi

  done

}

command_ci_reset() {
  exit_has_failed
  local number_re_try=0
  local folder_name=$1
  local result_file="$RESULT_FOLDER/ci_install_$folder_name.txt"

  log_color $YELLOW "CI:RESET $folder_name -> RUNNING..."

  while true; do
    if [ $number_re_try -eq $MAX_RE_TRY ]; then
      log_color $RED "====================================================="
      # In ra terminal 50 dòng cuối cùng của file result
      tail -n 50 "$result_file"
      log_color $RED "$(pwd)"
      log_color $RED "START CONTAINER $i -> FAILED"
      cache_exit_failed "ci:reset $folder_name"
      # Xóa file result
      rm -rf $result_file
      break
    fi
    local start_time=$(date +%s)
    (yarn ci:reset) >$result_file 2>&1
    local exit_code=$?

    local end_time=$(date +%s)
    local elapsed_time=$((end_time - start_time))

    if [ $exit_code -ne 0 ]; then
      ((number_re_try++))
      log_color $RED "CI:RESET $folder_name -> FAIL RETRY [$number_re_try] (${elapsed_time}s)"
    else
      log_color $GREEN "CI:RESET $folder_name -> DONE (${elapsed_time}s)"
      is_success=true
      # Xóa file result
      rm -rf $result_file
      break
    fi
  done
}

command_run_multi_docker() {
  local start_time=$(date +%s)
  log_color $YELLOW "YARN CI:RUN:MULTI:DOCKER $number_devices CONTAINER -> RUNNING..."
  # Start nhiều docker compose
  yarn ci:multi:docker:run --numberContainer=$number_devices --portNode=$PORT_NODE_DEFAULT --portGo=$PORT_GO_DEFAULT --portMetro=$PORT_METRO_DEFAULT --portDB=$PORT_DB_DEFAULT --featureGoName=$FEATURE_GO_NAME
  local end_time=$(date +%s)
  local elapsed_time=$((end_time - start_time))
  log_color $GREEN "YARN CI:RUN:MULTI:DOCKER $number_devices CONTAINER -> DONE (${elapsed_time}s)"
}

command_start_metro() {
  exit_has_failed
  local port="$1"
  echo "yarn start --port=$port &"
  run_command_not_log yarn start --port=$port &
  LIST_PORT_METRO+=($port)
}

kill_metro_by_port() {
  # 1 tham số
  # $1 port metro muốn kill
  # ví dụ kill_metro_by_port 8081 => kill port 8081
  local port=$1
  lsof -ti :$port | xargs kill -9
  log_color $GREEN "KILL PORT $port -> DONE"
}

kill_all_port_metro() {
  for port in "${LIST_PORT_METRO[@]}"; do
    kill_metro_by_port $port
  done
  log_color $RED "KILL ALL PORT -> DONE"
}

change_port() {
  # 2 tham số
  # $1 port để cần chạy go
  # $2 port để cần chạy node
  local PORT_GO=$1
  local PORT_NODE=$2
  local REPO=$3
  local file_path_dev_env="dev.env"
  local file_step_definitions=$PATH_PORT
  # Tìm trong file file_path_dev_env dòng có text SERVER_API_PORT để thay thế giá trị đó bằng port go được truyền vào
  sed -i.bak -e "s/SERVER_API_PORT=.*/SERVER_API_PORT=$PORT_GO/" "$file_path_dev_env"
  rm "${file_path_dev_env}.bak"

  # Tìm trong file file_step_definitions dòng có text PORT_GO để thay thế giá trị đó bằng port go được truyền vào
  sed -i.bak -e "s/const PORT_GO =.*/const PORT_GO =$PORT_GO/" "$file_step_definitions"

  # Tìm trong file file_step_definitions dòng có text PORT_NODE để thay thế giá trị đó bằng port node được truyền vào
  sed -i.bak -e "s/const PORT_NODE =.*/const PORT_NODE =$PORT_NODE/" "$file_step_definitions"
  rm "${file_step_definitions}.bak"
  log_color $GREEN "CHANGE_PORT REPO $REPO: PORT_GO=$PORT_GO PORT_NODE=$PORT_NODE -> DONE"
}

chunk_array() {
  # Function tách nhỏ 1 mảng thành 1 mảng theo length truyền vào những kết quả trả ra là 1 string
  # Ví dụ:
  # chunk_array 2 1 2 3 4 5 6 7 8
  # $1 = 2 (chiều dài mới của mảng)
  # $@ = [1,2,3,4,5,6,7,8] (Mảng được truyền vào)
  # kết quả là 1 chuỗi: 1 2 3 4 ## 5 6 7 8
  # Lưu ý khi sử dụng dùng đoạn code bên dưới để biến kết quả chunk_array thành 1 mảng
  # input_vm=$(chunk_array 1 1 2 3 4 5 6 7 8)
  # while read -r -d "$(trim $SPECIAL_CHAR)" item; do
  #   if [[ -n "$item" ]]; then
  #     chunk_spec_files_vm+=("$item")
  #   fi
  # done <<<"$input_vm"

  local newLength=$1 #Length mới
  shift
  local arr=("$@")           # Mảng được truyền vào
  local arrLength=${#arr[@]} # Chiều dài mảng được truyền vào

  # Mảng kết quả
  local result=()

  # String kết quả dùng dể convert mảng sang string
  resultStr=""

  # Phần còn lại > độ dà của mảng ban đầu chia lấy phần dư độ dài mảng truyền vào
  local remainder=$((arrLength % newLength))

  # Độ dài quy định của mảng con bên trong
  local chunkLength=$((arrLength / newLength))

  if ((chunkLength < 2)); then
    # Nếu độ dài mảng con nhỏ hơn 2 thì sẽ gom lại thành 1 mảng con
    resultStr+="${arr[@]}$SPECIAL_CHAR"
  else
    local chunkTempt=""

    for e in "${arr[@]}"; do
      chunkTempt+=" $e"

      # Độ dài chuỗi chunkTempt
      lengthChuckTempt=$(echo $chunkTempt | wc -w)

      # Nếu độ dài của mảng chuckTempt bằng độ dài quy định thì add vào mảng result và reset mảng chuckTempt
      if (($lengthChuckTempt >= chunkLength)); then
        result+=("$(trim "$chunkTempt")")
        chunkTempt=""
      fi
    done

    # nếu "Phần còn lại" lớn hơn 0
    if ((remainder > 0)); then
      # "Mảng dư" > Tách phần còn lại chưa được gán vào mảng
      local arrRedundant=("${arr[@]:arrLength-remainder}")

      # index cuối cùng của mảng result
      local lastIndex=$((${#result[@]} - 1))

      # Giá trị của cùng của mảng result
      local lastResultItem=("${result[@]:lastIndex:1}")

      # Set lại item cuối cùng của mảng result bằng chính nó cộng "Mảng dư"
      result[$lastIndex]="${lastResultItem[@]} ${arrRedundant[@]}"
    fi

    for chunk in "${result[@]}"; do
      resultStr+="$chunk$SPECIAL_CHAR"
    done
  fi
  echo $resultStr
}

# Ghi lại kết quả chạy test từ file $RESULT vào folder $FAIL và $TOTAL
catch_result() {
  exit_has_failed

  # Tìm các dòng fail và in ra 3 dòng tiếp theo và ghi đè
  awk '/\)\ FILE:/ {sub(/.*FILE:/, "FILE:", $0); print "[" (++count) "]", $0; for(i=1;i<=2;i++) {getline; print };print ""}' $RESULT >>$FAIL

  # Tìm các dòng chứa passing | failing | pending
  awk "/([0-9]+) passing|([0-9]+) failing|([0-9]+) pending/" $RESULT >>$TOTAL
}

# Đếm lại tất cả kết quả test passing, pending, failing trong file $TOTAL
count_data_run() {
  # Count và Trả kết quả test
  while IFS= read -r line; do
    if [[ $line =~ $pattern_passing ]]; then
      number=$((BASH_REMATCH[1] + 0))
      total_passing=$((total_passing + number))
    fi
    if [[ $line =~ $pattern_pending ]]; then
      number=$((BASH_REMATCH[1] + 0))
      total_pending=$((total_pending + number))
    fi
    if [[ $line =~ $pattern_failing ]]; then
      number=$((BASH_REMATCH[1] + 0))
      total_failing=$((total_failing + number))
    fi
  done <"$TOTAL"
}

# Dùng để fix test (lấy ra những path test fail từ file $RESULT)
# lấy data từ repo trên runner paste vào file $RESULT
re_run_test_fail() {
  # Số re-run +1
  ((number_re_run++))
  log_color $RED "====================== RE_RUN_TEST_FAIL [$number_re_run] ========================"

  # Kiểm tra file FAIL có giá trị thì mới tiếp tục
  if grep -q '[^[:space:]]' "$FAIL"; then
    copy_e2e_to_e2e_tempt

    echo -e "\n\n"
    cat "$FAIL" | while IFS= read -r line; do
      log_color $RED "$line"
    done
    echo -e "\n"
    log_color $GREEN "$total_passing passing"
    log_color $RED "$total_failing failing"
    log_color $RED "================================================================="

    file_paths_re_run=()

    # clean file failed | total giữ lại file result để lọc ra những test fail
    >$FAIL
    >$TOTAL
    catch_result

    file_path=''
    while IFS= read -r line; do

      # Trích xuất file path từ dòng chứa "FILE:"
      if [[ $line == *"FILE:"* ]]; then
        file_path=$(get_path_from_title_test "$line")

        # kiểm tra file path có giá trị thì mới add vào mảng
        if [ -n "$file_path" ]; then
          # add file_path vào mảng file_path_re_run
          file_paths_re_run+=("$file_path")

          # Remove các item trùng nhau trong mảng
          file_paths_re_run=($(echo "${file_paths_re_run[@]}" | tr ' ' '\n' | sort -u))
        fi

      fi

      # Trích xuất test line từ dòng chứa "before each" hook for "LINE thì sẽ thêm describe.only để chạy lại toàn bọ file test
      if [[ $line == *"\"before each\" hook for \"LINE"* ]]; then

        sed -i.bak -e "s/describe(/describe.only(/" "$file_path"

        rm "${file_path}.bak"
      fi

      # Trích xuất test line từ dòng chứa "LINE" thì replace it( thành it.only( vào file_path
      if [[ $line == *"LINE"* ]]; then

        result_line=$(extract_line_from_title_test "$line")

        # Tìm và replace it( thành it.only(
        sed -i '' -e "/$result_line/s/it(/it.only(/g" "$file_path"
      fi

    done <"$FAIL"

    if [ ${#file_paths_re_run[@]} -gt 0 ]; then
      yarn e2e:remove:artifacts
      # run test và ghi đè vào file $RESULT
      run_e2e_test true "${file_paths_re_run[@]}"
    else
      echo "Không tìm thấy path fail"
    fi

    # clean file failed | total
    >$FAIL
    >$TOTAL
    catch_result

    # Count và Trả kết quả test
    total_failing=0
    count_data_run

    # Nếu total_failing lớn hơn 0 (-gt) thì tiếp tục xử lý
    # Ngược lại thì in ra kết quả và kết thúc
    if [[ $total_failing -gt 0 ]]; then
      # Nếu số lần re-run lớn hơn hoặc bằng (-ge) thì sẽ in ra kết quả và kết thúc
      # Ngược lại thì sẽ chạy lại re_run_test_fail
      if [[ $number_re_run -ge $MAX_NUMBER_RE_RUN ]]; then
        printf_result
      else
        re_run_test_fail
      fi
    else
      printf_result
    fi
  else
    printf_result
  fi
}

get_path_test_from_merge_request() {
  if [ -n "$asset_token_gitlab" ] && [ -n "$project_id" ] && [ -n "$merge_request_id" ]; then
    # Dùng gitlab API để lấy file tất cả các thay đổi của 1 PR
    url="${DOMAIN_GIT_LAB_API}/${project_id}/merge_requests/${merge_request_id}/changes"
    local res=$(curl -s -X GET $url -H "Private-Token: $asset_token_gitlab")
    local error=$(echo "$res" | jq -r '.error')

    if [ "$error" == "null" ]; then
      # Lọc chỉ lấy nhưng file thay đổi có đuôi là .spec.js
      local listPathSpec=$(echo "$res" | jq -r '.changes[] | select(.new_path | endswith(".spec.js")) | .new_path')

      # Kiểm tra độ dài của listPathSpec có lớn hơn 0 và item có giá trị thì mới thực thi
      if [ $(echo "$listPathSpec" | grep -c -v '^$') -gt 0 ]; then
        log_color $YELLOW "=================================================="
        log_color $YELLOW "Các paths file .spec.js thay đổi trong merge request:"
        index=1
        # Vòng lặp for để in từng phần tử trong danh sách
        while IFS= read -r line; do
          if [ -n "$line" ]; then
            log_color $YELLOW "[$index] $line"
          fi
          ((index++))
        done <<<"$listPathSpec"
        log_color $YELLOW "=================================================="
      fi
      paths_test_in_merge_request=$listPathSpec
    else
      echo "error: get_path_test_from_merge_request $error"
    fi
  fi
}

get_feature_name_from_merge_request() {
  if [ -n "$asset_token_gitlab" ] && [ -n "$project_id" ] && [ -n "$merge_request_id" ]; then
    # Dùng gitlab API để lấy thông tin của 1 PR
    url="${DOMAIN_GIT_LAB_API}/${project_id}/merge_requests/${merge_request_id}"
    local res=$(curl -s -X GET $url -H "Private-Token: $asset_token_gitlab")
    local error=$(echo "$res" | jq -r '.error')

    if [ "$error" == "null" ]; then
      # Trích xuất field "title" bằng cách sử dụng jq
      title=$(echo "$res" | jq -r '.title')
      log_color $YELLOW "TITLE PR: $title"

      # Trích xuất đoạn GO-... từ title và bỏ dấu #
      go_part=$(echo "$title" | grep -o '#GO-[0-9]*' | sed 's/#GO-//')

      # Kiểm tra nếu go_part có giá thì mới gán vào FEATURE_GO_NAME
      if [ -n "$go_part" ]; then
        FEATURE_GO_NAME=$go_part
        log_color $YELLOW "FEATURE_GO_NAME: $FEATURE_GO_NAME"
      fi
    else
      echo "error: get_feature_name_from_merge_request $error"
    fi
  fi
}

printf_result() {
  local color=$GREEN
  local is_failed=false
  # clear docker khi chạy test xong
  yarn ci:multi:docker:end
  exit_has_failed

  if [[ $total_failing -gt 0 || $total_passing -eq 0 ]]; then
    is_failed=true
  fi

  # check test fail
  if $is_failed; then
    if grep -q '[^[:space:]]' "$FAIL"; then
      color=$RED
      echo -e "${RED}=========================== TEST FAIL ===========================${NC}"
      echo -e "\n\n"
      cat "$FAIL" | while IFS= read -r line; do
        log_color $RED "$line"
      done
      echo -e "\n"
      echo -e "${RED}=================================================================${NC}"
      node scripts/cache-test-fail.js $FAIL
    fi
  fi

  log_color $color "\n\n====================== RESULT TEST ========================\n\n"
  log_color $GREEN "$total_passing passing"
  log_color $YELLOW "$total_pending pending"
  log_color $RED "$total_failing failing"
  log_color $color "\n\n-----------------------------------------------------------\n\n"
  if [ $length_paths_test_in_merge_request -gt 0 ]; then
    log_color $color "TOTAL file .spec changes in merge request: $length_paths_test_in_merge_request"
  fi
  log_color $color "TOTAL file .spec: $total_file_spec"
  log_color $color "TOTAL test: $((total_passing + total_pending + total_failing))"
  log_color $color "TOTAL test REQUIRED : $total_required_count"
  log_color $color "re-run: $number_re_run"
  # Lưu thời gian kết thúc
  end_time=$(date +%s)
  # Tính toán tổng thời gian
  total_time=$((end_time - start_time))
  total_time_formatted=$(date -u -r $total_time +'%H giờ %M phút %S giây')
  end_date=$(date +"%d/%m/%Y %T")
  # In thời gian bắt đầu
  log_color $color "Thời gian chạy: $start_date - $end_date"
  log_color $color "Tổng thời gian chạy test: $total_time_formatted"
  log_color $color "\n\n===========================================================\n\n"
  echo -e "\n\n"

  # check có test fail thì throw error
  if $is_failed; then
    exit 1
  else
    exit 0
  fi
}

run_init_app() {
  exit_has_failed
  local number_re_try=0
  local order=$1
  local port=$2

  while true; do
    if [ $number_re_try -eq $MAX_RE_TRY ]; then
      # Nếu chạy test init app fail thì throw error
      log_color $RED "[DEVICE $order] RUN E2E INIT APP -> FAILED [$number_re_try]"
      kill_metro_by_port $port # kill port đang chạy
      cache_exit_failed "command_run_e2e $order $PATH_INIT_APP"
      break
    fi

    command_run_e2e $order $PATH_INIT_APP
    if [ $? -ne 0 ]; then
      ((number_re_try++))
    else
      is_success=true
      break
    fi
  done
}

run_e2e_test() {
  local is_re_run=$1      #$1 tham số đầu tiên kiếm tra có phải là chạy lại hay không
  shift                   #Loại tham số đầu tiên ra khỏi list tham số
  local spec_files=("$@") # Lấy tất cả những tham số còn lại (list path file .spec muốn chạy)

  # --------- CLEAR CACHE TRƯỚC KHI CHẠY ------------
  # Xóa folder artifacts
  yarn e2e:remove:artifacts
  # clean detox mỗi lần chạy
  yarn detox clean-framework-cache
  yarn detox build-framework-cache

  # Nếu là chạy lại thì không chạy reset và change port
  if [ "$is_re_run" == "false" ]; then
    # Start nhiều docker compose
    command_run_multi_docker
  fi

  # clear file result | failed | total
  >$RESULT
  >$FAIL
  >$TOTAL
  # --------------------------------------------------

  # Mảng chỉ chưa unique item trong spec_files
  unique_spec_files=()

  # Lặp qua từng phần tử trong mảng spec_files
  for file in "${spec_files[@]}"; do
    # Kiểm tra nếu phần tử chưa tồn tại trong mảng unique_spec_files thì thêm vào
    if [[ ! " ${unique_spec_files[@]} " =~ " $file " ]]; then
      unique_spec_files+=("$(convert_path_e2e_to_e2e_tempt $file)")
    fi
  done

  # sort lại spec_files
  IFS=$'\n' spec_files_sort=($(sort <<<"${unique_spec_files[*]}"))
  log_color $YELLOW "===== LOG SPEC FILES SORT ====="
  for file in "${spec_files_sort[@]}"; do
    log_color $YELLOW $file
  done
  log_color $YELLOW "===== END LOG SPEC FILES SORT ====="
  unset IFS

  chunk_spec_files_vm=()
  chunk_spec_files_devices=()

  # Ví dụ:
  # spec_files_sort = [1,2,3,4,5,6.7.8] (Mảng được truyền vào)
  # number_vm = 2
  # Kết quả: chunk_spec_files=[[1,2,3,4],[5,6,7,8]]
  # Tách mảng spec_files_sort thành chunk_spec_files 1 mảng chứa những phần tử là những mảng nhỏ. chunk_spec_files.length = number_vm
  input_vm=$(chunk_array $number_vm ${spec_files_sort[@]})
  while read -r -d "$(trim $SPECIAL_CHAR)" item; do
    if [[ -n "$item" ]]; then
      chunk_spec_files_vm+=("$item")
    fi
  done <<<"$input_vm"

  # Lấy ra mảng VM đang chạy
  # Ví dụ
  # chunk_spec_files_vm=[[1,2,3,4],[5,6,7,8]] (Mảng VM đã được tách nhỏ ở trên)
  # index_vm=0 (index VM được truyền vào)
  # ${chunk_spec_files_vm[$index_vm] = [1,2,3,4] lấy giá trị mảng chunk_spec_files_vm theo index VM)
  # number_devices=2 (Số lượng devices chạy trên 1 máy)
  # input_devicesTách nhỏ mảng VM chỉ đinh theo số lượng devices
  # Kết quả: chunk_spec_files_devices=[[1,2],[3,4]]
  input_devices=$(chunk_array $number_devices ${chunk_spec_files_vm[$index_vm]})
  while read -r -d "$(trim $SPECIAL_CHAR)" item; do
    if [[ -n "$item" ]]; then
      chunk_spec_files_devices+=("$item")
    fi
  done <<<"$input_devices"

  # Loop qua mảng chunk_spec_files_devices để chỉ định list path chạy trên mỗi device
  # Ví dụ
  # chunk_spec_files_devices=[[1,2],[3,4]]
  # device 1 => run 1 2
  # device 2 => run 3 4
  for index in "${!chunk_spec_files_devices[@]}"; do
    # lấy ra list spec theo index
    list_item="${chunk_spec_files_devices[$index]}"
    # Log list_item
    echo "Device $((index + 1)) => run: ${list_item[*]}"
    # run detox (thêm & ở cuối lệnh run để run song)
    run $is_re_run $index "$list_item" &

  done
  wait
}

run() {
  # Nhận vào 3 tham số
  # $1 biến để check phải là chạy re-run hay không
  # $2 là index của device
  # 2 lần shift loại $1 $2 lấy các phần tử còn lại là list path .spec được chạy
  local is_re_run=$1
  local index=$2
  shift
  shift
  local array=($@)
  local is_re_build="false"

  # Số thứ tự index + 1 là do sẽ được bắt đầu từ 1
  local order=$((index + 1))
  # Lấy tham số từ filde env-docker/order
  PORT_GO=$(grep 'PORT_GO' env-docker/${order} | cut -d '=' -f2-)
  PORT_NODE=$(grep 'PORT_NODE' env-docker/${order} | cut -d '=' -f2-)
  PORT_METRO=$(grep 'PORT_METRO' env-docker/${order} | cut -d '=' -f2-)

  paths_vn=""
  paths_th=""
  paths_id=""
  paths_my=""

  # Phân loại các đường dẫn
  for str in "${array[@]}"; do
    if [[ "$str" == *"$VIETNAM"* ]]; then
      paths_vn+="$str "
    elif [[ "$str" == *"$THAILAND"* ]]; then
      paths_th+="$str "
    elif [[ "$str" == *"$INDONESIA"* ]]; then
      paths_id+="$str "
    elif [[ "$str" == *"$MALAYSIA"* ]]; then
      paths_my+="$str "
    fi
  done

  # Tạo danh sách các đối tượng chạy
  list_run=()

  if [ -n "$paths_vn" ]; then
    list_run+=("$VIETNAM")
  fi
  if [ -n "$paths_th" ]; then
    list_run+=("$THAILAND")
  fi
  if [ -n "$paths_id" ]; then
    list_run+=("$INDONESIA")
  fi
  if [ -n "$paths_my" ]; then
    list_run+=("$MALAYSIA")
  fi

  # Mở simulator nếu chưa mở
  yarn ci:open:simulator "iPhone 11-$order"

  cd $ROOT_DIR_FOLDER
  # Nếu là chạy lại thì không copy file
  if [ "$is_re_run" == "false" ]; then
    # Copy folder gốc ra folder theo thứ tự devces
    copy_btaskee_folder $order
  fi

  # Đang trong thư mục gốc -> cd ra ngoài -> cd vào thư mục con
  cd ..
  cd $order

  # Nếu không phải re-run thì check package.json để build
  if [ "$is_re_run" == "false" ]; then
    check_package_json
    is_change_package_json=$?
    if [ $is_change_package_json -eq 0 ]; then
      is_re_build="false"
    else
      is_re_build="true"
    fi
  fi

  if [ "$is_re_build" == "true" ]; then
    # run ci:reset
    command_ci_reset $order
    if [ $? -ne 0 ]; then
      exit 1
      kill_metro_by_port $PORT_METRO # kill port đang chạy
    fi
  fi

  # clear file result và folder artifacts ở thư mục con
  >$RESULT
  yarn e2e:remove:artifacts

  # kill port metro trước khi run
  kill_metro_by_port $PORT_METRO

  # Start port metro
  command_start_metro $PORT_METRO

  if [ "$is_re_run" == "true" ]; then
    # Nếu là chạy lại thì copy folder e2e-tempt của thư mục gộc vào e2e-tempt của thư mục con
    copy_directory "$ROOT_DIR_FOLDER/$PATH_TEMPT_E2E_FOLDER" $(pwd)/$PATH_TEMPT_E2E_FOLDER
  fi

  # Change port thư mục con
  change_port $PORT_GO $PORT_NODE $order

  # Nếu là chạy lại thì không chạy build
  if [ "$is_re_build" == "true" ]; then
    # Build detox theo port và thứ tự device
    command_build_e2e $order $PORT_METRO
  fi

  run_init_app $order $PORT_METRO

  log_color $YELLOW "[DEVICE $order] RUN E2E TEST -> RUNNING..."
  log_color $GREEN "[DEVICE $order] LIST COUNTRY -> ${list_run[@]}"
  for name in "${list_run[@]}"; do
    case $name in
    $VIETNAM)
      path_init_country=$PATH_INIT_VN
      paths=$paths_vn
      ;;
    $THAILAND)
      path_init_country=$PATH_INIT_TH
      paths=$paths_th
      ;;
    $INDONESIA)
      path_init_country=$PATH_INIT_INDO
      paths=$paths_id
      ;;
    $MALAYSIA)
      path_init_country=$PATH_INIT_MY
      paths=$paths_my
      ;;
    esac

    # Run e2e init
    command_run_e2e $order $PATH_INIT_APP
    command_run_e2e $order $path_init_country -r
    if [ $? -ne 0 ]; then
      # Nếu run init country lỗi thì throw error
      log_color $RED "[DEVICE $order] RUN E2E INIT COUNTRY -> FAILED"
      kill_metro_by_port $PORT_METRO # kill port đang chạy
      cache_exit_failed " command_run_e2e $order $path_init_country -r"
    fi

    IFS=' ' read -r -a path_array <<<"$paths"
    total_paths=${#path_array[@]}
    number_of_run=1

    # Tách nhỏ list file theo MAX_NUMBER_FILE_RUN để chạy nhiều lần
    for ((i = 0; i < total_paths; i += MAX_NUMBER_FILE_RUN)); do

      path_run=("${path_array[@]:i:MAX_NUMBER_FILE_RUN}")

      # remove path init quốc gia vì đã được run ở trên
      path_run=("${path_run[@]/$PATH_INIT_APP/}")
      path_run=("${path_run[@]/$PATH_INIT_VN/}")
      path_run=("${path_run[@]/$PATH_INIT_TH/}")
      path_run=("${path_run[@]/$PATH_INIT_INDO/}")
      path_run=("${path_run[@]/$PATH_INIT_MY/}")

      time_run=$(date +"%d/%m/%Y %T")
      log_color $YELLOW "[DEVICE $order] RUN E2E TEST ($time_run) -> RUN [$number_of_run]"
      command_run_e2e $order ${path_run[@]} -r | tee -a $RESULT # Khi chạy sẽ ghi log vào file result
      ((number_of_run++))
    done
  done
  copy_artifacts_folder          # Copy folder artifacts từ folder con vào folder gốc
  copy_result_file $order              # Copy file result từ folder con vào file result folder gốc
  kill_metro_by_port $PORT_METRO # kill port đang chạy
  log_color $GREEN "[DEVICE $order] RUN E2E TEST -> DONE"
}

main() {
  # Lặp qua các tham số và tách giá trị từ tham sô
  for arg in "$@"; do
    case $arg in
    --isoCode=*)
      iso_code="${arg#*=}"
      echo "--isoCode=$iso_code"
      ;;
    --requires)
      requires="true"
      echo "--requires=true"
      ;;
    --projectId=*)
      project_id="${arg#*=}"
      echo "--projectId=$project_id"
      ;;
    --mergeRequestId=*)
      merge_request_id="${arg#*=}"
      echo "--mergeRequestId=$merge_request_id"
      ;;
    --assetTokenGitlab=*)
      asset_token_gitlab="${arg#*=}"
      echo "--assetTokenGitlab=$asset_token_gitlab"
      ;;
    --numberVM=*)
      number_vm=${arg#*=}
      echo "--numberVM=$number_vm"
      ;;
    --indexVM=*)
      index_vm=${arg#*=}
      echo "--indexVM=$index_vm"
      ;;
    --numberDevices=*)
      number_devices=${arg#*=}
      echo "--numberDevices=$number_devices"
      ;;
    esac
  done

  path_folder_test=''
  path_init_test_country=''

  # Lưu thời gian bắt đầu
  start_time=$(date +%s)
  start_date=$(date +"%d/%m/%Y %T")

  # tạo folder result-test nếu chưa có
  if [ ! -d $RESULT_FOLDER ]; then
    mkdir -p $RESULT_FOLDER
  fi

  # copy folder e2e sang e2e-tempt để thay đổi trên e2e-tempt
  copy_e2e_to_e2e_tempt

  # Dựa vào biến iso_code để lấy path folder test
  case "$iso_code" in
  "vn")
    path_folder_test=$PATH_FOLDER_TEST_VN
    ;;
  "id")
    path_folder_test=$PATH_FOLDER_TEST_ID
    ;;
  "th")
    path_folder_test=$PATH_FOLDER_TEST_TH
    ;;
  "my")
    path_folder_test=$PATH_FOLDER_TEST_MY
    ;;
  *)
    path_folder_test=$PATH_TEMPT_E2E_FOLDER
    ;;
  esac

  # Khởi tạo arr chứa tất cả file spec
  declare -a spec_files=()

  # Khởi tạo file temp
  tmp_file=$(mktemp)

  # Tìm tất cả những file .spec.js trong folder $path_folder_test và ghi vào file tmp_file
  find "$path_folder_test" -type f -name "*.spec.js" -print0 >"$tmp_file"

  # Đọc danh sách các tệp .spec.js dòng theo dòng
  while IFS= read -r -d '' spec; do
    # Đếm số lượng từ "REQUIRED" trong file hiện tại
    required_count=$(grep -o "\[REQUIRED\]" "$spec" | wc -l)
    if [ "$requires" == "true" ]; then
      # Nếu requires = true thì sẽ replace it( thành it.only( và add với mảng spec_files
      if grep -q "\[REQUIRED\]" "$spec"; then
        # Thay thế it( bằng it.only( trong các dòng chứa [REQUIRED]
        sed -i '' -e '/\[REQUIRED\]/s/it(/it.only(/g' "$spec"
        # Thêm path file vào mảng nếu trong file có chứa chữ [REQUIRED]
        spec_files+=("$spec")
      fi
    else
      # add tất cả tên file .spec.js vào mảng
      spec_files+=("$spec")
    fi
    # Cộng số lượng từ "REQUIRED" trong file hiện tại vào tổng số lượng
    total_required_count=$((total_required_count + required_count))
  done <"$tmp_file"

  # xóa file temp
  rm "$tmp_file"

  # Lấy ra những path e2e có thay đổi trong PR
  get_path_test_from_merge_request

  # Lấy ra title của PR
  get_feature_name_from_merge_request

  # Kiểm tra giá trị của $3 nếu có thì thêm vào mãng spec_files
  if [ -n "$paths_test_in_merge_request" ]; then
    # Thêm các phần tử từ chuỗi vào mảng
    for str in $paths_test_in_merge_request; do
      ((length_paths_test_in_merge_request++))
      path_converted="$(convert_path_e2e_to_e2e_tempt $str)"
      # Tìm và replace it( thành it.only(
      sed -i '' -e 's/it(/it.only(/g' "$path_converted"
      spec_files+=("$str")
    done
  fi

  # Tổng số lượng file .spec.js được chạy
  total_file_spec=${#spec_files[@]}
  log_color $YELLOW "TOTAL FILES .SPEC: $total_file_spec FILES"

  run_e2e_test false "${spec_files[@]}"

  cd $ROOT_DIR_FOLDER
  catch_result
  count_data_run

  # Xuất result test
  # Nếu có test fail và số lần re-run lớn hơn 0 thì sẽ cho re-run những test fail
  # Ngước lại thì sẽ in ra kết quả và kết thúc
  if [ "$total_failing" -gt 0 -a "$MAX_NUMBER_RE_RUN" -gt 0 ]; then
    if grep -q '[^[:space:]]' "$FAIL"; then
      re_run_test_fail
    else
      log_color $RED "TEST FAIL BUT $FAIL EMPTY"
      printf_result
    fi
  else
    printf_result
  fi
}

# yarn ci:e2e <...args>
# Danh sách args:
# --isoCode=<vn|th|id> *optional* Quốc gia cần chạy test
# --requires *optional* Chạy các test requires (kết hợp với --isoCode để chạy test reuqires 1 quốc gia)
# --projectId=? *optional* id của project trên gitlab
# --mergeRequestId=? *optional* id của merge request trên gitlab
# --assetTokenGitlab=? *optional* asset token trên git lab
# --numberDevices=? *optional* số devices khi chạy test 1 lần
# --numberVM=? *optional* số máy ảo khi chạy test 1 lần
# --indexVM=? *optional* xác định đang chạy trên máy ảo nào
# Lưu ý: --projectId --mergeRequestId --assetTokenGitlab sẽ được truyền cùng lúc khi muốn run test các file .spec.js thay đổi có trong merge request
main $@
