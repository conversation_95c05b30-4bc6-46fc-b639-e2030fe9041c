#!/usr/bin/env node

/**
 * generate-mschecked.js
 *
 * Đọc ms.json và vi.json, so s<PERSON>h key ở mọi cấp độ,
 * - Nếu ms.json không có key => lấy vi.json
 * - Nếu ms.json có key:
 *     • Cả hai là object thuần => merge đệ quy
 *     • Cùng kiểu (cùng primitive hoặc cùng array) => lấy ms.json
 *     • <PERSON>h<PERSON>c kiểu => lấy vi.json
 * Kết quả ghi vào mschecked.json giữ nguyên thứ tự key của vi.json.
 */

const fs = require('fs');
const path = require('path');

function isPlainObject(val) {
  return val !== null && typeof val === 'object' && !Array.isArray(val);
}

function deepMerge(msData, viData) {
  const result = {};

  Object.keys(viData).forEach((key) => {
    const viVal = viData[key];
    const hasMs = Object.prototype.hasOwnProperty.call(msData, key);
    const msVal = msData[key];

    if (!hasMs) {
      // ms.json thiếu -> dùng vi.json
      result[key] = viVal;
      return;
    }

    // cả hai có key
    if (isPlainObject(viVal) && isPlainObject(msVal)) {
      // cùng là object -> merge đệ quy
      result[key] = deepMerge(msVal, viVal);
      return;
    }

    // xử lý array
    if (Array.isArray(viVal)) {
      if (Array.isArray(msVal)) {
        // cùng array -> giữ ms.json
        result[key] = msVal;
      } else {
        // khác kiểu -> lấy vi.json
        result[key] = viVal;
      }
      return;
    }

    // xử lý primitive (string, number, boolean, null)
    const typeVi = viVal === null ? 'null' : typeof viVal;
    const typeMs = msVal === null ? 'null' : typeof msVal;
    if (typeVi === typeMs) {
      // cùng kiểu -> lấy ms.json
      result[key] = msVal;
    } else {
      // khác kiểu -> lấy vi.json
      result[key] = viVal;
    }
  });

  return result;
}

function findMissingKeys(msData, viData) {
  const missing = {};
  Object.keys(viData).forEach((key) => {
    const viVal = viData[key];
    const hasMs = Object.prototype.hasOwnProperty.call(msData, key);

    const msVal = msData[key];

    if (!hasMs) {
      // ms.json thiếu -> thêm vào danh sách thiếu
      missing[key] = viVal;
      return;
    }

    // Nếu là object, kiểm tra đệ quy
    if (isPlainObject(viVal) && isPlainObject(msVal)) {
      const childMissing = findMissingKeys(msVal, viVal);
      if (Object.keys(childMissing).length > 0) {
        missing[key] = childMissing;
      }
    }
    // Nếu là array hoặc primitive thì không cần kiểm tra sâu hơn
  });

  return missing;
}

function main() {
  const dir = __dirname;
  const msPath = path.join(dir, '../src/lib/localization/ms.json');
  const viPath = path.join(dir, '../src/lib/localization/vi.json');
  const outPath = path.join(dir, 'mschecked.json');
  const missingPath = path.join(dir, 'missing.json');

  if (!fs.existsSync(msPath) || !fs.existsSync(viPath)) {
    console.error('Vui lòng đảm bảo ms.json và vi.json đều có mặt trong cùng thư mục.');
    process.exit(1);
  }

  const msData = JSON.parse(fs.readFileSync(msPath, 'utf8'));
  const viData = JSON.parse(fs.readFileSync(viPath, 'utf8'));

  const merged = deepMerge(msData, viData);

  fs.writeFileSync(outPath, JSON.stringify(merged, null, 2), 'utf8');

  const missing = findMissingKeys(msData, viData);
  fs.writeFileSync(missingPath, JSON.stringify(missing, null, 2), 'utf8');
}

main();
