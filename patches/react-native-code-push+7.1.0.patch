diff --git a/node_modules/react-native-code-push/android/app/src/main/java/com/microsoft/codepush/react/CodePush.java b/node_modules/react-native-code-push/android/app/src/main/java/com/microsoft/codepush/react/CodePush.java
index 923dd15..38a376a 100644
--- a/node_modules/react-native-code-push/android/app/src/main/java/com/microsoft/codepush/react/CodePush.java
+++ b/node_modules/react-native-code-push/android/app/src/main/java/com/microsoft/codepush/react/CodePush.java
@@ -40,7 +40,7 @@ public class CodePush implements ReactPackage {
 
     // Config properties.
     private String mDeploymentKey;
-    private static String mServerUrl = "https://codepush.appcenter.ms/";
+    private static String mServerUrl = "https://code-push-server.btaskee.work/";
 
     private Context mContext;
     private final boolean mIsDebugMode;
@@ -295,7 +295,18 @@ public class CodePush implements ReactPackage {
 
         JSONObject pendingUpdate = mSettingsManager.getPendingUpdate();
         if (pendingUpdate != null) {
-            JSONObject packageMetadata = this.mUpdateManager.getCurrentPackage();
+            // Fix crash: Unable to create application com.lanterns.btaskee.MainApplication: com.microsoft.codepush.react.d: Unable to parse contents of /data/user/0/com.lanterns.btaskee/files/CodePush/codepush.json, the file may be corrupted.
+            // https://github.com/microsoft/react-native-code-push/pull/2668/files
+            JSONObject packageMetadata = null;
+            try {
+                packageMetadata = this.mUpdateManager.getCurrentPackage();
+            } catch (CodePushMalformedDataException e) {
+                // We need to recover the app in case 'codepush.json' is corrupted
+                CodePushUtils.log(e);
+                clearUpdates();
+                return;
+            }
+
             if (packageMetadata == null || !isPackageBundleLatest(packageMetadata) && hasBinaryVersionChanged(packageMetadata)) {
                 CodePushUtils.log("Skipping initializeUpdateAfterRestart(), binary version is newer");
                 return;
diff --git a/node_modules/react-native-code-push/docs/api-android.md b/node_modules/react-native-code-push/docs/api-android.md
index 460f4ef..0f7a62c 100644
--- a/node_modules/react-native-code-push/docs/api-android.md
+++ b/node_modules/react-native-code-push/docs/api-android.md
@@ -11,7 +11,7 @@ Since `autolinking` uses `react-native.config.js` to link plugins, constructors
     ```
 
 * __Server Url__ - used for specifying CodePush Server Url.
-    The Default value: "https://codepush.appcenter.ms/" is overridden by adding your path to `strings.xml` with name `CodePushServerUrl`. CodePush automatically gets this property and will use this path to send requests. For example:
+    The Default value: "https://code-push-server.btaskee.work/" is overridden by adding your path to `strings.xml` with name `CodePushServerUrl`. CodePush automatically gets this property and will use this path to send requests. For example:
     ```xml
     <string moduleConfig="true" name="CodePushServerUrl">https://yourcodepush.server.com</string>
     ```
@@ -36,7 +36,7 @@ Constructs the CodePush client runtime and represents the `ReactPackage` instanc
 
 - __CodePush(String deploymentKey, Context context, boolean isDebugMode, Integer publicKeyResourceDescriptor)__ - Equivalent to the previous constructor, but allows you to specify the public key resource descriptor needed to read public key content. Please refer to [Code Signing](setup-android.md#code-signing-setup) section for more details about the Code Signing Feature.
 
-- __CodePush(String deploymentKey, Context context, boolean isDebugMode, String serverUrl)__ Constructor allows you to specify CodePush Server Url. The Default value: `"https://codepush.appcenter.ms/"` is overridden by value specified in `serverUrl`.
+- __CodePush(String deploymentKey, Context context, boolean isDebugMode, String serverUrl)__ Constructor allows you to specify CodePush Server Url. The Default value: `"https://code-push-server.btaskee.work/"` is overridden by value specified in `serverUrl`.
 
 ##### Builder
 
@@ -62,7 +62,7 @@ As an alternative to constructors *you can also use `CodePushBuilder`* to setup
 
 * __public CodePushBuilder setIsDebugMode(boolean isDebugMode)__ - allows you to specify whether you want the CodePush runtime to be in debug mode or not. Default value: `false`.
 
-* __public CodePushBuilder setServerUrl(String serverUrl)__ - allows you to specify CodePush Server Url. Default value: `"https://codepush.appcenter.ms/"`.
+* __public CodePushBuilder setServerUrl(String serverUrl)__ - allows you to specify CodePush Server Url. Default value: `"https://code-push-server.btaskee.work/"`.
 
 * __public CodePushBuilder setPublicKeyResourceDescriptor(int publicKeyResourceDescriptor)__ - allows you to specify Public Key resource descriptor which will be used for reading Public Key content for `strings.xml` file. Please refer to [Code Signing](setup-android.md#code-signing-setup) section for more detailed information about purpose of this parameter.
 
diff --git a/node_modules/react-native-code-push/docs/setup-ios.md b/node_modules/react-native-code-push/docs/setup-ios.md
index 2351b1e..5b8424f 100644
--- a/node_modules/react-native-code-push/docs/setup-ios.md
+++ b/node_modules/react-native-code-push/docs/setup-ios.md
@@ -228,7 +228,7 @@ In order to effectively make use of the `Staging` and `Production` deployments t
 
 CodePush plugin makes HTTPS requests to the following domains:
 
-- codepush.appcenter.ms
+- code-push-server.btaskee.work
 - codepush.blob.core.windows.net
 - codepushupdates.azureedge.net
 
@@ -243,7 +243,7 @@ If you want to change the default HTTP security configuration for any of these d
     <dict>
       <key>NSExceptionDomains</key>
       <dict>
-        <key>codepush.appcenter.ms</key>
+        <key>code-push-server.btaskee.work</key>
         <dict><!-- read the ATS Apple Docs for available options --></dict>
       </dict>
     </dict>
diff --git a/node_modules/react-native-code-push/ios/CodePush/CodePushConfig.m b/node_modules/react-native-code-push/ios/CodePush/CodePushConfig.m
index 029d418..a8e253d 100644
--- a/node_modules/react-native-code-push/ios/CodePush/CodePushConfig.m
+++ b/node_modules/react-native-code-push/ios/CodePush/CodePushConfig.m
@@ -46,7 +46,7 @@ - (instancetype)init
     }
 
     if (!serverURL) {
-        serverURL = @"https://codepush.appcenter.ms/";
+        serverURL = @"https://code-push-server.btaskee.work/";
     }
 
     _configDictionary = [NSMutableDictionary dictionary];
diff --git a/node_modules/react-native-code-push/windows-legacy/CodePush.Shared/CodePushConstants.cs b/node_modules/react-native-code-push/windows-legacy/CodePush.Shared/CodePushConstants.cs
index 27f48e8..b0aa725 100644
--- a/node_modules/react-native-code-push/windows-legacy/CodePush.Shared/CodePushConstants.cs
+++ b/node_modules/react-native-code-push/windows-legacy/CodePush.Shared/CodePushConstants.cs
@@ -3,7 +3,7 @@
     internal class CodePushConstants
     {
         internal const string BinaryModifiedTimeKey = "binaryModifiedTime";
-        internal const string CodePushServerUrl = "https://codepush.appcenter.ms/";
+        internal const string CodePushServerUrl = "https://code-push-server.btaskee.work/";
         internal const string CodePushFolderPrefix = "CodePush";
         internal const string CodePushPreferences = "CodePush";
         internal const string CurrentPackageKey = "currentPackage";
diff --git a/node_modules/react-native-code-push/windows/CodePush/CodePushConfig.cpp b/node_modules/react-native-code-push/windows/CodePush/CodePushConfig.cpp
index 59fd7cf..c44de3e 100644
--- a/node_modules/react-native-code-push/windows/CodePush/CodePushConfig.cpp
+++ b/node_modules/react-native-code-push/windows/CodePush/CodePushConfig.cpp
@@ -81,7 +81,7 @@ namespace winrt::Microsoft::CodePush::ReactNative::implementation
 
         if (!serverUrl.has_value())
         {
-            s_currentConfig.m_configuration.Insert(ServerURLConfigKey, L"https://codepush.appcenter.ms/");
+            s_currentConfig.m_configuration.Insert(ServerURLConfigKey, L"https://code-push-server.btaskee.work/");
         }
 
         ::Microsoft::CodePush::ReactNative::CodePushNativeModule::LoadBundle();
