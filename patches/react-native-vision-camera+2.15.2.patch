diff --git a/node_modules/react-native-vision-camera/android/build.gradle b/node_modules/react-native-vision-camera/android/build.gradle
index 0b623d5..66e4aef 100644
--- a/node_modules/react-native-vision-camera/android/build.gradle
+++ b/node_modules/react-native-vision-camera/android/build.gradle
@@ -327,7 +327,7 @@ if (ENABLE_FRAME_PROCESSORS) {
 
   task downloadBoost(dependsOn: createNativeDepsDirectories, type: Download) {
     def transformedVersion = BOOST_VERSION.replace("_", ".")
-    def srcUrl = "https://boostorg.jfrog.io/artifactory/main/release/${transformedVersion}/source/boost_${BOOST_VERSION}.tar.gz"
+    def srcUrl = "https://archives.boost.io/release/${transformedVersion}/source/boost_${BOOST_VERSION}.tar.gz"
     if (REACT_NATIVE_VERSION < 69) {
       srcUrl = "https://github.com/react-native-community/boost-for-react-native/releases/download/v${transformedVersion}-0/boost_${BOOST_VERSION}.tar.gz"
     }
diff --git a/node_modules/react-native-vision-camera/ios/Parsers/AVCaptureColorSpace+descriptor.swift b/node_modules/react-native-vision-camera/ios/Parsers/AVCaptureColorSpace+descriptor.swift
index 13a403b..48fa031 100644
--- a/node_modules/react-native-vision-camera/ios/Parsers/AVCaptureColorSpace+descriptor.swift
+++ b/node_modules/react-native-vision-camera/ios/Parsers/AVCaptureColorSpace+descriptor.swift
@@ -38,7 +38,7 @@ extension AVCaptureColorSpace {
     case .sRGB:
       return "srgb"
     default:
-      fatalError("AVCaptureDevice.Position has unknown state.")
+      return "unknown"
     }
   }
 }
