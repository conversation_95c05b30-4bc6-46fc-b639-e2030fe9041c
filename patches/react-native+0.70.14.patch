diff --git a/node_modules/react-native/scripts/.packager.env b/node_modules/react-native/scripts/.packager.env
new file mode 100644
index 0000000..361f5fb
--- /dev/null
+++ b/node_modules/react-native/scripts/.packager.env
@@ -0,0 +1 @@
+export RCT_METRO_PORT=8081
diff --git a/node_modules/react-native/third-party-podspecs/boost.podspec b/node_modules/react-native/third-party-podspecs/boost.podspec
index 2f1fcc4..4aedb49 100644
--- a/node_modules/react-native/third-party-podspecs/boost.podspec
+++ b/node_modules/react-native/third-party-podspecs/boost.podspec
@@ -10,7 +10,8 @@ Pod::Spec.new do |spec|
   spec.homepage = 'http://www.boost.org'
   spec.summary = 'Boost provides free peer-reviewed portable C++ source libraries.'
   spec.authors = '<PERSON>'
-  spec.source = { :http => 'https://boostorg.jfrog.io/artifactory/main/release/1.76.0/source/boost_1_76_0.tar.bz2',
+  # fix: https://github.com/facebook/react-native/issues/42110
+  spec.source = { :http => 'https://archives.boost.io/release/1.76.0/source/boost_1_76_0.tar.bz2',
                   :sha256 => '****************************************************************' }
 
   # Pinning to the same version as React.podspec.
