package com.lanterns.btaskee;

import android.app.Application;
import android.content.Context;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.config.ReactFeatureFlags;
import com.facebook.soloader.SoLoader;
import com.lanterns.btaskee.newarchitecture.MainApplicationReactNativeHost;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import com.brentvatne.react.ReactVideoPackage;

// code push
import com.microsoft.codepush.react.CodePush;

// zalo
import com.lanterns.btaskee.zpmodule.PayZaloBridge;
import vn.zalopay.sdk.Environment;
import vn.zalopay.sdk.ZaloPaySDK;

// CleverTap imports
import com.clevertap.android.sdk.ActivityLifecycleCallback;
import com.clevertap.react.CleverTapPackage;
import com.clevertap.android.sdk.CleverTapAPI;
import com.clevertap.android.sdk.pushnotification.CTPushNotificationListener;
import com.clevertap.android.geofence.CTGeofenceAPI;
import com.clevertap.android.geofence.CTGeofenceSettings;
import com.clevertap.android.geofence.Logger;
import com.clevertap.android.geofence.interfaces.CTGeofenceEventsListener;
import com.clevertap.android.geofence.interfaces.CTLocationUpdatesListener;
import android.location.Location;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.clevertap.android.sdk.interfaces.OnInitCleverTapIDListener;

import java.util.Objects;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import javax.annotation.Nullable;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import android.os.Handler;
import android.os.Looper;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.modules.core.DeviceEventManagerModule;

// fix crash android 14: https://github.com/facebook/react-native/issues/41288#issuecomment-2291833031
import android.content.BroadcastReceiver;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
public class MainApplication extends Application implements ReactApplication, CTPushNotificationListener {

  private final ReactNativeHost mReactNativeHost =
      new ReactNativeHost(this) {

        // code push
        @Override
        protected String getJSBundleFile() {
            return CodePush.getJSBundleFile();
        }

        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          // Packages that cannot be autolinked yet can be added manually here, for example:
          // packages.add(new MyReactNativePackage());

          //Video
           packages.add(new ReactVideoPackage());

          // Zalo
          packages.add(new PayZaloBridge());
          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }
      };

  private final ReactNativeHost mNewArchitectureNativeHost =
      new MainApplicationReactNativeHost(this);

  @Override
  public ReactNativeHost getReactNativeHost() {
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      return mNewArchitectureNativeHost;
    } else {
      return mReactNativeHost;
    }
  }

  // fix crash android 14: https://github.com/facebook/react-native/issues/41288#issuecomment-2291833031
  @Override
  public Intent registerReceiver(@Nullable BroadcastReceiver receiver, IntentFilter filter) {
    if (Build.VERSION.SDK_INT >= 34 && getApplicationInfo().targetSdkVersion >= 34) {
      return super.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED);
    } else {
      return super.registerReceiver(receiver, filter);
    }
  }
  @Override
  public void onCreate() {
    // Register the CleverTap ActivityLifecycleCallback; before calling super
    ActivityLifecycleCallback.register(this);

    super.onCreate();

    // If you opted-in for the New Architecture, we enable the TurboModule system
    ReactFeatureFlags.useTurboModules = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
    SoLoader.init(this, /* native exopackage */ false);
    initializeFlipper(this, getReactNativeHost().getReactInstanceManager());

    // Zalo Pay
    ZaloPaySDK.init(Integer.parseInt(BuildConfig.ZALO_APP_ID), Environment.SANDBOX);

    // CleverTap
    CleverTapAPI clevertapDefaultInstance = CleverTapAPI.getDefaultInstance(getApplicationContext());
    clevertapDefaultInstance.setDebugLevel(CleverTapAPI.LogLevel.VERBOSE);
    //clevertapDefaultInstance.enablePersonalization();
    clevertapDefaultInstance.setCTPushNotificationListener(this);

    clevertapDefaultInstance.getCleverTapID(new OnInitCleverTapIDListener() {
      @Override
      public void onInitCleverTapID(final String CTID) {
          // Callback on main thread
          FirebaseAnalytics mFirebaseAnalytics;
          mFirebaseAnalytics = FirebaseAnalytics.getInstance(getApplicationContext());
          mFirebaseAnalytics.setUserProperty("ct_objectId", Objects.requireNonNull(CTID));
      }
    });

    CTGeofenceSettings ctGeofenceSettings = new CTGeofenceSettings.Builder()
        .setLogLevel(Logger.VERBOSE)//Log Level
        .setLocationAccuracy(CTGeofenceSettings.ACCURACY_HIGH)//byte value for Location Accuracy
        .setLocationFetchMode(CTGeofenceSettings.FETCH_CURRENT_LOCATION_PERIODIC)//byte value for Fetch Mode
        .build();

    CTGeofenceAPI.getInstance(getApplicationContext()).init(ctGeofenceSettings,clevertapDefaultInstance);

      try {
          CTGeofenceAPI.getInstance(getApplicationContext()).triggerLocation();
      } catch (IllegalStateException e){
          // thrown when this method is called before geofence SDK initialization
      }

      CTGeofenceAPI.getInstance(getApplicationContext())
              .setOnGeofenceApiInitializedListener(new CTGeofenceAPI.OnGeofenceApiInitializedListener() {
                  @Override
                  public void OnGeofenceApiInitialized() {
                      //App is notified on the main thread that CTGeofenceAPI is initialized
                  }
              });

      CTGeofenceAPI.getInstance(getApplicationContext())
              .setCtGeofenceEventsListener(new CTGeofenceEventsListener() {
                  @Override
                  public void onGeofenceEnteredEvent(JSONObject jsonObject) {
                      //Callback on the main thread when the user enters Geofence with info in jsonObject
                  }

                  @Override
                  public void onGeofenceExitedEvent(JSONObject jsonObject) {
                      //Callback on the main thread when user exits Geofence with info in jsonObject
                  }
              });
              
      CTGeofenceAPI.getInstance(getApplicationContext())
              .setCtLocationUpdatesListener(new CTLocationUpdatesListener() {
                  @Override
                  public void onLocationUpdates(Location location) {
                      //New location on the main thread as provided by the Android OS
                  }
              });

  }

  //Push Notification Clicked callback

    @Override

    public void onNotificationClickedPayloadReceived(HashMap<String, Object> payload) {

      final String CLEVERTAP_PUSH_NOTIFICATION_CLICKED = "CleverTapPushNotificationClicked";

        Handler handler = new Handler(Looper.getMainLooper());

        handler.post(new Runnable() {

            public void run() {

                // Construct and load our normal React JS code bundle

                final ReactInstanceManager mReactInstanceManager =

                        ((ReactApplication) getApplicationContext())

                                 .getReactNativeHost().getReactInstanceManager();

                ReactContext context = mReactInstanceManager.getCurrentReactContext();

                // If it’s constructed, send a notification

                if (context != null) {

                   sendEvent(CLEVERTAP_PUSH_NOTIFICATION_CLICKED

                        ,getWritableMapFromMap(payload),context);

                } else {

                    // Otherwise wait for construction, then send the notification

                    mReactInstanceManager.addReactInstanceEventListener(

                      new ReactInstanceManager.ReactInstanceEventListener() {

                        public void onReactContextInitialized(ReactContext context) {

                            sendEvent(CLEVERTAP_PUSH_NOTIFICATION_CLICKED

                                  ,getWritableMapFromMap(payload),context);

                            mReactInstanceManager.removeReactInstanceEventListener(this);

                        }

                    });

                    if (!mReactInstanceManager.hasStartedCreatingInitialContext()) {

                        // Construct it in the background

                        mReactInstanceManager.createReactContextInBackground();

                    }
                    
                }

            }

        });

    }

    private void sendEvent(String eventName, Object params,ReactContext context) {

      try {

        context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)

                .emit(eventName, params);

      } catch (Throwable t) {

      }

    }

  private WritableMap getWritableMapFromMap(Map<String, ? extends Object> var1) {

    JSONObject extras = var1 != null ? new JSONObject(var1) : new JSONObject();

    WritableMap extrasParams = Arguments.createMap();

    Iterator extrasKeys = extras.keys();

    while (extrasKeys.hasNext()) {

      String key = null;

      String value = null;

      try {

        key = extrasKeys.next().toString();

        value = extras.get(key).toString();

      } catch (Throwable t) {

      }

      if (key != null && value != null) {

        extrasParams.putString(key, value);

      }

    }

    return extrasParams;

  }

  /**
   * Loads Flipper in React Native templates. Call this in the onCreate method with something like
   * initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
   *
   * @param context
   * @param reactInstanceManager
   */
  private static void initializeFlipper(
      Context context, ReactInstanceManager reactInstanceManager) {
    if (BuildConfig.DEBUG) {
      try {
        /*
         We use reflection here to pick up the class that initializes Flipper,
        since Flipper library is not available in release mode
        */
        Class<?> aClass = Class.forName("com.lanterns.btaskee.ReactNativeFlipper");
        aClass
            .getMethod("initializeFlipper", Context.class, ReactInstanceManager.class)
            .invoke(null, context, reactInstanceManager);
      } catch (ClassNotFoundException e) {
        e.printStackTrace();
      } catch (NoSuchMethodException e) {
        e.printStackTrace();
      } catch (IllegalAccessException e) {
        e.printStackTrace();
      } catch (InvocationTargetException e) {
        e.printStackTrace();
      }
    }
  }
}
