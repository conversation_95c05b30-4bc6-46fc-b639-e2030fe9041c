<manifest
xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools"
package="com.lanterns.btaskee">
    <uses-feature android:name="android.hardware.audio.output" />
    <uses-feature android:name="android.hardware.microphone" />


    <!-- Permission -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <!-- permisstion for anroid 11 above -->

    <queries>
      <intent>
        <action android:name="android.intent.action.SENDTO" />
        <data android:scheme="mailto" />
        <category android:name="android.intent.category.DEFAULT" />
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="tel"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="http" android:host="*"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" android:host="*"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" android:host="api.whatsapp.com"/>
      </intent>
       <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" android:host="btaskee.com"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="zalopay"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="zalo"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="zalopay.api.v2"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="comgooglemaps"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="maps"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="fb"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="youtu.be"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="vnd.youtube"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="instagram"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="line"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="tiktok"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="shopeevn"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" android:host="app.shopeepay.vn"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="momo"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" android:host="payment.momo.vn"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" android:host="gopay.co.id"/>
      </intent>
      <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" android:host="m.dana.id"/>
      </intent>
    </queries>

    <application
      android:requestLegacyExternalStorage="true"
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:exported="true"
      android:largeHeap="true"
      android:networkSecurityConfig="@xml/network_security_config">

    <!-- notification -->
    <meta-data
      android:name="com.google.firebase.messaging.default_notification_channel_id"
      android:value="@string/default_notification_channel_id"
      tools:replace="android:value" />

    <meta-data
      android:name="com.google.firebase.messaging.default_notification_color"
      android:resource="@color/primary_color"
      tools:replace="android:resource" />

    <meta-data
     android:name="com.google.firebase.messaging.default_notification_icon"
     android:resource="@mipmap/ic_notification" />

    <!-- End notification -->

      <!-- Google map -->
      <meta-data
        android:name="com.google.android.geo.API_KEY"
        android:value="@string/GOOLE_MAPS_API_KEY_ANDROID"/>

      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:screenOrientation="unspecified"
        android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
        android:launchMode="singleTop"
        android:windowSoftInputMode="adjustResize"
        android:exported="true"
        >

         <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
            <action android:name="android.intent.action.DOWNLOAD_COMPLETE"/>
        </intent-filter>
        <!-- Dynamic Links -->
        <intent-filter android:autoVerify="true">
          <action android:name="android.intent.action.VIEW"/>
          <category android:name="android.intent.category.DEFAULT"/>
          <category android:name="android.intent.category.BROWSABLE"/>
          <data android:host="@string/DEEPLINK_PREFIX" android:scheme="http"/>
          <data android:host="@string/DEEPLINK_PREFIX" android:scheme="https"/>
          <data android:host="" android:scheme="btaskee"/>
        </intent-filter>

        <intent-filter android:autoVerify="true">
          <action android:name="android.intent.action.VIEW"/>
          <category android:name="android.intent.category.DEFAULT"/>
          <category android:name="android.intent.category.BROWSABLE"/>
          <data android:host="@string/DEEPLINK_BTASKEE" android:scheme="http"/>
          <data android:host="@string/DEEPLINK_BTASKEE" android:scheme="https"/>
        </intent-filter>
      </activity>

      <service
        android:name="app.notifee.core.ForegroundService"
        tools:replace="android:foregroundServiceType"
        android:stopWithTask="true"
        android:foregroundServiceType="microphone" />

      <!-- facebook -->
      <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
      <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
      <meta-data android:name="com.facebook.sdk.ApplicationName" android:value="@string/app_name"/>
      <meta-data android:name="com.facebook.sdk.AutoLogAppEventsEnabled" android:value="true"/>
      <meta-data android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled" android:value="false"/>
      <provider android:name="com.facebook.FacebookContentProvider" android:authorities="com.facebook.app.FacebookContentProvider355198514515820" android:exported="true" />

      <activity android:name="com.facebook.FacebookActivity" android:configChanges= "keyboard|keyboardHidden|screenLayout|screenSize|orientation" android:label="@string/app_name" android:exported="true"/>
      <activity android:name="com.facebook.CustomTabActivity" android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="@string/fb_login_protocol_scheme" />
      </intent-filter>
      </activity>
      <!-- end facebook -->

      <!-- CleverTap -->
      <service
          android:name="com.clevertap.android.sdk.pushnotification.fcm.FcmMessageListenerService" android:exported="true">
          <intent-filter>
              <action android:name="com.google.firebase.MESSAGING_EVENT"/>
          </intent-filter>
      </service>

      <!-- <service
        android:name=".java.MyFirebaseMessagingService"
        android:exported="false">
        <intent-filter>
            <action android:name="com.google.firebase.MESSAGING_EVENT" />
        </intent-filter>
      </service> -->

      <meta-data
        android:name="CLEVERTAP_ACCOUNT_ID"
        android:value="@string/CLEVERTAP_ACCOUNT_ID"/>
      <meta-data
          android:name="CLEVERTAP_TOKEN"
          android:value="@string/CLEVERTAP_TOKEN"/>

      <meta-data
        android:name="FCM_SENDER_ID"
        android:value="id:@string/FCM_SENDER_ID"/>

      <meta-data
        android:name="CLEVERTAP_BACKGROUND_SYNC"
        android:value="1"/>

      <meta-data
        android:name="CLEVERTAP_USE_GOOGLE_AD_ID"
        android:value="1"/>

      <meta-data
        android:name="CLEVERTAP_INAPP_EXCLUDE"
        android:value=".MyReactActivity" />

      <!-- End CleverTap -->
      <meta-data
        android:name="google_analytics_adid_collection_enabled"
        android:value="false"
        tools:replace="android:value" />

    </application>
</manifest>
