<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:textColor">#000000</item>
        <item name="android:windowIsTranslucent">true</item>
        <!-- Fix crash  newDrawable(android.content.res.Resources)' on a null object reference-->
        <item name="android:editTextBackground">@android:color/transparent</item>
        <item name="android:navigationBarColor">#FFFFFF</item>
    </style>
    <style name="SplashScreenTheme" parent="SplashScreen_SplashTheme">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <!--Required Api 21 above-->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="DatePickerTheme" parent="DatePickerBaseTheme">
        <item name="android:textSize">22sp</item>
        <item name="android:fontFamily">Inter-Bold</item>
    </style>

</resources>
