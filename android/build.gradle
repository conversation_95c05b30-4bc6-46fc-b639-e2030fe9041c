// Top-level build file where you can add configuration options common to all sub-projects/modules.
def REACT_NATIVE_VERSION = new File(['node', '--print',"JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())

buildscript {
    ext {
        buildToolsVersion = "36.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 35
        googlePlayServicesAuthVersion = "16.0.1" // google login
        ndkVersion = "25.1.8937393"
    }
    repositories {
        google()
        mavenCentral()
        maven {
          url = uri("https://storage.googleapis.com/r8-releases/raw")
        }
    }
    dependencies {
        classpath("com.android.tools:r8:8.1.44")
        classpath("com.android.tools.build:gradle:7.2.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("de.undercouch:gradle-download-task:5.0.1")

        classpath 'com.google.gms:google-services:4.3.14' // google login
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.2'
        classpath "com.google.firebase:firebase-messaging:23.1.1"
        classpath "com.google.firebase:firebase-iid:16.0.0"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }

         maven {
            url("$rootDir/../node_modules/react-native-vnpay-merchant/android/repo")
        }

        google()
        maven { url 'https://www.jitpack.io' }
        maven {
            url "https://maven.google.com"
        }
        configurations.all {
            resolutionStrategy {
                // Remove this override in 0.65+, as a proper fix is included in react-native itself.
                force "com.facebook.react:react-native:" + REACT_NATIVE_VERSION
            }
        }
    }
}

//Fix compile not found in react-native-vnpay-merchant
subprojects { subproject ->
        if(subproject['name'] == 'react-native-vnpay-merchant'){
            subproject.configurations { compile { } }
        }
}
