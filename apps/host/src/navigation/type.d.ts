import { RouteName } from '@btaskee/design-system';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type RootStackParamList = {
  [RouteName.IntroNavigator]: any;
  [RouteName.MainNavigator]: any;
};

export type IntroStackParamList = {
  [RouteName.AppIntro]: any;
  [RouteName.Landing]: any;
};

type TabsStackParamList = {
  [RouteName.TabHome]: any;
  [RouteName.TabActivity]: any;
  [RouteName.TabCommunity]: any;
  [RouteName.TabNotification]: any;
  [RouteName.TabAccount]: any;
};

type MainStackParamList = {
  [RouteName.TabNavigator]: any;
  [RouteName.CleaningService]: any;
  [RouteName.DeepCleaningService]: any;
  [RouteName.AirConditionerService]: any;
  [RouteName.ElderlyCareService]: any;
  [RouteName.ElderlyCareSubscriptionService]: any;
  [RouteName.PatientCareService]: any;
  [RouteName.PatientCareSubscriptionService]: any;
  [RouteName.ChildCareService]: any;
  [RouteName.ChildCareSubscriptionService]: any;
  [RouteName.VoiceChat]: any;
  [RouteName.Auth]: any;
  [RouteName.CleaningSubscriptionService]: any;
  [RouteName.OfficeCleaningService]: any;
  [RouteName.OfficeCleaningSubscriptionService]: any;
  [RouteName.Payment]: any;
  [RouteName.Setting]: any;
  [RouteName.HelpCenter]: any;
  [RouteName.FavTasker]: any;
  [RouteName.BlacklistTasker]: any;
  [RouteName.ReferralCode]: any;
  [RouteName.ComboVoucher]: any;
  [RouteName.BReward]: any;
  [RouteName.BPay]: any;
  [RouteName.MyGift]: any;
  [RouteName.TaskManagement]: any;
  [RouteName.ExploreBTaskee]: any;
  [RouteName.IntroExploreBTaskee]: any;
  [RouteName.WaterHeaterService]: any;
  [RouteName.WashingMachineService]: any;
  [RouteName.DisinfectionService]: any;
  [RouteName.HomeCookingService]: any;
  [RouteName.OfficeCarpetCleaningService]: any;
  [RouteName.IndustrialCleaningService]: any;
  [RouteName.SofaCleaningService]: any;
  [RouteName.MassageService]: any;
  [RouteName.SofaCleaningThailandService]: any;
  [RouteName.IroningService]: any;
  [RouteName.HomeMovingService]: any;
  [RouteName.HousekeepingService]: any;
};

export type ParamsNavigationList = RootStackParamList &
  IntroStackParamList &
  MainStackParamList;

export type NavigationProps = NativeStackNavigationProp<ParamsNavigationList>;
