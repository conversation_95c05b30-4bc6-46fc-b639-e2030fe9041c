import React from 'react';
import { RouteName } from '@btaskee/design-system';
import { useRoute } from '@react-navigation/native';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const Housekeeping = React.lazy(() => import('housekeeping/MainNavigator'));

export function HousekeepingScreen(): React.JSX.Element {
  const route = useRoute();
  return (
    <ErrorBoundary name={RouteName.HousekeepingService}>
      <React.Suspense fallback={<LoadingMiniApp />}>
        <Housekeeping {...route.params} />
      </React.Suspense>
    </ErrorBoundary>
  );
}
