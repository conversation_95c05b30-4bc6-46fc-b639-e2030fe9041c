/**
 * @Filename: IntroService/index.tsx
 * @Description: Housekeeping service introduction screen with swiper - Migrated and modernized from legacy
 * @CreatedAt: 2025-01-15
 * @Author: AI Assistant
 **/

import React, { createRef, forwardRef, useCallback, useState } from 'react';
import type { ImageProps } from 'react-native';
import { ImageBackground } from 'react-native';
import Swiper from 'react-native-swiper';
import {
  BlockView,
  ColorsV2,
  CText,
  PrimaryButton,
  TouchableOpacity,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useAppNavigation, useI18n } from '@hooks';
import {
  imgIntroHouseKeeping1,
  imgIntroHouseKeeping2,
  imgIntroHouseKeeping3,
} from '@images';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

/**
 * Props for SwiperContainer component
 */
interface ISwiperContainerProps {
  index?: number;
  setIndexIntroPage: (index: number) => void;
}

/**
 * Renders the dot indicator for swiper pagination
 * Purpose: Provides visual indicator for current slide position
 * @returns {React.JSX.Element} React component displaying pagination dot
 */
const Dot = (): React.JSX.Element => <BlockView style={styles.dot} />;

/**
 * Renders individual slider item with background image and text overlay
 * Purpose: Displays service introduction content with image background and descriptive text
 * @param image - Background image source for the slide
 * @returns {React.JSX.Element} React component displaying slide content
 */
const SliderItem = ({
  image,
}: {
  image: ImageProps['source'];
}): React.JSX.Element => {
  const { t } = useI18n();

  return (
    <BlockView flex>
      <ImageBackground
        source={image}
        style={styles.bgImage}
      >
        <BlockView style={styles.wrapDescription}>
          <CText style={styles.txtTitle}>
            {(
              t('TITLE_INTRO_HOSTEL_1') || 'Housekeeping Service'
            ).toUpperCase()}
          </CText>
          <CText style={styles.txtDescription}>
            {t('DES_INTRO_HOSTEL_1') ||
              'Professional housekeeping service for your home'}
          </CText>
        </BlockView>
      </ImageBackground>
    </BlockView>
  );
};

/**
 * Swiper container component with navigation slides
 * Purpose: Manages swiper functionality and slide navigation for intro content
 * @param props - Component props including index and callback function
 * @param ref - Forwarded ref for swiper control
 * @returns {React.JSX.Element} React component containing swiper with intro slides
 */
const SwiperContainer = forwardRef<Swiper, ISwiperContainerProps>(
  (props, ref): React.JSX.Element => {
    return (
      <BlockView flex>
        <Swiper
          ref={ref}
          index={props.index}
          loop={false}
          autoplay={false}
          activeDot={<Dot />}
          paginationStyle={styles.dotStyle}
          onIndexChanged={props.setIndexIntroPage}
          pagingEnabled={true}
        >
          <SliderItem image={imgIntroHouseKeeping1} />
          <SliderItem image={imgIntroHouseKeeping2} />
          <SliderItem image={imgIntroHouseKeeping3} />
        </Swiper>
      </BlockView>
    );
  },
);

/**
 * Housekeeping service introduction screen component
 * Purpose: Displays swiper-based service introduction with navigation controls
 * @returns {React.JSX.Element} React component showing housekeeping service introduction
 */
export const IntroService = (): React.JSX.Element => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { setIsFirstOpen } = usePostTaskStore();
  const sliderRef = createRef<Swiper>();

  const [indexIntroPage, setIndexIntroPage] = useState<number>(0);

  /**
   * Handles completion of intro and navigation to service selection
   * Purpose: Marks intro as seen and navigates to service selection
   * @returns {Promise<void>} No return value, performs navigation action
   */
  const onCreateNewLocation = useCallback(async (): Promise<void> => {
    setIsFirstOpen();
    navigation.replace(RouteName.ChooseService);
  }, [setIsFirstOpen, navigation]);

  /**
   * Handles skip action to jump to last slide
   * Purpose: Allows user to skip intro slides and go directly to final slide
   * @returns {void} No return value, performs swiper navigation
   */
  const onSkip = useCallback((): void => {
    sliderRef?.current?.scrollBy(2, true);
  }, [sliderRef]);

  /**
   * Renders footer button based on current slide index
   * Purpose: Shows appropriate action button (skip or start) based on slide position
   * @returns {React.JSX.Element} React component displaying footer button
   */
  const renderButtonFooter = useCallback((): React.JSX.Element => {
    if (indexIntroPage === 2) {
      return (
        <BlockView style={styles.boxFooter}>
          <PrimaryButton
            testID="hkIntroBtnCreateHostel"
            title={t('CREATE_HOSTEL') || 'Get Started'}
            onPress={onCreateNewLocation}
          />
        </BlockView>
      );
    }

    return (
      <BlockView style={styles.boxFooterSkip}>
        <TouchableOpacity
          testID="hkIntroBtnSkip"
          onPress={onSkip}
        >
          <CText style={{ color: ColorsV2.neutral600 }}>
            {t('SKIP') || 'Skip'}
          </CText>
        </TouchableOpacity>
      </BlockView>
    );
  }, [indexIntroPage, t, onCreateNewLocation, onSkip]);

  return (
    <BlockView flex>
      <SwiperContainer
        ref={sliderRef}
        index={indexIntroPage}
        setIndexIntroPage={setIndexIntroPage}
      />
      {renderButtonFooter()}
    </BlockView>
  );
};
