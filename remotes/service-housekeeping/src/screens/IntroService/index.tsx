import React, { useCallback, useRef, useState } from 'react';
import { FlatList, ImageBackground, ImageProps } from 'react-native';
import {
  BlockView,
  ColorsV2,
  CText,
  <PERSON><PERSON><PERSON><PERSON>per,
  FlatList,
  PrimaryButton,
  TouchableOpacity,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useAppNavigation, useI18n } from '@hooks';
import {
  imgIntroHouseKeeping1,
  imgIntroHouseKeeping2,
  imgIntroHouseKeeping3,
} from '@images';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

/**
 * Interface for intro slide data
 */
interface IntroSlideData {
  id: string;
  image: ImageProps['source'];
}

/**
 * Props for IntroSlider component
 */
interface IntroSliderProps {
  data: IntroSlideData[];
  onIndexChange: (index: number) => void;
}

/**
 * Renders pagination dots for slide navigation
 * Purpose: Provides visual indicator for current slide position and total slides
 * @param totalSlides - Total number of slides
 * @param currentIndex - Current active slide index
 * @returns {React.JSX.Element} React component displaying pagination dots
 */
const PaginationDots = ({
  totalSlides,
  currentIndex,
}: {
  totalSlides: number;
  currentIndex: number;
}): React.JSX.Element => {
  return (
    <BlockView style={styles.paginationContainer}>
      {Array.from({ length: totalSlides }, (_, index) => (
        <BlockView
          key={index}
          style={[
            styles.dot,
            index === currentIndex ? styles.activeDot : styles.inactiveDot,
          ]}
        />
      ))}
    </BlockView>
  );
};

/**
 * Renders individual slider item with background image and text overlay
 * Purpose: Displays service introduction content with image background and descriptive text
 * @param image - Background image source for the slide
 * @returns {React.JSX.Element} React component displaying slide content
 */
const SliderItem = ({
  image,
}: {
  image: ImageProps['source'];
}): React.JSX.Element => {
  const { t } = useI18n();

  return (
    <BlockView flex>
      <ImageBackground
        source={image}
        style={styles.bgImage}
      >
        <BlockView style={styles.wrapDescription}>
          <CText style={styles.txtTitle}>
            {t('TITLE_INTRO_HOSTEL_1').toUpperCase()}
          </CText>
          <CText style={styles.txtDescription}>{t('DES_INTRO_HOSTEL_1')}</CText>
        </BlockView>
      </ImageBackground>
    </BlockView>
  );
};

/**
 * FlatList-based intro slider component
 * Purpose: Manages slide navigation using FlatList with pagination
 * @param data - Array of slide data with images
 * @param onIndexChange - Callback function when slide index changes
 * @returns {React.JSX.Element} React component containing FlatList with intro slides
 */
const IntroSlider = ({
  data,
  onIndexChange,
}: IntroSliderProps): React.JSX.Element => {
  const flatListRef = useRef<FlatList>(null);

  /**
   * Handles scroll end event to update current slide index
   * Purpose: Updates parent component with current slide index for pagination
   * @param event - Scroll event containing content offset
   * @returns {void} No return value, calls onIndexChange callback
   */
  const handleScrollEnd = useCallback(
    (event: any): void => {
      const contentOffset = event.nativeEvent.contentOffset;
      const viewSize = event.nativeEvent.layoutMeasurement;
      const pageNum = Math.floor(contentOffset.x / viewSize.width);
      onIndexChange?.(pageNum);
    },
    [onIndexChange],
  );

  /**
   * Renders individual slide item
   * Purpose: Displays slide content with image and text overlay
   * @param item - Slide data containing image source
   * @returns {React.JSX.Element} React component displaying slide content
   */
  const renderSlide = useCallback(
    ({ item }: { item: IntroSlideData }): React.JSX.Element => {
      return <SliderItem image={item.image} />;
    },
    [],
  );

  /**
   * Scrolls to specific slide index
   * Purpose: Programmatically navigate to a specific slide
   * @param index - Target slide index
   * @returns {void} No return value, performs scroll action
   */
  const scrollToIndex = useCallback((index: number): void => {
    flatListRef.current?.scrollToIndex({ index, animated: true });
  }, []);

  // Expose scrollToIndex method to parent component
  React.useImperativeHandle(flatListRef, () => ({
    scrollToIndex,
  }));

  return (
    <BlockView flex>
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={renderSlide}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScrollEnd}
        getItemLayout={(_, index) => ({
          length: DeviceHelper.WINDOW.WIDTH,
          offset: DeviceHelper.WINDOW.WIDTH * index,
          index,
        })}
      />
    </BlockView>
  );
};

/**
 * Housekeeping service introduction screen component
 * Purpose: Displays swiper-based service introduction with navigation controls
 * @returns {React.JSX.Element} React component showing housekeeping service introduction
 */
export const IntroService = (): React.JSX.Element => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { setIsFirstOpen } = usePostTaskStore();
  const sliderRef = useRef<any>(null);

  const [indexIntroPage, setIndexIntroPage] = useState<number>(0);

  // Slide data for FlatList
  const slideData: IntroSlideData[] = [
    { id: '1', image: imgIntroHouseKeeping1 },
    { id: '2', image: imgIntroHouseKeeping2 },
    { id: '3', image: imgIntroHouseKeeping3 },
  ];

  /**
   * Handles completion of intro and navigation to service selection
   * Purpose: Marks intro as seen and navigates to service selection
   * @returns {Promise<void>} No return value, performs navigation action
   */
  const onCreateNewLocation = useCallback(async (): Promise<void> => {
    setIsFirstOpen();
    navigation.replace(RouteName.ChooseService);
  }, [setIsFirstOpen, navigation]);

  /**
   * Handles skip action to jump to last slide
   * Purpose: Allows user to skip intro slides and go directly to final slide
   * @returns {void} No return value, performs FlatList navigation
   */
  const onSkip = useCallback((): void => {
    sliderRef?.current?.scrollToIndex(2);
  }, [sliderRef]);

  /**
   * Renders footer button based on current slide index
   * Purpose: Shows appropriate action button (skip or start) based on slide position
   * @returns {React.JSX.Element} React component displaying footer button
   */
  const renderButtonFooter = useCallback((): React.JSX.Element => {
    if (indexIntroPage === 2) {
      return (
        <BlockView style={styles.boxFooter}>
          <PrimaryButton
            testID="hkIntroBtnCreateHostel"
            title={t('CREATE_HOSTEL')}
            onPress={onCreateNewLocation}
          />
        </BlockView>
      );
    }

    return (
      <BlockView style={styles.boxFooterSkip}>
        <TouchableOpacity
          testID="hkIntroBtnSkip"
          onPress={onSkip}
        >
          <CText style={{ color: ColorsV2.neutral600 }}>{t('SKIP')}</CText>
        </TouchableOpacity>
      </BlockView>
    );
  }, [indexIntroPage, t, onCreateNewLocation, onSkip]);

  return (
    <BlockView flex>
      <IntroSlider
        data={slideData}
        onIndexChange={setIndexIntroPage}
      />
      <PaginationDots
        totalSlides={slideData.length}
        currentIndex={indexIntroPage}
      />
      {renderButtonFooter()}
    </BlockView>
  );
};
