/**
 * @Filename: IntroService/index.tsx
 * @Description: Housekeeping service introduction screen - Modernized for service-housekeeping microservice
 * @CreatedAt: 2023-11-15
 * @ModifiedAt: 2025-01-15
 * @Author: Duong <PERSON>c <PERSON> / AI Assistant
 **/

import React, { useCallback } from 'react';
import {
  bgHeader,
  BlockView,
  BottomView,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  IconImage,
  IconImageProps,
  Markdown,
  PrimaryButton,
  ScrollView,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useAppNavigation, useI18n } from '@hooks';
import {
  icIntroHousekeeping1,
  icIntroHousekeeping2,
  icIntroHousekeeping3,
  icIntroHousekeeping4,
  icIntroHousekeeping5,
  imgIntroHousekeeping,
} from '@images';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

/**
 * Props for TextItem component
 */
type TextItemProps = {
  /** Icon source for the text item */
  icon?: IconImageProps['source'];
  /** Text content to display */
  text?: string;
};

/**
 * Renders a text item with an icon and description
 * Purpose: Displays service feature information with visual icon and text content
 * @param icon - Icon source for the feature item
 * @param text - Text description for the feature item
 * @returns {JSX.Element} React component displaying icon and text in a row layout
 */
const TextItem = ({ icon, text }: TextItemProps): React.JSX.Element => {
  return (
    <BlockView row>
      <IconImage
        source={icon}
        size={32}
        style={styles.iconStyle}
      />
      <BlockView
        flex
        style={styles.boxText}
      >
        <Markdown
          text={text || ''}
          paragraphStyle={{ color: ColorsV2.neutral500 }}
        />
      </BlockView>
    </BlockView>
  );
};

/**
 * Housekeeping service introduction screen component
 * Purpose: Displays service introduction with features and navigation to service selection
 * @returns {React.JSX.Element} React component showing housekeeping service introduction
 */
export const IntroService = (): React.JSX.Element => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { isFirstOpen, setIsFirstOpen } = usePostTaskStore();

  // React.useEffect(() => {
  //   trackingServiceView({
  //     screenName: TrackingScreenNames.ServiceIntroduction,
  //     serviceName: SERVICES.HOUSEKEEPING,
  //     entryPoint: entryPointRoute || entryPoint,
  //   });
  // }, [entryPointRoute, entryPoint]);

  /**
   * Handles submit button press action
   * Purpose: Navigates to service selection or goes back based on first open state
   * @returns {Promise<void>} No return value, performs navigation action
   */
  const onSubmit = useCallback(async (): Promise<void> => {
    if (isFirstOpen) {
      setIsFirstOpen();
      navigation.replace(RouteName.ChooseService);
    } else {
      navigation.goBack();
    }
  }, [isFirstOpen, setIsFirstOpen, navigation]);

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <FastImage
          resizeMode="cover"
          source={bgHeader}
          style={styles.headerBackgroundStyle}
        />
        <BlockView style={styles.content}>
          <BlockView style={styles.wrap_image}>
            <FastImage
              resizeMode="cover"
              source={imgIntroHousekeeping}
              style={styles.headerBackgroundIntroStyle}
            />
          </BlockView>
          <ScrollView
            style={styles.wrap_note}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}
          >
            <CText
              bold
              size={FontSizes.SIZE_20}
              style={styles.txt_serviceName}
            >
              {t('INTRO_TITLE')}
            </CText>
            <CText style={styles.txtStyle}>{t('SUB_INTRO') || ''}</CText>
            <TextItem
              icon={icIntroHousekeeping1}
              text={t('SUB_INTRO_1') || ''}
            />
            <TextItem
              icon={icIntroHousekeeping2}
              text={t('SUB_INTRO_2') || ''}
            />
            <TextItem
              icon={icIntroHousekeeping3}
              text={t('SUB_INTRO_3') || ''}
            />
            <TextItem
              icon={icIntroHousekeeping4}
              text={t('SUB_INTRO_4') || ''}
            />
            <TextItem
              icon={icIntroHousekeeping5}
              text={t('SUB_INTRO_5') || ''}
            />
          </ScrollView>
        </BlockView>
      </BlockView>
      <BottomView margin={{ horizontal: Spacing.SPACE_16 }}>
        <PrimaryButton
          title={
            isFirstOpen
              ? t('INTRO_START_EXPERIENCE') || 'Start Experience'
              : t('BTN_BACK') || 'Back'
          }
          onPress={onSubmit}
        />
      </BottomView>
    </BlockView>
  );
};
