import { StyleSheet } from 'react-native';
import {
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ontSizes,
  Spacing,
} from '@btaskee/design-system';

const { WIDTH, HEIGHT } = DeviceHelper.WINDOW;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
  },
  bgImage: {
    width: '100%',
    height: '100%',
  },
  dotStyle: {
    position: 'absolute',
  },
  txtTitle: {
    fontWeight: '500',
    fontSize: FontSizes.SIZE_16,
    textAlign: 'center',
    color: ColorsV2.orange500,
    marginBottom: Spacing.SPACE_16,
  },
  txtDescription: {
    marginBottom: Spacing.SPACE_08,
    textAlign: 'center',
    color: ColorsV2.neutral800,
  },
  wrapDescription: {
    position: 'absolute',
    top: HEIGHT / 1.8,
    right: 0,
    left: 0,
    padding: Spacing.SPACE_16,
  },
  boxFooter: {
    height: WIDTH / 4,
    backgroundColor: ColorsV2.neutralWhite,
    padding: Spacing.SPACE_16,
    justifyContent: 'center',
  },
  boxFooterSkip: {
    height: WIDTH / 4,
    backgroundColor: ColorsV2.neutralWhite,
    padding: Spacing.SPACE_16,
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  paginationContainer: {
    position: 'absolute',
    bottom: WIDTH / 4 + Spacing.SPACE_16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: Spacing.SPACE_16,
    height: Spacing.SPACE_08,
    borderRadius: Spacing.SPACE_04,
    marginHorizontal: 3,
  },
  activeDot: {
    backgroundColor: ColorsV2.orange500,
  },
  inactiveDot: {
    backgroundColor: ColorsV2.neutral300,
  },
});
