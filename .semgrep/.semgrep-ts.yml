rules:
  # Tạm thời chưa check moment
  # - id: moment-deprecated
  #   pattern-either:
  #     - pattern: "import moment from 'moment'"
  #     - pattern: "import { ... } from 'moment'"
  #     - pattern: "import * as moment from 'moment'"
  #     - pattern: "import 'moment'"
  #     - pattern: "const moment = require('moment')"
  #     - pattern: "const { ... } = require('moment')"
  #   message: Moment là một dự án cũ đang trong chế độ bảo trì. H<PERSON>y sử dụng dayjs.
  #   languages:
  #     - typescript
  #     - javascript
  #   severity: ERROR
  #   metadata:
  #     category: best-practice
  #     technology:
  #       - moment
  #       - dayjs
  #     references:
  #       - https://momentjs.com/docs/#/-project-status/
  #       - https://day.js.org/
  #     license: Semgrep Rules License v1.0. For more details, visit
  #       semgrep.dev/legal/rules-license
  #   paths:
  #     exclude:
  #       - 'src/lib/helper/index.tsx'

  - id: useless-ternary
    pattern: |
      $CONDITION ? $ANS : $ANS
    message: <PERSON><PERSON> vẻ như dù điều kiện $CONDITION được đ<PERSON>h gi<PERSON> thế nào, bi<PERSON><PERSON> thức này vẫn trả về $ANS. Đây có thể là một lỗi do sao chép và dán.
    languages:
      - typescript
      - javascript
    metadata:
      category: correctness
      technology:
        - react
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
    severity: ERROR
    paths:
      exclude:
        - 'src/lib/helper/index.tsx'
        - 'e2e/**'
        - 'scripts/**'

  - id: no-inline-styles
    languages: [javascript, typescript]
    severity: ERROR
    message: "Không sử dụng inline styles. '$COMPONENT'"
    pattern-either:
      - pattern: <$COMPONENT style={{...}}>...</$COMPONENT>

  - id: detect-console-methods
    languages: [javascript, typescript]
    severity: ERROR
    pattern-either:
      - pattern: console.log(...)
      - pattern: console.warn(...)
      - pattern: console.error(...)
    message: 'Tránh sử dụng console.log, console.warn và console.error trong môi trường production.'
    paths:
      exclude:
        - 'src/libs/helper/log.helpers.ts'
        - 'scripts/**'
  - id: no-inline-function
    patterns:
      - pattern-either:
          # Bắt lỗi function inline không tham số
          - pattern: |
              <$COMPONENT $PROP={() => {...}} >...</$COMPONENT>
          # Bắt lỗi function inline không tham số
          - pattern: |
              <$COMPONENT $PROP={() => $BODY} >...</$COMPONENT>
          # Bắt lỗi function inline có một tham số
          - pattern: |
              <$COMPONENT $PROP={($ARG) => $BODY} >...</$COMPONENT>
          # Bắt lỗi function inline có một tham số với kiểu TypeScript
          - pattern: |
              <$COMPONENT $PROP={($ARG: $TYPE) => $BODY} >...</$COMPONENT>
          # Bắt lỗi function inline có destructuring trong tham số
          - pattern: |
              <$COMPONENT $PROP={({ ...$ARGS }: $TYPE) => $BODY} >...</$COMPONENT>
    message: "Không nên truyền function inline trực tiếp vào props. Hãy dùng useCallback hoặc khai báo function bên ngoài. '$COMPONENT'"
    languages: [typescript, javascript]
    severity: ERROR
  # Tạm thời chưa check đc case
  # const LUCKY_DRAW = 'LUCKY_DRAW';
  # if (data?.name === LUCKY_DRAW) {}

  # - id: detect-string-comparison
  #   pattern-either:
  #     - pattern: $VAR === '...'
  #     - pattern: $VAR == '...'
  #     - pattern: $VAR==='...'
  #     - pattern: $VAR=='...'
  #     - pattern: "'...' === $VAR"
  #     - pattern: "'...' == $VAR"
  #     - pattern: "'...'===$VAR"
  #     - pattern: "'...'== $VAR"
  #   languages:
  #     - javascript
  #     - typescript
  #   message: Tránh so sánh trực tiếp biến với giá trị chuỗi Hãy gán giá trị đó cho một biến hằng số có tên rõ ràng. '$VAR'
  #   severity: ERROR
  #   paths:
  #     exclude:
  #       - 'src/lib/helper/index.tsx'
  - id: detect-number-comparison
    patterns:
      - pattern-either:
          - pattern-regex: '\b[a-zA-Z_][a-zA-Z0-9_]*\s*(==|===)\s*[0-9]+(\.[0-9]+)?\b'
          - pattern-regex: '\b[0-9]+(\.[0-9]+)?\s*(==|===)\s*[a-zA-Z_][a-zA-Z0-9_]*\b'
      - pattern-not: '$VAR == $ANOTHER_VAR'
      - pattern-not: '$VAR === $ANOTHER_VAR'
    message: Tránh so sánh trực tiếp biến với giá trị số Hãy gán giá trị đó cho một biến hằng số có tên rõ ràng.
    languages:
      - javascript
      - typescript
    severity: ERROR
    paths:
      exclude:
        - 'src/lib/helper/index.tsx'
        - 'e2e/**'
  - id: detect-math-with-literal
    patterns:
      - pattern-either:
          - pattern: $VAR + $NUMBER
          - pattern: $NUMBER + $VAR
          - pattern: $VAR - $NUMBER
          - pattern: $NUMBER - $VAR
          - pattern: $VAR * $NUMBER
          - pattern: $NUMBER * $VAR
          - pattern: $VAR / $NUMBER
          - pattern: $NUMBER / $VAR
          - pattern: $VAR % $NUMBER
          - pattern: $NUMBER % $VAR
          - pattern: $VAR ** $NUMBER
          - pattern: $NUMBER ** $VAR
      - metavariable-regex:
          metavariable: $NUMBER
          regex: "^(0\\.[0-9]+|[3-9]\\.[0-9]+|[3-9]|[1-9][0-9]+)$"
      - pattern-not-inside: |
          const $X = $NUMBER;
    message: "Tránh sử dụng số trực tiếp số trong phép toán. Hãy khai báo biến trước.'$VAR'"
    languages:
      - javascript
      - typescript
    severity: ERROR
    paths:
      exclude:
        - 'src/lib/helper/index.tsx'
        - 'e2e/**'
        - 'src/lib/package/react-native-iphone-x-helper/index.js'
  - id: detect-invalid-variable-names
    patterns:
      - pattern-either:
          - pattern: const $VAR = ...
          - pattern: let $VAR = ...
      - metavariable-regex:
          metavariable: $VAR
          regex: '^.{1,3}$'
    message: "Tên biến '$VAR' không hợp lệ. Độ dài phải từ 3 ký tự."
    languages:
      - javascript
      - typescript
    severity: ERROR
    paths:
      exclude:
        - 'src/lib/helper/index.tsx'
        - 'src/lib/config/index.ts'
        - 'e2e/**'
        - 'scripts/**'
