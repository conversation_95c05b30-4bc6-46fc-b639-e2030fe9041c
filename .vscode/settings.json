{"i18n-ally.regex.usageMatchAppend": ["I18n.\\t\\([\\'\"]({key})[\\'\"]", "t\\([\\'\"]({key})[\\'\"]"], "i18n-ally.localesPaths": "src/lib/localization", "i18n-ally.sourceLanguage": ["vi", "en", "th", "ko", "id"], "i18n-ally.pathMatcher": "{locale}.json", "i18n-ally.displayLanguage": "vi", "i18n-ally.keystyle": "nested", "i18n-ally.extract.keyMaxLength": 1000, "i18n-ally.readonly": false, "i18n-ally.sortKeys": false, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript"], "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "javascript.referencesCodeLens.showOnAllFunctions": true, "javascript.referencesCodeLens.enabled": true, "typescript.referencesCodeLens.showOnAllFunctions": true, "typescript.referencesCodeLens.enabled": true, "cSpell.language": "en,vi", "cSpell.words": ["<PERSON><PERSON><PERSON><PERSON>", "bpay", "Btaskee", "exclamationcircleo", "favourite", "gorhom", "Ionicons", "<PERSON><PERSON>", "MIDTRANS", "Moca", "modalize", "momo", "Pressable", "qris", "shopee", "stringee", "Taskee", "Tasker", "Taskers", "zalo"]}