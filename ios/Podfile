require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'
require_relative '../node_modules/react-native-permissions/scripts/setup.rb'

platform :ios, '13.0'
install! 'cocoapods', :deterministic_uuids => false

setup_permissions([
  'Microphone',
  'Camera',
  'PhotoLibrary'
])

target 'bTaskee' do
  # use_modular_headers!

  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-google-maps', :path => rn_maps_path
  pod 'GoogleMaps'
  pod 'Google-Maps-iOS-Utils'

  pod 'FirebaseCrashlytics'
  pod 'FirebaseSessions'
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseInstallations', :modular_headers => true
  pod 'GoogleDataTransport', :modular_headers => true
  pod 'nanopb', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'FirebaseRemoteConfig', :modular_headers => true
  pod 'FirebaseABTesting', :modular_headers => true

  permissions_path = '../node_modules/react-native-permissions/ios'
  pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications"
  pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse"

  pod 'clevertap-react-native', :path =>  '../node_modules/clevertap-react-native'
  # pod 'CleverTap-Geofence-SDK'

  $RNFirebaseAsStaticFramework = true
  config = use_native_modules!

  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    # Upcoming versions of React Native may rely on get_default_flags(), but
    # we make it explicit here to aid in the React Native upgrade process.
    :hermes_enabled => false,
    :fabric_enabled => flags[:fabric_enabled],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    :flipper_configuration => FlipperConfiguration.enabled,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'bTaskeeTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    react_native_post_install(
      installer,
      # Set `mac_catalyst_enabled` to `true` in order to apply patches
      # necessary for Mac Catalyst builds
      :mac_catalyst_enabled => false
    )
    # NOTE: Change IPHONEOS_DEPLOYMENT_TARGET to 13.0.
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
        # Fix Xcode 15
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
        # Fix build M1
        config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
      end

      # Fix Xcode 15.4 Flipper issue
      # https://medium.com/@darwinchan02/flipper-bug-with-xcode-15-3-94da2b54ac1f
      if target.name == 'Flipper'
        file_path = 'Pods/Flipper/xplat/Flipper/FlipperTransportTypes.h'
        contents = File.read(file_path)
        unless contents.include?('#include <functional>')
          File.open(file_path, 'w') do |file|
            file.puts('#include <functional>')
            file.puts(contents)
          end
        end
      end

    end
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end

target 'notification' do
  pod "CleverTap-iOS-SDK"
  pod "CTNotificationService"
end
