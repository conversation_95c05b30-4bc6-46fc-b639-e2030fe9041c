<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>${APP_NAME}</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>${APP_VERSION_NAME}</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>facebook</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>${FACEBOOK_SCHEMES}</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>btaskee</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>btaskee</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>google</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>${GOOGLE_SCHEMES}</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>${APP_VERSION_CODE}</string>
	<key>CleverTapAccountID</key>
	<string>${CLEVERTAP_ACCOUNT_ID}</string>
	<key>CleverTapToken</key>
	<string>${CLEVERTAP_TOKEN}</string>
	<key>NSExtensionPrincipalClass</key>
	<string>${PRODUCT_BUNDLE_IDENTIFIER}.notification</string>
	<key>CodePushDeploymentKey</key>
	<string>${CODE_PUSH_API_KEY_IOS}</string>
	<key>FacebookAppID</key>
	<string>${FACEBOOK_APP_ID}</string>
	<key>FacebookClientToken</key>
	<string>${FACEBOOK_APP_CLIENT_TOKEN}</string>
	<key>FacebookDisplayName</key>
	<string>${FACEBOOK_APP_NAME}</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-api</string>
		<string>fbauth</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>fb-messenger-share-api</string>
		<string>tel</string>
		<string>mailto</string>
		<string>itms-apps</string>
		<string>zalo</string>
		<string>zalopay.api.v2</string>
		<string>zalopay</string>
		<string>https</string>
		<string>comgooglemaps</string>
		<string>maps</string>
		<string>shopeevn</string>
		<string>momo</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>bTaskee requires to access camera for taking profile picture</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your location will be displayed on the map, used for directions to the workplaces.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Your location will be displayed on the map, used for directions to the workplaces.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location will be displayed on the map, used for directions to the workplaces.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>bTaskee requires to access microphone for voice call</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>bTaskee requires to access photo library for uploading profile picture</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>bTaskee requires to access photo library for uploading profile picture</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This feature allows us to customize and improve your app experience and send you relevant bTaskee promotions.</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Montserrat-Bold.ttf</string>
		<string>Montserrat-Bold-Italic.ttf</string>
		<string>Montserrat-Medium.ttf</string>
		<string>Montserrat-Medium-Italic.ttf</string>
		<string>btaskee-icon.ttf</string>
		<string>Inter-Bold.ttf</string>
		<string>Inter-Regular.ttf</string>
		<string>Inter-SemiBold.ttf</string>
		<string>SVN-Diablo.otf</string>
		<string>UTM-Impact.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>voip</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
