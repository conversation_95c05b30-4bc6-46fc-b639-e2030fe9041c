// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* bTaskeeTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* bTaskeeTests.m */; };
		0917E3BF2CB7C7DF006DDD34 /* id_bells.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0917E3BE2CB7C7DF006DDD34 /* id_bells.mp3 */; };
		0917E3C12CB7C825006DDD34 /* id_notify.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0917E3C02CB7C824006DDD34 /* id_notify.mp3 */; };
		0C80B921A6F3F58F76C31292 /* libPods-bTaskee.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5DCACB8F33CDC322A6C60F78 /* libPods-bTaskee.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2D592B1D2B85B30100F54F0A /* Config.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 2D592B1C2B85B30100F54F0A /* Config.xcconfig */; };
		2D592B202B85BC2F00F54F0A /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 2D592B1F2B85BC2F00F54F0A /* GoogleService-Info.plist */; };
		2D592B242B85DC8500F54F0A /* notify.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 2D592B212B85DC8500F54F0A /* notify.mp3 */; };
		2D592B252B85DC8500F54F0A /* bells.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 2D592B222B85DC8500F54F0A /* bells.mp3 */; };
		2D592B262B85DC8500F54F0A /* outgoing.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 2D592B232B85DC8500F54F0A /* outgoing.mp3 */; };
		2D592B2C2B85F85900F54F0A /* PayZaloBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D592B2B2B85F85900F54F0A /* PayZaloBridge.m */; };
		2D592B2E2B85F8AC00F54F0A /* zpdk.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D592B2D2B85F8AC00F54F0A /* zpdk.framework */; };
		315C9B25EE664597812D2090 /* Montserrat-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2FC90FFA02944545BA232E31 /* Montserrat-Medium.ttf */; };
		32670FCB805B4BFE90FCCBA1 /* SVN-Diablo.otf in Resources */ = {isa = PBXBuildFile; fileRef = D2026762F2E144D5A77E0FB1 /* SVN-Diablo.otf */; };
		3690D7CB2D1BF27A00348408 /* end.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 3690D7CA2D1BF27A00348408 /* end.mp3 */; };
		369B79AA2D2F70BF00EFE661 /* CallKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 369B79A82D2F70BF00EFE661 /* CallKit.framework */; };
		369B79AC2D2F70EB00EFE661 /* Intents.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 369B79AB2D2F70EB00EFE661 /* Intents.framework */; };
		369D17AF2D6D7DCB00358D98 /* incallmanager_ringback.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 369D17AD2D6D7DCB00358D98 /* incallmanager_ringback.mp3 */; };
		369D17B02D6D7DCB00358D98 /* incallmanager_busytone.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 369D17AC2D6D7DCB00358D98 /* incallmanager_busytone.mp3 */; };
		369D17B12D6D7DCB00358D98 /* incallmanager_ringtone.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 369D17AE2D6D7DCB00358D98 /* incallmanager_ringtone.mp3 */; };
		36DE9D012BC8F2B4001D75EE /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 36DE9D002BC8F2B4001D75EE /* PrivacyInfo.xcprivacy */; };
		4EED35A4E9C04343926E3058 /* Montserrat-Bold-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DE3B3C6FAFED4F238277D25E /* Montserrat-Bold-Italic.ttf */; };
		5234A8BC2C4115C200CB3370 /* RNUserDefaults.m in Sources */ = {isa = PBXBuildFile; fileRef = 5234A8BB2C4115C200CB3370 /* RNUserDefaults.m */; };
		525DC2E72C40DCEE00530E3E /* NotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = 525DC2E62C40DCEE00530E3E /* NotificationService.m */; };
		525DC2EB2C40DCEE00530E3E /* notification.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 525DC2E32C40DCED00530E3E /* notification.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		7699B88040F8A987B510C191 /* libPods-bTaskee-bTaskeeTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 19F6CBCC0A4E27FBF8BF4A61 /* libPods-bTaskee-bTaskeeTests.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		A970234BD3BC42B682A52F03 /* UTM-Impact.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 35594D71FD4C4E06979E737E /* UTM-Impact.ttf */; };
		BFE7599BDD194ECBA90CD2F0 /* Montserrat-Medium-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FDA1398DA218405CA965D105 /* Montserrat-Medium-Italic.ttf */; };
		C4BD000A732FDB987B4E0609 /* libPods-notification.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 80EC88FC54F58C10E759EE29 /* libPods-notification.a */; };
		CC2B77A419D84CEC95BC96F3 /* btaskee-icon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9FF783C791594439ADC431F8 /* btaskee-icon.ttf */; };
		EEA15CA91BAB463AA80EAE9E /* Montserrat-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 13C37F05BAEE40DD8660F3F1 /* Montserrat-Bold.ttf */; };
		803AAFDEF5B84DA7A0CAA62C /* Inter-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8055B9A9148F432C8B4480B0 /* Inter-Bold.ttf */; };
		1321BFE352394263A4F804D4 /* Inter-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D8D207E831B5468CB877CE73 /* Inter-Regular.ttf */; };
		E18E4CBAF8274E5CABC5B4D7 /* Inter-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3D0DAF1DFED54950A8D6E36E /* Inter-SemiBold.ttf */; };
		32670FCB805B4BFE90FCCBA1 /* SVN-Diablo.otf in Resources */ = {isa = PBXBuildFile; fileRef = D2026762F2E144D5A77E0FB1 /* SVN-Diablo.otf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = bTaskee;
		};
		525DC2E92C40DCEE00530E3E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 525DC2E22C40DCED00530E3E;
			remoteInfo = notification;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		525DC2EC2C40DCEE00530E3E /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				525DC2EB2C40DCEE00530E3E /* notification.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* bTaskeeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = bTaskeeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* bTaskeeTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = bTaskeeTests.m; sourceTree = "<group>"; };
		0917E3BE2CB7C7DF006DDD34 /* id_bells.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = id_bells.mp3; sourceTree = "<group>"; };
		0917E3C02CB7C824006DDD34 /* id_notify.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = id_notify.mp3; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* bTaskee.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = bTaskee.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = bTaskee/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = bTaskee/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = bTaskee/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = bTaskee/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = bTaskee/main.m; sourceTree = "<group>"; };
		13C37F05BAEE40DD8660F3F1 /* Montserrat-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Bold.ttf"; path = "../src/lib/assets/fonts/Montserrat-Bold.ttf"; sourceTree = "<group>"; };
		19F6CBCC0A4E27FBF8BF4A61 /* libPods-bTaskee-bTaskeeTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-bTaskee-bTaskeeTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		2D20A2BC2B99C94B00379EBE /* bTaskeeDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = bTaskeeDebug.entitlements; path = bTaskee/bTaskeeDebug.entitlements; sourceTree = "<group>"; };
		2D592B1C2B85B30100F54F0A /* Config.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = Config.xcconfig; sourceTree = "<group>"; };
		2D592B1F2B85BC2F00F54F0A /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "bTaskee/GoogleService-Info.plist"; sourceTree = "<group>"; };
		2D592B212B85DC8500F54F0A /* notify.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = notify.mp3; sourceTree = "<group>"; };
		2D592B222B85DC8500F54F0A /* bells.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = bells.mp3; sourceTree = "<group>"; };
		2D592B232B85DC8500F54F0A /* outgoing.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = outgoing.mp3; sourceTree = "<group>"; };
		2D592B272B85DCE800F54F0A /* bTaskeeRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = bTaskeeRelease.entitlements; path = bTaskee/bTaskeeRelease.entitlements; sourceTree = "<group>"; };
		2D592B2A2B85F85900F54F0A /* PayZaloBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PayZaloBridge.h; path = zalo/PayZaloBridge.h; sourceTree = "<group>"; };
		2D592B2B2B85F85900F54F0A /* PayZaloBridge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = PayZaloBridge.m; path = zalo/PayZaloBridge.m; sourceTree = "<group>"; };
		2D592B2D2B85F8AC00F54F0A /* zpdk.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = zpdk.framework; path = Frameworks/zalo/zpdk.framework; sourceTree = "<group>"; };
		2FC90FFA02944545BA232E31 /* Montserrat-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Medium.ttf"; path = "../src/lib/assets/fonts/Montserrat-Medium.ttf"; sourceTree = "<group>"; };
		3690D7CA2D1BF27A00348408 /* end.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = end.mp3; sourceTree = "<group>"; };
		369B79A32D2F70BF00EFE661 /* libReactNativeIncallManager.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libReactNativeIncallManager.a; sourceTree = BUILT_PRODUCTS_DIR; };
		369B79A52D2F70BF00EFE661 /* libRNCallKeep.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libRNCallKeep.a; sourceTree = BUILT_PRODUCTS_DIR; };
		369B79A72D2F70BF00EFE661 /* libswiftCallKit.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libswiftCallKit.tbd; path = usr/lib/swift/libswiftCallKit.tbd; sourceTree = SDKROOT; };
		369B79A82D2F70BF00EFE661 /* CallKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CallKit.framework; path = System/Library/Frameworks/CallKit.framework; sourceTree = SDKROOT; };
		369B79AB2D2F70EB00EFE661 /* Intents.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Intents.framework; path = System/Library/Frameworks/Intents.framework; sourceTree = SDKROOT; };
		369D17AC2D6D7DCB00358D98 /* incallmanager_busytone.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = incallmanager_busytone.mp3; sourceTree = "<group>"; };
		369D17AD2D6D7DCB00358D98 /* incallmanager_ringback.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = incallmanager_ringback.mp3; sourceTree = "<group>"; };
		369D17AE2D6D7DCB00358D98 /* incallmanager_ringtone.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = incallmanager_ringtone.mp3; sourceTree = "<group>"; };
		35594D71FD4C4E06979E737E /* UTM-Impact.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = undefined; includeInIndex = 0; lastKnownFileType = unknown; name = "UTM-Impact.ttf"; path = "../src/lib/assets/fonts/UTM-Impact.ttf"; sourceTree = "<group>"; };
		36DE9D002BC8F2B4001D75EE /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		3B4392A12AC88292D35C810B /* Pods-bTaskee.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bTaskee.debug.xcconfig"; path = "Target Support Files/Pods-bTaskee/Pods-bTaskee.debug.xcconfig"; sourceTree = "<group>"; };
		492C640F01496DA5209ACC7B /* Pods-notification.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-notification.release.xcconfig"; path = "Target Support Files/Pods-notification/Pods-notification.release.xcconfig"; sourceTree = "<group>"; };
		5234A8B82C4114E900CB3370 /* RNUserDefaults.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = RNUserDefaults.h; path = userProfile/RNUserDefaults.h; sourceTree = "<group>"; };
		5234A8BB2C4115C200CB3370 /* RNUserDefaults.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = RNUserDefaults.m; path = userProfile/RNUserDefaults.m; sourceTree = "<group>"; };
		525DC2E32C40DCED00530E3E /* notification.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = notification.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		525DC2E52C40DCEE00530E3E /* NotificationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotificationService.h; sourceTree = "<group>"; };
		525DC2E62C40DCEE00530E3E /* NotificationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotificationService.m; sourceTree = "<group>"; };
		525DC2E82C40DCEE00530E3E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		525DC2F32C40ED5F00530E3E /* notification.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = notification.entitlements; sourceTree = "<group>"; };
		5709B34CF0A7D63546082F79 /* Pods-bTaskee.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bTaskee.release.xcconfig"; path = "Target Support Files/Pods-bTaskee/Pods-bTaskee.release.xcconfig"; sourceTree = "<group>"; };
		5B7EB9410499542E8C5724F5 /* Pods-bTaskee-bTaskeeTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bTaskee-bTaskeeTests.debug.xcconfig"; path = "Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests.debug.xcconfig"; sourceTree = "<group>"; };
		5DCACB8F33CDC322A6C60F78 /* libPods-bTaskee.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-bTaskee.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		74786F50444837727E12884E /* Pods-notification.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-notification.debug.xcconfig"; path = "Target Support Files/Pods-notification/Pods-notification.debug.xcconfig"; sourceTree = "<group>"; };
		80EC88FC54F58C10E759EE29 /* libPods-notification.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-notification.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = bTaskee/LaunchScreen.storyboard; sourceTree = "<group>"; };
		89C6BE57DB24E9ADA2F236DE /* Pods-bTaskee-bTaskeeTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bTaskee-bTaskeeTests.release.xcconfig"; path = "Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests.release.xcconfig"; sourceTree = "<group>"; };
		9FF783C791594439ADC431F8 /* btaskee-icon.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "btaskee-icon.ttf"; path = "../src/lib/assets/fonts/btaskee-icon.ttf"; sourceTree = "<group>"; };
		D2026762F2E144D5A77E0FB1 /* SVN-Diablo.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = undefined; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-Diablo.otf"; path = "../src/lib/assets/fonts/SVN-Diablo.otf"; sourceTree = "<group>"; };
		DE3B3C6FAFED4F238277D25E /* Montserrat-Bold-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Bold-Italic.ttf"; path = "../src/lib/assets/fonts/Montserrat-Bold-Italic.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		FDA1398DA218405CA965D105 /* Montserrat-Medium-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Medium-Italic.ttf"; path = "../src/lib/assets/fonts/Montserrat-Medium-Italic.ttf"; sourceTree = "<group>"; };
		8055B9A9148F432C8B4480B0 /* Inter-Bold.ttf */ = {isa = PBXFileReference; name = "Inter-Bold.ttf"; path = "../src/lib/assets/fonts/Inter-Bold.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		D8D207E831B5468CB877CE73 /* Inter-Regular.ttf */ = {isa = PBXFileReference; name = "Inter-Regular.ttf"; path = "../src/lib/assets/fonts/Inter-Regular.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		3D0DAF1DFED54950A8D6E36E /* Inter-SemiBold.ttf */ = {isa = PBXFileReference; name = "Inter-SemiBold.ttf"; path = "../src/lib/assets/fonts/Inter-SemiBold.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		D2026762F2E144D5A77E0FB1 /* SVN-Diablo.otf */ = {isa = PBXFileReference; name = "SVN-Diablo.otf"; path = "../src/lib/assets/fonts/SVN-Diablo.otf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				7699B88040F8A987B510C191 /* libPods-bTaskee-bTaskeeTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				369B79AC2D2F70EB00EFE661 /* Intents.framework in Frameworks */,
				369B79AA2D2F70BF00EFE661 /* CallKit.framework in Frameworks */,
				0C80B921A6F3F58F76C31292 /* libPods-bTaskee.a in Frameworks */,
				2D592B2E2B85F8AC00F54F0A /* zpdk.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		525DC2E02C40DCED00530E3E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C4BD000A732FDB987B4E0609 /* libPods-notification.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* bTaskeeTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* bTaskeeTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = bTaskeeTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* bTaskee */ = {
			isa = PBXGroup;
			children = (
				2D20A2BC2B99C94B00379EBE /* bTaskeeDebug.entitlements */,
				2D592B272B85DCE800F54F0A /* bTaskeeRelease.entitlements */,
				2D592B1F2B85BC2F00F54F0A /* GoogleService-Info.plist */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = bTaskee;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				369B79AB2D2F70EB00EFE661 /* Intents.framework */,
				369B79A82D2F70BF00EFE661 /* CallKit.framework */,
				369B79A72D2F70BF00EFE661 /* libswiftCallKit.tbd */,
				369B79A32D2F70BF00EFE661 /* libReactNativeIncallManager.a */,
				369B79A52D2F70BF00EFE661 /* libRNCallKeep.a */,
				2D592B2D2B85F8AC00F54F0A /* zpdk.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				5DCACB8F33CDC322A6C60F78 /* libPods-bTaskee.a */,
				19F6CBCC0A4E27FBF8BF4A61 /* libPods-bTaskee-bTaskeeTests.a */,
				80EC88FC54F58C10E759EE29 /* libPods-notification.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		525DC2E42C40DCEE00530E3E /* notification */ = {
			isa = PBXGroup;
			children = (
				525DC2F32C40ED5F00530E3E /* notification.entitlements */,
				525DC2E52C40DCEE00530E3E /* NotificationService.h */,
				525DC2E62C40DCEE00530E3E /* NotificationService.m */,
				525DC2E82C40DCEE00530E3E /* Info.plist */,
			);
			path = notification;
			sourceTree = "<group>";
		};
		6EFC833F88114884809FE7AD /* Resources */ = {
			isa = PBXGroup;
			children = (
				DE3B3C6FAFED4F238277D25E /* Montserrat-Bold-Italic.ttf */,
				13C37F05BAEE40DD8660F3F1 /* Montserrat-Bold.ttf */,
				FDA1398DA218405CA965D105 /* Montserrat-Medium-Italic.ttf */,
				2FC90FFA02944545BA232E31 /* Montserrat-Medium.ttf */,
				9FF783C791594439ADC431F8 /* btaskee-icon.ttf */,
				8055B9A9148F432C8B4480B0 /* Inter-Bold.ttf */,
				D8D207E831B5468CB877CE73 /* Inter-Regular.ttf */,
				3D0DAF1DFED54950A8D6E36E /* Inter-SemiBold.ttf */,
				D2026762F2E144D5A77E0FB1 /* SVN-Diablo.otf */,
				35594D71FD4C4E06979E737E /* UTM-Impact.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				369D17AC2D6D7DCB00358D98 /* incallmanager_busytone.mp3 */,
				369D17AD2D6D7DCB00358D98 /* incallmanager_ringback.mp3 */,
				369D17AE2D6D7DCB00358D98 /* incallmanager_ringtone.mp3 */,
				3690D7CA2D1BF27A00348408 /* end.mp3 */,
				5234A8BB2C4115C200CB3370 /* RNUserDefaults.m */,
				5234A8B82C4114E900CB3370 /* RNUserDefaults.h */,
				36DE9D002BC8F2B4001D75EE /* PrivacyInfo.xcprivacy */,
				2D592B2A2B85F85900F54F0A /* PayZaloBridge.h */,
				2D592B2B2B85F85900F54F0A /* PayZaloBridge.m */,
				2D592B222B85DC8500F54F0A /* bells.mp3 */,
				0917E3C02CB7C824006DDD34 /* id_notify.mp3 */,
				2D592B212B85DC8500F54F0A /* notify.mp3 */,
				0917E3BE2CB7C7DF006DDD34 /* id_bells.mp3 */,
				2D592B232B85DC8500F54F0A /* outgoing.mp3 */,
				2D592B1C2B85B30100F54F0A /* Config.xcconfig */,
				13B07FAE1A68108700A75B9A /* bTaskee */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* bTaskeeTests */,
				525DC2E42C40DCEE00530E3E /* notification */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				6EFC833F88114884809FE7AD /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* bTaskee.app */,
				00E356EE1AD99517003FC87E /* bTaskeeTests.xctest */,
				525DC2E32C40DCED00530E3E /* notification.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B4392A12AC88292D35C810B /* Pods-bTaskee.debug.xcconfig */,
				5709B34CF0A7D63546082F79 /* Pods-bTaskee.release.xcconfig */,
				5B7EB9410499542E8C5724F5 /* Pods-bTaskee-bTaskeeTests.debug.xcconfig */,
				89C6BE57DB24E9ADA2F236DE /* Pods-bTaskee-bTaskeeTests.release.xcconfig */,
				74786F50444837727E12884E /* Pods-notification.debug.xcconfig */,
				492C640F01496DA5209ACC7B /* Pods-notification.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* bTaskeeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "bTaskeeTests" */;
			buildPhases = (
				A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				C59DA0FBD6956966B86A3779 /* [CP] Embed Pods Frameworks */,
				F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = bTaskeeTests;
			productName = bTaskeeTests;
			productReference = 00E356EE1AD99517003FC87E /* bTaskeeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* bTaskee */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "bTaskee" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				525DC2EC2C40DCEE00530E3E /* Embed Foundation Extensions */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
				90889A8ED9C4377BCE2EFE8D /* [CP-User] [RNFB] Crashlytics Configuration */,
				2D592B1E2B85BB1300F54F0A /* ShellScript */,
				A62FC7178CA05DA33484E89F /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
				525DC2EA2C40DCEE00530E3E /* PBXTargetDependency */,
			);
			name = bTaskee;
			productName = bTaskee;
			productReference = 13B07F961A680F5B00A75B9A /* bTaskee.app */;
			productType = "com.apple.product-type.application";
		};
		525DC2E22C40DCED00530E3E /* notification */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 525DC2EF2C40DCEE00530E3E /* Build configuration list for PBXNativeTarget "notification" */;
			buildPhases = (
				38A21AC73A98D4D7057187CD /* [CP] Check Pods Manifest.lock */,
				525DC2DF2C40DCED00530E3E /* Sources */,
				525DC2E02C40DCED00530E3E /* Frameworks */,
				525DC2E12C40DCED00530E3E /* Resources */,
				0F0AD4F1E12743283524F62E /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = notification;
			productName = notification;
			productReference = 525DC2E32C40DCED00530E3E /* notification.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
					525DC2E22C40DCED00530E3E = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "bTaskee" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* bTaskee */,
				00E356ED1AD99517003FC87E /* bTaskeeTests */,
				525DC2E22C40DCED00530E3E /* notification */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2D592B262B85DC8500F54F0A /* outgoing.mp3 in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				2D592B242B85DC8500F54F0A /* notify.mp3 in Resources */,
				369D17AF2D6D7DCB00358D98 /* incallmanager_ringback.mp3 in Resources */,
				369D17B02D6D7DCB00358D98 /* incallmanager_busytone.mp3 in Resources */,
				369D17B12D6D7DCB00358D98 /* incallmanager_ringtone.mp3 in Resources */,
				36DE9D012BC8F2B4001D75EE /* PrivacyInfo.xcprivacy in Resources */,
				0917E3BF2CB7C7DF006DDD34 /* id_bells.mp3 in Resources */,
				2D592B202B85BC2F00F54F0A /* GoogleService-Info.plist in Resources */,
				2D592B252B85DC8500F54F0A /* bells.mp3 in Resources */,
				2D592B1D2B85B30100F54F0A /* Config.xcconfig in Resources */,
				0917E3C12CB7C825006DDD34 /* id_notify.mp3 in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				4EED35A4E9C04343926E3058 /* Montserrat-Bold-Italic.ttf in Resources */,
				EEA15CA91BAB463AA80EAE9E /* Montserrat-Bold.ttf in Resources */,
				3690D7CB2D1BF27A00348408 /* end.mp3 in Resources */,
				BFE7599BDD194ECBA90CD2F0 /* Montserrat-Medium-Italic.ttf in Resources */,
				315C9B25EE664597812D2090 /* Montserrat-Medium.ttf in Resources */,
				CC2B77A419D84CEC95BC96F3 /* btaskee-icon.ttf in Resources */,
				803AAFDEF5B84DA7A0CAA62C /* Inter-Bold.ttf in Resources */,
				1321BFE352394263A4F804D4 /* Inter-Regular.ttf in Resources */,
				E18E4CBAF8274E5CABC5B4D7 /* Inter-SemiBold.ttf in Resources */,
				32670FCB805B4BFE90FCCBA1 /* SVN-Diablo.otf in Resources */,
				A970234BD3BC42B682A52F03 /* UTM-Impact.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		525DC2E12C40DCED00530E3E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee/Pods-bTaskee-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee/Pods-bTaskee-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bTaskee/Pods-bTaskee-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		0F0AD4F1E12743283524F62E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-notification/Pods-notification-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-notification/Pods-notification-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-notification/Pods-notification-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2D592B1E2B85BB1300F54F0A /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
				"$(SRCROOT)/${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildXCConfig.rb\" \"${SRCROOT}/..\" \"${SRCROOT}/tmp.xcconfig\"\n\"${PODS_ROOT}/FirebaseCrashlytics/run\"\n";
		};
		38A21AC73A98D4D7057187CD /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-notification-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		90889A8ED9C4377BCE2EFE8D /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-bTaskee-bTaskeeTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A62FC7178CA05DA33484E89F /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-bTaskee-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C59DA0FBD6956966B86A3779 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee/Pods-bTaskee-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee/Pods-bTaskee-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bTaskee/Pods-bTaskee-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bTaskee-bTaskeeTests/Pods-bTaskee-bTaskeeTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* bTaskeeTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				5234A8BC2C4115C200CB3370 /* RNUserDefaults.m in Sources */,
				2D592B2C2B85F85900F54F0A /* PayZaloBridge.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		525DC2DF2C40DCED00530E3E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				525DC2E72C40DCEE00530E3E /* NotificationService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* bTaskee */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		525DC2EA2C40DCEE00530E3E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 525DC2E22C40DCED00530E3E /* notification */;
			targetProxy = 525DC2E92C40DCEE00530E3E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5B7EB9410499542E8C5724F5 /* Pods-bTaskee-bTaskeeTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = bTaskeeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bTaskee.app/bTaskee";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 89C6BE57DB24E9ADA2F236DE /* Pods-bTaskee-bTaskeeTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = bTaskeeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bTaskee.app/bTaskee";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B4392A12AC88292D35C810B /* Pods-bTaskee.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bTaskee/bTaskeeDebug.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 85Q6F9K636;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/zalo",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/AppAuth\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/Base64\"",
					"\"${PODS_ROOT}/Headers/Public/CTNotificationService\"",
					"\"${PODS_ROOT}/Headers/Public/CleverTap-iOS-SDK\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/CodePush\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCrashlytics\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseDynamicLinks\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseSessions\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GTMAppAuth\"",
					"\"${PODS_ROOT}/Headers/Public/GTMSessionFetcher\"",
					"\"${PODS_ROOT}/Headers/Public/Google-Maps-iOS-Utils\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleMLKit\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleSignIn\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleToolboxForMac\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilitiesComponents\"",
					"\"${PODS_ROOT}/Headers/Public/JWT\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationWhenInUse\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Notifications\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNAppleAuthentication\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNCallKeep\"",
					"\"${PODS_ROOT}/Headers/Public/RNDateTimePicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBAnalytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBCrashlytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBDynamicLinks\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNGoogleSignin\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageCropPicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNNotifee\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNQuickAction\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNShare\"",
					"\"${PODS_ROOT}/Headers/Public/RNSound\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/RNVoipPushNotification\"",
					"\"${PODS_ROOT}/Headers/Public/React-Codegen\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-bridging\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeIncallManager\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/TOCropViewController\"",
					"\"${PODS_ROOT}/Headers/Public/Toast\"",
					"\"${PODS_ROOT}/Headers/Public/VisionCamera\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/clevertap-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blob-util\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blurhash\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-cameraroll\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-color-matrix-image-filters\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-compressor\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-detector\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-fbsdk-next\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-google-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-in-app-review\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-launch-arguments\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-restart\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-tracking-transparency\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-video\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-view-shot\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-vnpay-merchant\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_ROOT}/Headers/Public/stream-react-native-webrtc\"",
					"\"${PODS_ROOT}/Headers/Public/stream-video-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/vision-camera-code-scanner\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"$(inherited)",
					"${PODS_ROOT}/GoogleMLKit/MLKitCore/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"$(SRCROOT)/../node_modules/react-native-callkeep/ios/RNCallKeep",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
				);
				INFOPLIST_FILE = bTaskee/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(SDKROOT)/usr/lib/swift",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.lanterns.btaskee;
				PRODUCT_NAME = bTaskee;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				USER_HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/libwebp/src",
					"$(SRCROOT)/../node_modules/react-native-callkeep/ios/RNCallKeep",
				);
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5709B34CF0A7D63546082F79 /* Pods-bTaskee.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bTaskee/bTaskeeRelease.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 85Q6F9K636;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/zalo",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/AppAuth\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/Base64\"",
					"\"${PODS_ROOT}/Headers/Public/CTNotificationService\"",
					"\"${PODS_ROOT}/Headers/Public/CleverTap-iOS-SDK\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/CodePush\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCrashlytics\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseDynamicLinks\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseSessions\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GTMAppAuth\"",
					"\"${PODS_ROOT}/Headers/Public/GTMSessionFetcher\"",
					"\"${PODS_ROOT}/Headers/Public/Google-Maps-iOS-Utils\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleMLKit\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleSignIn\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleToolboxForMac\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilitiesComponents\"",
					"\"${PODS_ROOT}/Headers/Public/JWT\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-LocationWhenInUse\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Notifications\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNAppleAuthentication\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNCallKeep\"",
					"\"${PODS_ROOT}/Headers/Public/RNDateTimePicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBAnalytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBCrashlytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBDynamicLinks\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNGoogleSignin\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageCropPicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNNotifee\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNQuickAction\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNShare\"",
					"\"${PODS_ROOT}/Headers/Public/RNSound\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/RNVoipPushNotification\"",
					"\"${PODS_ROOT}/Headers/Public/React-Codegen\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-bridging\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeIncallManager\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/TOCropViewController\"",
					"\"${PODS_ROOT}/Headers/Public/Toast\"",
					"\"${PODS_ROOT}/Headers/Public/VisionCamera\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/clevertap-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blob-util\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blurhash\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-cameraroll\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-color-matrix-image-filters\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-compressor\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-detector\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-fbsdk-next\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-google-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-in-app-review\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-launch-arguments\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-restart\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-tracking-transparency\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-video\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-view-shot\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-vnpay-merchant\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_ROOT}/Headers/Public/stream-react-native-webrtc\"",
					"\"${PODS_ROOT}/Headers/Public/stream-video-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/vision-camera-code-scanner\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"$(inherited)",
					"${PODS_ROOT}/GoogleMLKit/MLKitCore/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"$(SRCROOT)/../node_modules/react-native-callkeep/ios/RNCallKeep",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
				);
				INFOPLIST_FILE = bTaskee/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(SDKROOT)/usr/lib/swift",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.lanterns.btaskee;
				PRODUCT_NAME = bTaskee;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				USER_HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/libwebp/src",
					"$(SRCROOT)/../node_modules/react-native-callkeep/ios/RNCallKeep",
				);
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		525DC2ED2C40DCEE00530E3E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 74786F50444837727E12884E /* Pods-notification.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = notification/notification.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 85Q6F9K636;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = notification/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = notification;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.lanterns.btaskee.notification;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		525DC2EE2C40DCEE00530E3E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 492C640F01496DA5209ACC7B /* Pods-notification.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = notification/notification.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 85Q6F9K636;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = notification/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = notification;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.lanterns.btaskee.notification;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2D592B1C2B85B30100F54F0A /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
					"-ld_classic",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2D592B1C2B85B30100F54F0A /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
					"-ld_classic",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "bTaskeeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "bTaskee" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		525DC2EF2C40DCEE00530E3E /* Build configuration list for PBXNativeTarget "notification" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				525DC2ED2C40DCEE00530E3E /* Debug */,
				525DC2EE2C40DCEE00530E3E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "bTaskee" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
