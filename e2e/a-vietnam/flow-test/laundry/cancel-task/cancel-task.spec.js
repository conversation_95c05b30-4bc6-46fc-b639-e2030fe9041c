/**
 * case 1: <PERSON><PERSON><PERSON> 38 - Ask<PERSON> cancel laundry task, fee = 0
 * case 2: LINE 73 - Asker cancel laundry task include cancel fee
 **/

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  expectIdToHaveText,
  waitForElement,
  swipe,
  expectElementVisibleAtIndex,
} = require('../../../../step-definition');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASK1 = {
  isoCode: 'VN',
  serviceName: 'LAUNDRY',
  askerPhone: ASKER.phone,
  description: 'Task 01',
};

describe('FILE: e2e/a-vietnam/flow-test/laundry/cancel-task/cancel-task.spec.js - Asker cancel Laundry task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1]);
    await initData('task/createTask', [TASK1]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: ********* },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 57 - Asker cancel laundry task, fee = 0', async () => {
    await tapId('Tab_Activity');
    // SEE TASK ITEM
    await expectIdToHaveText('serviceNameTask 01', 'Giặt ủi');
    await tapId('taskTask 01');

    // SEE TASK DETAIL
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await expectElementVisible('cancelTask');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskTask 01');
  });

  it('LINE 92 - Asker cancel laundry task include cancel fee', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK1.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapId('Tab_Activity');
    // SEE TASK ITEM
    await expectIdToHaveText('serviceNameTask 01', 'Giặt ủi');
    await expectElementVisibleAtIndex('Lưu ý: Vui lòng kiểm tra đồ đạc khi giao - nhận', 0, 'text');
    await tapId('taskTask 01');

    // SEE TASK DETAIL
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await expectElementVisible('cancelTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // choose reason cancel task, chon ly do thi moi dong y duoc
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskTask 01');
  });
});
