const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  postTask,
  expectElementVisible,
  tapHeaderBack,
  expectElementNotVisible,
  waitForLoading,
} = require('../../../step-definition');
const { E2EHelpers } = require('../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};

describe('FILE: e2e/a-vietnam/flow-test/z-view-test/asker-see-special-pre-booking.spec.js - Special pre-booking task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('event-config/create-data', [{ isoCode: 'VN', services: ['CLEANING'] }]);
    await initData('service/update-fields', {
      isoCode: 'VN',
      serviceName: 'CLEANING',
      dataUpdate: {
        tetBookingDates: {
          bookTaskTime: {
            fromDate: moment().startOf('month').toDate(),
            toDate: moment().add(32, 'days').toDate(),
          },
          fromDate: moment().startOf('month').toDate(),
          toDate: moment().add(60, 'days').toDate(),
        },
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 48 - Asker booking task with special pre-booking', async () => {
    await postTask('postTaskServiceTetCLEANING', 'Pham Van Nghi');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapText('Đăng việc');
    await waitForLoading(500);
    await expectElementVisible(
      'Bạn cần thêm 210,000 VND để có thể đăng công việc này. Vui lòng nạp thêm hoặc chọn hình thức thanh toán khác.',
      'text',
    );
    await tapText('Đóng');
    await tapId('choosePaymentMethod');
    await expectElementNotVisible('Tiền mặt', 'text');
    await tapHeaderBack();
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      financialAccountData: { FMainAccount: 5000000 },
    });
    await tapText('Đăng việc');

    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '210,000 ₫');
    await expectIdToHaveText('paymentMethod', 'bPay');
  });
});
