/**
 * @description Asker can navigate to verify phone number
 *  case 1: <PERSON><PERSON><PERSON> 60 - <PERSON><PERSON> get OTP more 3 turn, see alert and can type OTP
 *  case 2: <PERSON><PERSON><PERSON> 81 - Ask<PERSON> do not receive OTP and press resend
 */
const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapText,
  tapId,
  waitForElement,
  tapIdAtIndex,
  typeToTextField,
  expectElementVisible,
  fillActiveCode,
  tapHeaderBack,
} = require('../../../step-definition');
const { device } = require('detox');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
describe('FILE: e2e/a-vietnam/flow-test/z-view-test/asker-can-natigate-to-verifi-number-page-after-try-3-turn.spec.js - Asker get OTP more 3 turn, see alert and can type OTP', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogout();
  });

  afterEach(async () => {
    try {
      await tapHeaderBack();
    } catch (error) {}
  });

  const forgotPassword = async () => {
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');
    await waitForElement('modalBtnLogin', 500);
    await tapId('modalBtnLogin');

    // Check keyboard
    try {
      // await waitForElementAtIndex('signInBtnForgotPassword', 500, 0);
      await tapIdAtIndex('signInBtnForgotPassword', 0);
    } catch (e) {
      // await waitForElementAtIndex('signInBtnForgotPassword', 500, 1);
      await tapIdAtIndex('signInBtnForgotPassword', 1);
    }

    // Activation screen
    await typeToTextField('inputPhoneNumberForgotPass', ASKER.phone);

    try {
      await tapIdAtIndex('nextButtonForgotPass', 0);
    } catch (e) {
      await tapIdAtIndex('nextButtonForgotPass', 1);
    }
  };

  it('LINE 60 - Asker get OTP more 3 turn, see alert and can type OTP', async () => {
    await initData('user/createActivationCode', { phone: ASKER.phone, numResent: 3, isoCode: 'VN' });
    await device.reloadReactNative();
    await forgotPassword();
    await expectElementVisible(
      'Bạn đã yêu cầu gửi mã xác thực quá số lần cho phép. Vui lòng liên hệ với bTaskee qua Zalo Oa hoặc tổng đài 1900 636 736 để được hỗ trợ.',
      'text',
    );
    await tapText('Đóng');
    await fillActiveCode('0834567890', '+84');
    // Type new password
    await typeToTextField('txtPassword', '113114115');
    await typeToTextField('txtSecondPassword', '113114115');
    try {
      await tapIdAtIndex('btnSavePassword', 0);
    } catch (e) {
      await tapIdAtIndex('btnSavePassword', 1);
    }
    await expectElementVisible('Xin chào Asker', 'text');
  });

  it('LINE 81 - Asker do not receive OTP and press resend', async () => {
    await initData('user/createActivationCode', { phone: ASKER.phone, numResent: 1, isoCode: 'VN' });
    await device.reloadReactNative();
    await forgotPassword();
    await waitForElement('btnResent', 1500);
    await tapId('btnResent');
    await expectElementVisible('txtChooseSupplier');
    await expectElementVisible('btnSMS');
    await expectElementVisible('btnSupplier');
    await tapId('btnSMS');
    await fillActiveCode('0834567890', '+84');
    // Type new password
    await typeToTextField('txtPassword', '113114115');
    await typeToTextField('txtSecondPassword', '113114115');
    try {
      await tapIdAtIndex('btnSavePassword', 0);
    } catch (e) {
      await tapIdAtIndex('btnSavePassword', 1);
    }
    await expectElementVisible('Xin chào Asker', 'text');
  });
});
