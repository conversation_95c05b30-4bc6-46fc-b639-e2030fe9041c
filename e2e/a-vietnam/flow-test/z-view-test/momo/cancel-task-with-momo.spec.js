const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  tapId,
  swipe,
  tapText,
  initData,
  waitForElement,
  expectElementVisible,
  expectElementNotExist,
} = require('../../../../step-definition');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASK_01 = {
  isoCode: 'VN',
  serviceName: 'CLEANING',
  askerPhone: ASKER.phone,
  description: 'My Task',
};

describe('FILE: e2e/a-vietnam/flow-test/z-view-test/momo/cancel-task-with-momo.spec.js - Cancel task cleaning with momo', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK_01]);

    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          fromPartner: 'MOMO_MINI_APP',
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
          payment: {
            method: 'MOMO',
            status: 'PAID',
          },
        },
      },
    ]);
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK_01.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 66 - Asker cancel task cleaning with momo', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId(`task${TASK_01.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
  });
});
