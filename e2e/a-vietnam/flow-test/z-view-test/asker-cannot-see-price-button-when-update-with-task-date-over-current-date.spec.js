/**
 * @description Asker update task with status CONFIRMED
 *   case 1: LINE 26 - Asker see alert when update with task date over current time
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  swipe,
  selectTime24h,
} = require('../../../step-definition');
const moment = require('moment');
const { device } = require('detox');
const { E2EHelpers } = require('../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'TASKER',
  type: 'TASKER',
  status: 'ACTIVE',
};
describe('FILE: e2e/a-vietnam/flow-test/z-view-test/asker-cannot-see-price-button-when-update-with-task-date-over-current-date.spec.js - Asker update task with status CONFIRMED', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await device.reloadReactNative();
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 41 - Asker see alert when update with task date over current time', async () => {
    const currentHour = moment(new Date()).hours();

    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: ASKER.phone,
        description: 'Nau an 01',
        status: 'POSTED',
        taskerAccepted: [TASKER.phone],
      },
    ]);

    await initData('task/updateTask', [
      {
        description: 'Nau an 01',
        isoCode: 'VN',
        dataUpdate: {
          updateDate: moment().subtract(1, 'h').utc().format(),
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(50, 'minute').toDate(),
          status: 'CONFIRMED',
          requirements: [],
        },
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameNau an 01', 'Dọn dẹp nhà');
    await expectIdToHaveText('taskStatusNau an 01', 'Xác nhận');

    await tapId('serviceNameNau an 01');
    await expectIdToHaveText('taskDetailTaskerName', 'TASKER');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapText('Giữ Tasker hiện tại');
    await tapId('weekdays_1');
    await selectTime24h(10, currentHour);
    await tapText('Đồng ý');
    await tapId('chooseDuration-4');
    await tapId('chooseDuration-3');
    try {
      await tapText('Cập nhật');
    } catch (error) {
      await tapText('Đổi Tasker');
    }
    await expectElementVisible('Hết hạn thay đổi thời gian', 'text');
  });
});
