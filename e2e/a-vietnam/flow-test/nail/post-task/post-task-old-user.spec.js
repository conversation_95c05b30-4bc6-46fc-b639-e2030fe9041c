/**
 * @description Old user post task (8 test cases)
 *   case 1: <PERSON><PERSON><PERSON> 30 - Already Loged-in and I want to post a Task for me
 *   case 2: <PERSON><PERSON><PERSON> 45 - Check Choose Tasker Does It Mean
 *   case 3: <PERSON><PERSON><PERSON> 59 - Old user post new task
 *   case 4: <PERSON><PERSON><PERSON> 88 - New User post task cleaning 2h
 *   case 5: <PERSON><PERSON><PERSON> 131 - New User post task cleaning 3h
 *   case 6: <PERSON><PERSON><PERSON> 149 - New User post task cleaning 4h
 *   case 7: LINE 167 - bPay New User post task cleaning error
 *   case 8: LINE 187 - bPay New User post task cleaning
 * */

const {
  initData,
  tapId,
  tapText,
  waitForElement,
  expectIdToHaveText,
  expectElementVisible,
  expectElementNotVisible,
  swipe,
  tapHeaderBack,
  tapIdService,
  postTaskNail,
} = require('../../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
  FMainAccount: 0,
};

describe('FILE: e2e/a-vietnam/flow-test/nail/post-task/post-task-old-user.spec.js - Old user post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
    try {
      await tapHeaderBack();
    } catch (error) {}
  });

  it('LINE 52 - [REQUIRED] - Already Loged-in and I want to post a Task for me', async () => {
    await postTaskNail();
    await tapText('Xem công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    const data = await initData('user/getUserByPhone', { phone: '**********', countryCode: '+84' });
    expect(data.cities[0].country).to.equal('VN');
    expect(data.cities[0].city).to.equal('Hồ Chí Minh');
  });

  it('LINE 60 - Old user post new task patient', async () => {
    await postTaskNail();
    await tapText('Xem công việc');
    await tapText('Trang chủ');
    await postTaskNail();
    await waitForElement('Bạn đang có 1 công việc vào giờ này, bạn muốn có thêm 1 Tasker nữa ?', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 92 - bPay New User post task patient error', async () => {
    await tapIdService('postTaskServiceBEAUTY_CARE');
    await tapText('Trang điểm & Tạo mẫu tóc');
    await tapId('1-people');
    await tapId('graduationMakeup');
    await tapText('Tiếp tục');
    await tapId('graduationNaturalMakeup');
    await tapText('Tiếp tục');
    await tapId('address-0');
    await waitForElement('Tiếp tục', 500, 'text');
    await tapText('Tiếp tục');
    await waitForElement('taskNoteConfirm', 500);
    await tapId('taskNoteConfirm');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiền mặt');
    await tapText('bPay');
    await expectElementNotVisible('Tiền mặt', 'text');
    await tapText('Tiếp tục');
    await expectElementVisible('Thông báo', 'text');
    await expectElementVisible(
      'Bạn cần thêm 400,000 VND để có thể đăng công việc này. Vui lòng nạp thêm hoặc chọn hình thức thanh toán khác.',
      'text',
    );
    await tapText('Đóng');
    await expectElementVisible('bPay', 'text');
    await expectElementNotVisible('Tiền mặt', 'text');
  });

  it('LINE 154 - bPay debt cant post task patient', async () => {
    await initData('faccount/updateFinancialAccount', [
      {
        phone: ASKER.phone,
        isoCode: ASKER.isoCode,
        dataUpdate: {
          FMainAccount: -1000000,
        },
      },
    ]);
    await postTaskNail();
    await waitForElement('Vui lòng thanh toán nợ. Sau khi thanh toán xong mới có thể đăng việc.', 1000, 'text');
    await tapText('Thanh toán');
  });
});
