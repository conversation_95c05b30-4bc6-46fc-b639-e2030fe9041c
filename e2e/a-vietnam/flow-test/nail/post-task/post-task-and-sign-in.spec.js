const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  typeToTextField,
  loginWithModal,
  forgotPasswordWithModal,
  tapIdService,
  ADDRESS_VALUE,
  ADDRESS_KEY,
  waitForElement,
  swipe,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

describe('FILE: e2e/a-vietnam/flow-test/nail/post-task/post-task-and-sign-in.spec.js - New User post cleaning task before sign in', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.byPassUnauthorize();
    await E2EHelpers.onHaveLogout();
  });

  const checkDataAfterPostTask = async () => {
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
  };

  const clickPostTask = async () => {
    await tapIdService('postTaskServiceBEAUTY_CARE');
    await tapText('Làm móng');
    await tapId('1-people');
    await tapId('nailBaseCare');

    await waitForElement('Sơn thường', 500, 'text');
    await tapText('Sơn thường');
    await tapText('Chọn');
    await waitForElement('Sơn mắt mèo', 1000, 'text');
    await tapText('Sơn mắt mèo');
    await tapText('Chọn');
    await tapText('Tiếp tục');

    await typeToTextField('txtInputAddress', 'btaskee D1');
    await tapText(ADDRESS_VALUE[ADDRESS_KEY.HCM]);
    await waitForElement('Chọn vị trí này', 500, 'text');
    await tapText('Chọn vị trí này');
    await typeToTextField('txtInputPhoneNumber', '0834567890');
    await typeToTextField('txtInputContactName', 'Kaiser');
    await typeToTextField('txtInputHomeNumber', 'My Task');
    await tapText('Xác nhận');
    await waitForElement('Tiếp tục', 500, 'text');
    await tapText('Tiếp tục');
    await waitForElement('taskNoteConfirm', 500);
    await tapId('taskNoteConfirm');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
  };

  it('LINE 59 - New customer post cleaning task elderly and sign in', async () => {
    await clickPostTask();
    await loginWithModal('0834567890', '123456');
    await tapText('Xem công việc');
    await checkDataAfterPostTask();
  });

  it('LINE 69 - New customer post cleaning task elderly and for got password', async () => {
    await clickPostTask();
    await forgotPasswordWithModal('0834567890');
    await tapText('Xem công việc');
    await checkDataAfterPostTask();
  });
});
