/**
import { getCurrency } from '@helper';
 * @description Asker cancel task with promotion
 *   case 1: Asker cancel the task is not include fee
 *   case 2: Asker cancel the task include fee
 * */

const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  expectIdToHaveText,
  waitForElement,
  tapText,
  typeToTextFieldDishName,
  typeToTextFieldSubmitKeyboard,
  tapHeaderBack,
  tapIdService,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const cancelTask = async () => {
  await tapId('TAB_UPCOMING_My Task');
  await swipe('scrollTaskDetail', 'up');
  await tapId('btnEditTask');
  await tapId('cancelTask');
  await tapId('btnConfirmCancel');
  await waitForElement('Không cần công việc này nữa.', 500, 'text');
  await tapText('Không cần công việc này nữa.');
  await tapText('Đồng ý');
  await expectElementVisible(
    'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
    'text',
  );
  await tapText('Tiếp tục');
};

describe('FILE: e2e/a-vietnam/flow-test/home-cooking/cancel-task/asker-cancel-task-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'VN',
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: '',
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: 90000 },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 91 - Asker cancel the task is not include fee', async () => {
    await tapIdService('postTaskServiceHOME_COOKING');

    await typeToTextFieldDishName();
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('promotionCode');
    await typeToTextFieldSubmitKeyboard('textInputPromotion', 'def123');
    await expectIdToHaveText('originPrice', '150,000 VND');
    await expectIdToHaveText('price', '100,000 VND');
    await tapId('btnSubmitPostTask');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMING_My Task');
    await expectIdToHaveText('lbPriceMy Task', '100,000 ₫');
    await cancelTask();

    await tapId('Tab_Home');
    await tapIdService('postTaskServiceHOME_COOKING');

    await typeToTextFieldDishName();
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('promotionCode');
    await typeToTextFieldSubmitKeyboard('textInputPromotion', 'def123');
    await expectIdToHaveText('originPrice', '150,000 VND');
    await expectIdToHaveText('price', '100,000 VND');
    await tapId('btnSubmitPostTask');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMING_My Task');
    await expectIdToHaveText('lbPriceMy Task', '100,000 ₫');
  });

  it('LINE 135 - Asker cancel the task include fee', async () => {
    await tapIdService('postTaskServiceHOME_COOKING');

    await typeToTextFieldDishName();
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('promotionCode');
    await typeToTextFieldSubmitKeyboard('textInputPromotion', 'def123');
    await expectIdToHaveText('originPrice', '150,000 VND');
    await expectIdToHaveText('price', '100,000 VND');
    await tapId('btnSubmitPostTask');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMING_My Task');
    await expectIdToHaveText('lbPriceMy Task', '100,000 ₫');
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'My Task', taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(10, 'hour').toDate(),
          date: moment().add(50, 'minute').toDate(),
          status: 'CONFIRMED',
        },
      },
    ]);
    await waitForElement('TAB_UPCOMING_My Task', 500);
    await cancelTask();
    await tapId('Tab_Home');
    await tapIdService('postTaskServiceHOME_COOKING');

    await typeToTextFieldDishName();
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('promotionCode');
    await typeToTextFieldSubmitKeyboard('textInputPromotion', 'def123');
    await expectElementVisible('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 'text');
    await tapText('Đóng');
    await tapHeaderBack();
    await tapId('btnSubmitPostTask');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMING_My Task');
    await expectIdToHaveText('lbPriceMy Task', '150,000 ₫');
  });
});
