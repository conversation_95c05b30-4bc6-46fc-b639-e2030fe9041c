/**
 * @description Asker cancel task. Tasker will see the conflict tasks
 *   case 1: Asker cancel posted task
 *   case 2: Asker cancel posted task and other task of blacklist Asker
 * */

const {
  initData,
  loginWithPhoneAndPassword,
  tapId,
  tapText,
  swipe,
  waitForElement,
  expectElementVisible,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER_01 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER_02 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASK_01 = {
  isoCode: 'VN',
  serviceName: 'HOME_COOKING',
  askerPhone: ASKER.phone,
  description: 'Task 01',
};
const TASK_02 = {
  isoCode: 'VN',
  serviceName: 'HOME_COOKING',
  askerPhone: TASKER_02.phone,
  description: 'Task 02',
};
const TASK_03 = {
  isoCode: 'VN',
  serviceName: 'HOME_COOKING',
  askerPhone: ASKER.phone,
  description: 'Task 03',
  viewedTaskers: [TASKER_01.phone],
};
describe('FILE: e2e/a-vietnam/flow-test/home-cooking/cancel-task/asker-cancel-task-and-tasker-view-conflict-tasks.spec.js - Asker cancel task. Tasker will see the conflict tasks', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER_02, TASKER_01]);
    await initData('task/createTask', [TASK_01, TASK_02, TASK_03]);
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: [TASKER_01.phone],
        description: TASK_03.description,
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 78 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGTask 03');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
  });
});
