/**
 * @description Asker cancel task limit
 *   case 1: Asker cancel task with task createdAt + 10 minutes is less than or equal current date and total task canceled is less than limit
 *   case 2: Asker cancel many tasks within 24h
 *   case 3: Asker cancel task with task createdAt + 10 minutes is greater than current date
 * */

const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  swipe,
  waitForElement,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

describe('FILE: e2e/a-vietnam/flow-test/home-cooking/cancel-task/asker-cancel-task-limit.spec.js - Asker cancel task limit', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 164 - Asker cancel task with task createdAt + 10 minutes is greater than current date', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'HOME_COOKING',
        askerPhone: ASKER.phone,
        description: 'Task Test',
        status: 'POSTED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Task Test',
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(-11, 'minute').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGTask Test');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // await tapText('Đồng ý');
    await waitForElement('Không cần công việc này nữa.', 1000, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGTask Test');
  });
});
