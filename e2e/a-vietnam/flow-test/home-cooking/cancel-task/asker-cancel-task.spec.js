/**
 * @description Asker cancel task
 * case 1: <PERSON><PERSON><PERSON> 54 - <PERSON><PERSON> cancel posted task'
 * case 2: <PERSON><PERSON><PERSON> 79 - Ask<PERSON> cancel waiting cooking task'
 * case 3: <PERSON><PERSON><PERSON> 94 - Ask<PERSON> cancel confirmed cooking task before working time'
 * case 4: LINE 124 - Ask<PERSON> cancel confirmed cooking task before working time, find same task for Tasker'
 * case 5: <PERSON><PERSON><PERSON> 169 - Ask<PERSON> cancel confirmed cooking task after task began 15 minutes'
 * case 6: <PERSON><PERSON><PERSON> 194 - Ask<PERSON> cancel confirmed cooking task with fee 20k'
 * case 7: <PERSON><PERSON><PERSON> 219 - <PERSON><PERSON> cancel waiting cooking task with fee 0k'
 * case 8: LIN<PERSON> 237 - <PERSON><PERSON> cancel posted task with free charge'
 * case 9: LINE 258 - Ask<PERSON> cancel confirmed cooking task with reason Tasker not comming free'
 * case 10: LINE 280 - <PERSON><PERSON> cancel task and input the reason'
 * case 11: LINE 296 - <PERSON><PERSON> cancel cooking task after task begining'
 * case 12: L<PERSON>E 317 - <PERSON><PERSON> cancel cooking task before task begining less than 1 hour'
 * case 13: LINE 338 - <PERSON><PERSON> cancel cooking task before task begining 2 hours'
 * case 14: LINE 359 - <PERSON><PERSON> cancel CONFIRMED cooking task because Tasker request - Before 8 hours when task is started'
 */
const {
  initData,
  tapId,
  tapText,
  typeToTextField,
  expectElementVisible,
  expectElementNotExist,
  swipe,
  waitForElement,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASK_01 = {
  isoCode: 'VN',
  serviceName: 'HOME_COOKING',
  askerPhone: ASKER.phone,
  description: 'Task 01',
};
const TASK_02 = {
  isoCode: 'VN',
  serviceName: 'HOME_COOKING',
  askerPhone: ASKER.phone,
  description: 'Task 02',
};
const TASK_03 = {
  isoCode: 'VN',
  serviceName: 'HOME_COOKING',
  askerPhone: ASKER.phone,
  description: 'Task 03',
};
const TASK_04 = {
  isoCode: 'VN',
  serviceName: 'HOME_COOKING',
  askerPhone: ASKER.phone,
  description: 'Task 04',
};
const F_MAIN_ACCOUNT = 1_000_000;

describe('FILE: e2e/a-vietnam/flow-test/home-cooking/cancel-task/asker-cancel-task.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK_01, TASK_02, TASK_03, TASK_04]);
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: [TASKER.phone],
        description: TASK_01.description,
      },
      {
        isoCode: 'VN',
        status: 'CONFIRMED',
        taskerAccepted: [TASKER.phone],
        description: TASK_02.description,
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: F_MAIN_ACCOUNT },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 104 - Asker cancel posted task', async () => {
    await tapId('Tab_Activity');
    await swipe('scrollUpcoming', 'up');
    await tapId('TAB_UPCOMINGTask 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await expectElementVisible('btnOtherReason');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGTask 04');
    const task = await initData('task/getTaskByDescription', { description: 'Task 04', isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 129 - Asker cancel waiting cooking task', async () => {
    await tapId('Tab_Activity');
    await tapId('TAB_UPCOMINGTask 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGTask 01');
  });

  it('LINE 149 - Asker cancel confirmed cooking task before working time', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK_04.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASK_01.phone], isoCode: 'VN' },
    ]);
    await tapId('Tab_Activity');
    await tapId('TAB_UPCOMINGTask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await expectElementNotExist('Tasker tự ý không đến.', 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Hủy việc');
    await tapText('Đồng ý');

    const task = await initData('task/getTaskByDescription', { description: TASK_02.description, isoCode: 'VN' });
    expect(task.status).to.equal('CANCELED');

    const notify = await initData('notification/get-notification', { isoCode: 'VN', phone: TASKER.phone, type: 30 });
    expect(notify.length).to.equal(1);

    const data1 = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: 'VN',
      taskDescription: TASK_03.description,
    });
    expect(data1.length).to.equal(0);

    const data2 = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: 'VN',
      taskDescription: TASK_04.description,
    });
    expect(data2.length).to.equal(0);
  });

  it('LINE 208 - Asker cancel confirmed cooking task before working time, find same task for Tasker', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'VN' },
    ]);
    await tapId('Tab_Activity');
    await tapId('TAB_UPCOMINGTask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');

    const data = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: 'VN',
      taskDescription: TASK_03.description,
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: 'VN',
      taskDescription: TASK_04.description,
    });
    expect(data1.length).to.equal(0);
    await expectElementNotExist('tasktask 02');
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: TASK_02.description });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 271 - Asker cancel confirmed cooking task after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK_04.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          date: moment().add(15, 'minutes').toDate(),
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'VN' },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_02.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_02.description}`);
  });

  it('LINE 318 - Asker cancel confirmed cooking task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK_01.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_01.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_01.description}`);
    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 20000,
      isoCode: 'VN',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(20000);
    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data2.FMainAccount).to.equal(F_MAIN_ACCOUNT - 20000);
    expect(data2.Promotion).to.equal(0);
  });

  it('LINE 363 - Asker cancel waiting cooking task with fee 0k', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
          progress: 'WAITING',
          status: 'WAITING_ASKER_CONFIRMATION',
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_01.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_01.description}`);
  });

  it('LINE 395 - Asker cancel posted task with free charge', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_04.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
          progress: 'WAITING',
          status: 'POSTED',
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_04.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_04.description}`);
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 430 - Asker cancel confirmed cooking task with reason Tasker not comming free', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK_01.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(3, 'hour').toDate(),
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_01.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible(`TAB_UPCOMING${TASK_01.description}`);
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 476 - Asker cancel task and input the reason', async () => {
    await tapId('Tab_Activity');
    await swipe('scrollUpcoming', 'up');
    await tapId(`TAB_UPCOMING${TASK_04.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapId('btnOtherReason');
    await typeToTextField('inputOtherReason', 'I cancel');
    // tab 2 lần tắt bàn phím
    try {
      await tapId('txtOtherReason');
      await tapId('btnOKChooseReason');
    } catch (error) {}
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_04.description}`);
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'Task 04' });
    expect(task.cancellationText).to.equal('I cancel');
  });

  it('LINE 501 - Asker cancel cooking task after task begining', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(5, 'minutes').toDate(),
        },
      },
    ]);
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK_01.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_01.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_01.description}`);
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 45000);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 539 - Asker cancel cooking task before task begining less than 1 hour', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK_02.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(1, 'hour').toDate(),
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_02.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_02.description}`);
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 45_000);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 577 - Asker cancel cooking task before task begining 2 hours', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK_02.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(6, 'hour').toDate(),
          progress: 'BEFORE_WORKING_2H',
          status: 'CONFIRMED',
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_02.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist(`TAB_UPCOMING${TASK_02.description}`);
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 20_000);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 615 - Asker cancel CONFIRMED cooking task because Tasker request - Before 8 hours when task is started', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'VN' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId(`TAB_UPCOMING${TASK_02.description}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible(`TAB_UPCOMING${TASK_02.description}`);
  });
});
