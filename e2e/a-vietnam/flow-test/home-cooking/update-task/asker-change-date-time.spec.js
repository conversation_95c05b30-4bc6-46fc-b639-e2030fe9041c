/**
 * @description Asker change date time task home cooking
 *   case 1: <PERSON><PERSON> post task and change date and time to meal DINNER from 17:00 to 19:30
 *   case 2: Asker update task and change date and time to meal DINNER from 17:00 to 19:30
 *   case 3: Ask<PERSON> post task and change date and time in limited time (10:00 - 19:30)
 * */

const {
  initData,
  tapId,
  expectIdToHaveText,
  waitForElement,
  tapText,
  selectTime24h,
  typeToTextFieldDishName,
  swipe,
  tapIdService,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const { device } = require('detox');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
describe('FILE: e2e/a-vietnam/flow-test/home-cooking/update-task/asker-change-date-time.spec.js - Asker change date time task home cooking', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: ASKER.phone,
        description: 'Don dep nha 01',
        status: 'DONE',
        rated: true,
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 47 - Asker post task and change date and time to meal DINNER from 17:00 to 19:30', async () => {
    await tapIdService('postTaskServiceHOME_COOKING');
    await expectIdToHaveText('lbPrice', '150,000 VND/2h');
    await typeToTextFieldDishName();
    await tapId('btnNextStep2');
    await selectTime24h(18, 12);
    await tapId('btnOkTime');
    await expectIdToHaveText('lbPriceStep3', '180,000 VND/2h');
    await selectTime24h(19, 18);
    await tapId('btnOkTime');
    await expectIdToHaveText('lbPriceStep3', '180,000 VND/2h');
    await tapId('weekdays_2');
    await expectIdToHaveText('lbPriceStep3', '180,000 VND/2h');
    await tapId('weekdays_3');
    await expectIdToHaveText('lbPriceStep3', '180,000 VND/2h');
    await selectTime24h(16, 19);
    await tapId('btnOkTime');
    await expectIdToHaveText('lbPriceStep3', '150,000 VND/2h');
  });

  it('LINE 72 - Asker update task and change date and time to meal DINNER from 17:00 to 19:30', async () => {
    await tapIdService('postTaskServiceHOME_COOKING');
    await typeToTextFieldDishName();
    if (device.getPlatform() === 'ios') {
      await tapId('btnNextStep2');
    }
    await tapId('btnNextStep3');
    await waitForElement('btnSubmitPostTask', 1000);
    await tapId('btnSubmitPostTask');
    await tapText('Theo dõi công việc');
    const data = await initData('user/getUserByPhone', { phone: '0834567890' });
    expect(data.cities[0].country).to.equal('VN');
    expect(data.cities[0].city).to.equal('Hồ Chí Minh');
    await tapId('TAB_UPCOMING_My Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(18, 10);
    await tapId('btnOkTime');
    await expectIdToHaveText('lbPrice', '180,000 VND/2h');
    await selectTime24h(19, 18);
    await tapId('btnOkTime');
    await expectIdToHaveText('lbPrice', '180,000 VND/2h');
    await tapId('weekdays_2');
    await expectIdToHaveText('lbPrice', '180,000 VND/2h');
    await tapId('weekdays_3');
    await expectIdToHaveText('lbPrice', '180,000 VND/2h');
    await selectTime24h(18, 21);
    await tapId('btnOkTime');
    await expectIdToHaveText('lbPrice', '150,000 VND/2h');
  });

  it('LINE 110 - Asker post task and change date and time in limited time (10:00 - 19:30)', async () => {
    await tapIdService('postTaskServiceHOME_COOKING');
    await typeToTextFieldDishName();
    if (device.getPlatform() === 'ios') {
      await tapId('btnNextStep2');
    }
    await selectTime24h(9, 12);
    await tapId('btnOkTime');
    await tapId('btnNextStep3');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 10:00 đến 19:30 hàng ngày.',
      1000,
      'text',
    );
    await tapText('Đóng');
    await selectTime24h(20, 9);
    await tapId('btnOkTime');
    await tapId('btnNextStep3');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 10:00 đến 19:30 hàng ngày.',
      1000,
      'text',
    );
    await tapText('Đóng');
    await tapId('weekdays_2');
    await tapId('btnNextStep3');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 10:00 đến 19:30 hàng ngày.',
      1000,
      'text',
    );
    await tapText('Đóng');
    await tapId('weekdays_3');
    await tapId('btnNextStep3');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 10:00 đến 19:30 hàng ngày.',
      1000,
      'text',
    );
    await tapText('Đóng');
  });
});
