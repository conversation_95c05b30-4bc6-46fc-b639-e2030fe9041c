/**
 * @description Asker book task with favorite Tasker
 *   case 1: Asker booking in chat with favorite Tasker do not have any task confirm
 *   case 2: Asker booking 1 option in chat with favorite Tasker do not have any task confirm
 *   case 3: Asker chat with favorite Tasker have conflict time
 *   case 4: Asker cancel task posted with favorite Tasker
 *   case 5: Ask<PERSON> cancel task confirmed with favorite Tasker
 */

const {
  initData,
  tapId,
  tapText,
  waitForLoading,
  expectElementVisible,
  callService,
  scroll,
  expectElementNotVisible,
  swipe,
  expectElementNotExist,
  selectTime24h,
  waitForElement,
} = require('../../../step-definition');

const { E2EHelpers } = require('../../../e2e.helpers');
const expect = require('chai').expect;
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const ASKER_02 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASKER = {
  _id: 'tasker_0834567891',
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker Favorite',
  type: 'TASKER',
  services: ['CLEANING'],
  status: 'ACTIVE',
  oldUser: true,
};

describe('FILE: e2e/a-vietnam/flow-test/chat/book-task-with-favorite-tasker.spec.js - Asker book task with favorite Tasker', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER, ASKER_02]);
    await initData('user/updateUser', [
      {
        phone: ASKER.phone,
        dataUpdate: {
          favouriteTasker: ['tasker_0834567891'],
        },
        isoCode: ASKER.isoCode,
      },
    ]);
    await initData('user/updateUser', [
      {
        phone: TASKER.phone,
        dataUpdate: {
          workingPlaces: [
            {
              country: 'VN',
              city: 'Hồ Chí Minh',
              district: 'Quận 1',
            },
            {
              country: 'VN',
              city: 'Hồ Chí Minh',
              district: 'Quận 2',
            },
            {
              country: 'VN',
              city: 'Hồ Chí Minh',
              district: 'Quận 10',
            },
          ],
        },
        isoCode: TASKER.isoCode,
      },
    ]);
    await initData('service/updateServiceChannel', [
      { isoCode: 'VN', serviceName: 'CLEANING', taskerPhone: TASKER.phone, isRemove: false },
    ]);
    await initData('update-user/financialAccount', {
      phone: TASKER.phone,
      isoCode: 'VN',
      financialAccountData: { Promotion: 100000 },
    });

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 107 - Asker booking in chat with favorite Tasker do not have any task confirm', async () => {
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await waitForElement('btnBookBowAlert', 500);
    await tapId('btnBookBowAlert');
    await expectElementVisible('Tasker Favorite', 'text');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Thêm khung giờ');
    await tapText('13:00');
    await tapText('10:00');
    await tapText('Xác nhận');
    await expectElementVisible('Khung giờ 1', 'text');
    await expectElementVisible('Khung giờ 2', 'text');
    await expectElementVisible('Khung giờ 3', 'text');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 600, 'down', 0.5, 0.5);
    await expectElementVisible('step4DateOptions_0');
    await expectElementVisible('step4DateOptions_1');
    await expectElementVisible('step4DateOptions_2');
    await tapText('Đăng việc');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'My Task' });
    expect(task.forceTasker.taskerId).to.equal('tasker_0834567891');
    expect(task.dateOptions.length).to.equal(3);
    await expectElementVisible('Đang đợi Tasker Favorite nhận việc...', 'text');
    await tapId('TAB_UPCOMINGMy Task');
    await tapText('Gửi công việc cho Tasker khác');
    await tapText('Xác nhận');
    await tapText('Xác nhận');
    await tapText('Đóng');
    const taskAfterUpdate = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'My Task' });
    expect(taskAfterUpdate.forceTasker).to.equal(undefined);
    expect(taskAfterUpdate.dateOptions).to.equal(undefined);
  });

  it('LINE 92 - Asker booking 1 option in chat with favorite Tasker do not have any task confirm', async () => {
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await waitForElement('btnBookBowAlert', 500);
    await tapId('btnBookBowAlert');
    await expectElementVisible('Tasker Favorite', 'text');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Không, cảm ơn');
    await expectElementNotVisible('Khung giờ 1', 'text');
    await scroll('scrollViewStep4', 500, 'down', 0.5, 0.5);
    await expectElementNotVisible('step4DateOptions_0');
    await tapText('Đăng việc');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'My Task' });
    expect(task.forceTasker.taskerId).to.equal('tasker_0834567891');
    expect(task.dateOptions).to.equal(undefined);
    await tapId('TAB_UPCOMINGMy Task');
    await tapText('Gửi công việc cho Tasker khác');
    await tapText('Xác nhận');
    await tapText('Đóng');
    const taskAfterUpdate = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'My Task' });
    expect(taskAfterUpdate.forceTasker).to.equal(undefined);
    expect(taskAfterUpdate.dateOptions).to.equal(undefined);
  });

  it('LINE 173 - Asker chat with favorite Tasker have conflict time', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: ASKER_02.phone,
        description: 'My Task 01',
        status: 'POSTED',
        visibility: 3,
        viewedTaskers: [TASKER.phone],
        autoChooseTasker: true,
      },
    ]);
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'My Task 01' });
    const tasker = await initData('user/getUserByPhone', { phone: TASKER.phone });
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
    };
    const response = await callService('/v3/accept-task-vn/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await waitForElement('btnBookBowAlert', 500);
    await tapId('btnBookBowAlert');
    await expectElementVisible('Tasker Favorite', 'text');
    await tapId('btnNextStep2');
    await tapId('weekdays_2');
    await expectElementNotVisible('btnNextStep3');
    await expectElementVisible(
      'Rất tiếc! Khung giờ trên đã trùng với lịch làm việc của Tasker. Vui lòng xem lịch làm việc của Tasker bên dưới và chọn khung giờ khác.',
      'text',
    );
    await tapText('Xem lịch làm việc của Tasker');
    await tapText('08:00');
    await tapText('09:00');
    await tapText('10:00');
    await tapText('Xác nhận');
    await expectElementVisible('Khung giờ 1', 'text');
    await expectElementVisible('Khung giờ 2', 'text');
    await expectElementVisible('Khung giờ 3', 'text');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 600, 'down', 0.5, 0.5);
    await expectElementVisible('step4DateOptions_0');
    await expectElementVisible('step4DateOptions_1');
    await expectElementVisible('step4DateOptions_2');
    await tapText('Đăng việc');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    const task2 = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'My Task' });
    expect(task2.forceTasker.taskerId).to.equal('tasker_0834567891');
    expect(task2.dateOptions.length).to.equal(3);
  });

  it('LINE 210 - Asker cancel task posted with favorite Tasker', async () => {
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await waitForElement('btnFavoriteTasker_0', 500);
    await tapId('btnFavoriteTasker_0');
    await waitForElement('btnBookNow', 500);
    await tapId('btnBookNow');
    await waitForElement('btnBookBowAlert', 500);
    await tapId('btnBookBowAlert');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Thêm khung giờ');
    await tapText('13:00');
    await tapText('10:00');
    await tapText('Xác nhận');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 500, 'down', 0.5, 0.5);
    await tapText('Đăng việc');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    await tapId('TAB_UPCOMINGMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnCancelTaskFav');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGMy Task');
  });

  it('LINE 244 - Asker cancel task confirmed with favorite Tasker', async () => {
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await tapId('btnFavoriteTasker_0');
    await tapId('btnBookNow');
    await waitForElement('btnBookBowAlert', 500);
    await tapId('btnBookBowAlert');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Thêm khung giờ');
    await tapText('13:00');
    await tapText('10:00');
    await tapText('Xác nhận');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 500, 'down', 0.5, 0.5);
    await tapText('Đăng việc');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'VN',
        dataUpdate: {
          viewedTaskers: ['tasker_0834567891'],
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'My Task' });
    const request = {
      taskId: task._id,
      taskerId: 'tasker_0834567891',
      dateOptionId: task.dateOptions[0]._id,
    };
    const response = await callService('/v3/accept-task-vn/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await tapId('TAB_UPCOMINGMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGMy Task');
  });

  it('LINE 313 - Asker booking in chat with favorite Tasker normal', async () => {
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await waitForElement('btnBookBowAlert', 500);
    await tapId('btnBookBowAlert');
    await expectElementVisible('Tasker Favorite', 'text');
    await expectElementNotExist('choosePremiumDescription');
    await expectElementNotExist('chooseServies-2');
  });

  it('LINE 327 - Asker booking in chat with favorite Tasker Premium', async () => {
    await initData('user/updateUser', [
      {
        phone: TASKER.phone,
        dataUpdate: {
          cleaningOption: {
            hasCleaningKit: true,
          },
          isPremiumTasker: true,
        },
        isoCode: ASKER.isoCode,
      },
    ]);
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await waitForElement('btnBookBowAlert', 500);
    await tapId('btnBookBowAlert');
    await expectElementVisible('Tasker Favorite', 'text');
    await expectElementVisible('choosePremiumDescription');
    await scroll('scrollStep2Cleaning', 500, 'down', 0.5, 0.5);
    await expectElementVisible('chooseServies-2');
  });

  it('LINE 356 - Asker booking in chat with favorite Tasker 6h', async () => {
    const dateUpdate = moment().add(1, 'hour').toDate();
    const now = moment().toDate();

    // Test chọn cận giờ nên sẽ bị fail vào buổi tối hoặc trước 14h
    if (now.getHours() > 14 && now.getHours() < 17) {
      await waitForLoading('Tài khoản', 500, 'text');
      await tapText('Tài khoản');
      await tapText('Tasker yêu thích');
      await expectElementVisible('btnFavoriteTasker_0');
      await tapId('btnFavoriteTasker_0');
      await expectElementVisible('btnBookNow');
      await tapId('btnBookNow');
      await waitForElement('btnBookBowAlert', 500);
      await tapId('btnBookBowAlert');
      await expectElementVisible('Tasker Favorite', 'text');
      await tapId('btnNextStep2');
      await tapId('weekdays_0');

      await selectTime24h(dateUpdate.getHours(), now.getHours());
      await tapText('Đồng ý');
      await tapId('btnNextStep3');
      await waitForElement(
        'Bạn cần đặt trước giờ làm tối thiểu 3 tiếng để Tasker Yêu Thích có đủ thời gian nhận việc. Vui lòng chọn các khung giờ khác.',
        500,
        'text',
      );
    }
  });
});
