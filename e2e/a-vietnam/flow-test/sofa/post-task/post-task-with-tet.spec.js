/**
 * @description Post task choose option premium
 *   case 1: LINE 51 - Ask<PERSON> choose option premium
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,

  swipe,
  postTask,
  expectElementVisible,
  tapHeaderBack,
  tapTextAtIndex,
  expectElementNotVisible,
  typeToTextFieldSubmitKeyboard,
  expectIdToHaveTextAtIndex,
  tapIdAtIndex,
  waitForLoading,
  waitForElement,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
describe('FILE: e2e/a-vietnam/flow-test/sofa/post-task/post-task-with-tet.spec.js - Post task Tet service', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('event-config/create-data', [{ isoCode: 'VN', services: ['SofaCleaning'] }]);
    await initData('service/update-fields', {
      isoCode: 'VN',
      serviceName: 'SofaCleaning',
      dataUpdate: {
        tetBookingDates: {
          bookTaskTime: {
            fromDate: moment().add(2, 'days').toDate(),
            toDate: moment().add(32, 'days').toDate(),
          },
          fromDate: moment().subtract(1, 'days').toDate(),
          toDate: moment().add(60, 'days').toDate(),
        },
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 34 - Asker choose option tet', async () => {
    await postTask('postTaskServiceTetSofaCleaning');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await tapId('address1');

    await tapId('Tab_Mattress');

    // Select type 1: nho hon 1.5m
    await tapId('check_box_1_mattress'); // id: check_box_[type]_[from]
    await typeToTextFieldSubmitKeyboard('input_note_1_mattress', 'nem mini ne');
    await expectIdToHaveTextAtIndex('lbPrice', '300,000 VND', 0);

    // Select type 2: tu 1.5m den 1.8m
    await tapId('check_box_2_mattress'); // id: check_box_[type]_[from]
    await expectIdToHaveTextAtIndex('lbPrice', '650,000 VND', 0);

    await tapIdAtIndex('btnNextStep2', 0);
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('bPay', 'text');
  });

  it('LINE 76 - Asker change date tet booking', async () => {
    await postTask('postTaskServiceTetSofaCleaning');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await tapId('address1');

    await tapId('Tab_Mattress');

    // Select type 1: nho hon 1.5m
    await tapId('check_box_1_mattress'); // id: check_box_[type]_[from]
    await typeToTextFieldSubmitKeyboard('input_note_1_mattress', 'nem mini ne');
    await expectIdToHaveTextAtIndex('lbPrice', '300,000 VND', 0);

    // Select type 2: tu 1.5m den 1.8m
    await tapId('check_box_2_mattress'); // id: check_box_[type]_[from]
    await expectIdToHaveTextAtIndex('lbPrice', '650,000 VND', 0);

    await tapIdAtIndex('btnNextStep2', 0);
    await tapId('datePickerTet');

    const nextMonday = moment().add(10, 'days').date();
    await waitForLoading(500);
    try {
      await tapTextAtIndex(nextMonday.toString(), 0);
    } catch (error) {
      await tapTextAtIndex(nextMonday.toString(), 1);
    }
    await tapText('Đồng ý');

    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await swipe('scrollViewStep4', 'up');
    await tapText('Đăng việc');
    await waitForElement(
      'Bạn cần thêm 650,000 VND để có thể đăng công việc này. Vui lòng nạp thêm hoặc chọn hình thức thanh toán khác.',
      500,
      'text',
    );
    await tapText('Đóng');
    await tapId('choosePaymentMethod');
    await expectElementNotVisible('Tiền mặt', 'text');
    await tapHeaderBack();
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      financialAccountData: { FMainAccount: 5000000 },
    });
    await tapText('Đăng việc');

    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '650,000 ₫');
    await expectIdToHaveText('paymentMethod', 'bPay');
  });
});
