/**
 * @description Asker cancel Deep Cleaning task thailand
 *   case 1: <PERSON><PERSON> cancel posted task
 *   case 2: Ask<PERSON> cancel posted task (Max fee
 *   case 3: Ask<PERSON> cancel confirmed deep cleaning task before working time (max fee
 *   case 4: Ask<PERSON> cancel confirmed deep cleaning task with fee 20k
 *   case 5: Ask<PERSON> cancel waiting deep cleaning task with fee 0k
 *   case 6: Asker cancel posted task with free charge
 *   case 7: Ask<PERSON> cancel posted task
 * */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  waitForElement,
  swipe,
} = require('../../../step-definition');
const expect = require('chai').expect;
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASKER2 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 9,
};

const TASK1 = {
  isoCode: 'VN',
  serviceName: 'DISINFECTION_SERVICE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};
const TASK2 = {
  isoCode: 'VN',
  serviceName: 'DISINFECTION_SERVICE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 02',
};
const TASK3 = {
  isoCode: 'VN',
  serviceName: 'DISINFECTION_SERVICE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 03',
};
const TASK4 = {
  isoCode: 'VN',
  serviceName: 'DISINFECTION_SERVICE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 04',
};

const F_MAIN_ACCOUNT = 1000000;

describe('FILE: e2e/a-vietnam/flow-test/desinfection/cancel-task.spec.js - Asker cancel Deep Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);
    await initData('task/createTask', [TASK1, TASK2, TASK3, TASK4]);
    await initData('update-user/financialAccount', {
      phone: '**********',
      isoCode: 'VN',
      financialAccountData: { FMainAccount: F_MAIN_ACCOUNT },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 83 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { description: TASK4.description, isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 112 - Asker cancel confirmed deep cleaning task before working time (max fee)', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK2.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          progress: 'BEFORE_WORKING_50M',
          status: 'CONFIRMED',
          costDetail: {
            finalCost: 5000000,
            baseCost: 5000000,
            cost: 5000000,
          },
          cost: 5000000,
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
  });

  it('LINE 154 - Asker cancel confirmed deep cleaning task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK1.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');

    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 20000,
      isoCode: 'VN',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(20000);

    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data2.FMainAccount).to.equal(F_MAIN_ACCOUNT - 20000);
    expect(data2.Promotion).to.equal(0);
  });
});
