const { SERVICE_NAME } = require('../../../../helpers/constants');
const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  callService,
  expectIdToHaveTextAtIndex,
  tapHeaderBack,
  expectElementNotVisible,
  swipe,
  waitForLoading,
} = require('../../../../step-definition');
const moment = require('moment');
const expect = require('chai').expect;
const { device } = require('detox');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const COMPANY = {
  _id: 'userId_**********',
  isCompany: true,
  phone: '**********',
  isoCode: 'VN',
  type: 'TASKER',
  name: 'Tasker V<PERSON>',
  services: SERVICE_NAME.INDUSTRIAL_CLEANING,
  FMainAccount: 500000,
  Promotion: 5000,
};

const TASKER = {
  _id: 'userId_**********',
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  company: COMPANY.phone,
  oldUser: true,
  FMainAccount: 1000000,
  Promotion: 1000000,
};

const TASK1 = {
  isoCode: 'VN',
  serviceName: SERVICE_NAME.INDUSTRIAL_CLEANING,
  askerPhone: ASKER.phone,
  description: 'My Task',
  viewedTaskers: [COMPANY.phone, TASKER.phone],
};

const NEW_TASK_DETAIL = {
  homeType: {
    name: 'HOME',
    text: {
      vi: 'Nhà ở',
      en: 'Residential',
      ko: '주택',
      th: 'พื้นที่พักอาศัย',
      id: 'Perumahan',
    },
    type: {
      name: 'newHouse',
      text: {
        vi: 'Nhà mới xây/ sửa chữa',
        en: 'Newly built/ renovated house',
        ko: '신축/리모델링된 주택',
        th: 'บ้านที่สร้าง/ปรับปรุงใหม่',
        id: 'Rumah yang baru dibangun/renovasi',
      },
      area: {
        name: 'area2',
        text: {
          vi: '60 ~ 80 m2',
          en: '60 ~ 80 m2',
          ko: '60 ~ 80 m2',
          th: '60 ~ 80 m2',
          id: '60 ~ 80 m2',
        },
      },
      optional: [
        {
          name: 'hasFurniture',
          text: {
            vi: 'Nhà có nội thất',
            en: 'House with furniture',
            ko: '가구가 있는 주택',
            th: 'บ้านที่มีเฟอร์นิเจอร์',
            id: 'Rumah dengan furnitur',
          },
        },
      ],
    },
  },
};

describe('FILE: e2e/a-vietnam/flow-test/industrial-cleaning/chat-actions/tasker-request-task-detail.spec.js - Tasker request task detail AC', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER, COMPANY]);

    await initData('user/updateUser', {
      phone: COMPANY.phone,
      isoCode: COMPANY.isoCode,
      employees: [TASKER.phone],
    });
    await initData('task/createTask', [TASK1]);
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          date: moment().add(10, 'minutes').toDate(),
          detailIndustrialCleaning: {
            homeType: {
              name: 'HOME',
              text: {
                vi: 'Nhà ở',
                en: 'Residential',
                ko: '주택',
                th: 'พื้นที่พักอาศัย',
                id: 'Perumahan',
              },
              type: {
                name: 'oldHouse',
                text: {
                  vi: 'Nhà lâu năm',
                  en: 'Long-standing house',
                  ko: '오래된 집',
                  th: 'บ้านเก่า',
                  id: 'Rumah yang sudah lama berdiri',
                },
                area: {
                  name: 'area1',
                  text: {
                    vi: '<60 m2',
                    en: '<60 m2',
                    ko: '<60 m2',
                    th: '<60 m2',
                    id: '<60 m2',
                  },
                },
              },
            },
          },
        },
      },
    ]);
    await initData('service/updateServiceChannel', [
      { isoCode: COMPANY.isoCode, serviceName: SERVICE_NAME.INDUSTRIAL_CLEANING, taskerPhone: COMPANY.phone },
      { isoCode: TASKER.isoCode, serviceName: SERVICE_NAME.INDUSTRIAL_CLEANING, taskerPhone: TASKER.phone },
    ]);
    await initData('update-user/financialAccount', {
      phone: TASKER.phone,
      isoCode: TASKER.isoCode,
      financialAccountData: { Promotion: 100000 },
    });

    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body1 = {
      taskId: task._id,
      taskerId: TASKER._id,
      companyId: COMPANY._id,
    };
    const response1 = await callService('/v3/accept-task-vn/accept', body1);
    expect(response1.status).to.equal('CONFIRMED');
    await E2EHelpers.onHaveLogin(ASKER.phone);
  });

  it('LINE 182 - Asker approve case Task Cash', async () => {
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: TASKER._id,
      taskId: task._id,
      newTaskDetail: NEW_TASK_DETAIL,
      action: 'UPDATE_DETAIL',
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('txtRequestUpdateTask');
    await expectElementVisible('extraMoneyTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await waitForLoading(1000);
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);

    //Xem history
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          status: 'DONE',
          date: moment().add(-1, 'days').toDate(),
        },
      },
    ]);
    device.reloadReactNative();
    await tapHeaderBack();
    await tapText('Hoạt động');
    await tapId('btnTaskHistory');
    await tapId('taskMy Task');
    await swipe('scrollHistoryDetail', 'up');
    await tapId('seeChatHistory');
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });

  it('LINE 228 - Asker reject', async () => {
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      newTaskDetail: NEW_TASK_DETAIL,
      action: 'UPDATE_DETAIL',
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');

    await expectElementVisible('txtRequestUpdateTask');
    await expectElementVisible('extraMoneyTxt');
    await tapId('rejectUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('rejectUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Rất tiếc vì bạn đã từ chối', 1);

    //Xem history
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          status: 'DONE',
          date: moment().add(-1, 'days').toDate(),
        },
      },
    ]);
    device.reloadReactNative();
    await tapHeaderBack();
    await tapText('Hoạt động');
    await tapId('btnTaskHistory');
    await tapId('taskMy Task');
    await swipe('scrollHistoryDetail', 'up');
    await tapId('seeChatHistory');
    await expectElementNotVisible('rejectUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Rất tiếc vì bạn đã từ chối', 1);
  });

  it('LINE 274 - Asker approve case Task bPay', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      financialAccountData: { FMainAccount: 1000000 },
    });
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          payment: {
            method: 'CREDIT',
          },
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      newTaskDetail: NEW_TASK_DETAIL,
      action: 'UPDATE_DETAIL',
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await waitForLoading(1000);
    await expectElementVisible('txtRequestUpdateTask');
    await expectElementVisible('extraMoneyTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementVisible('Thanh toán thêm', 'text');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });

  it('LINE 317 - Asker approve case Task bPay but enough money and recharge bPay', async () => {
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          payment: {
            method: 'CREDIT',
          },
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      newTaskDetail: NEW_TASK_DETAIL,
      action: 'UPDATE_DETAIL',
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('txtRequestUpdateTask');
    await expectElementVisible('extraMoneyTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementVisible('Thanh toán thêm', 'text');
    await tapId('btnActionAlert0'); //Tap nút Nạp thêm
    await expectElementVisible('Nạp thêm', 'text');
    await tapHeaderBack();
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementVisible('Thanh toán thêm', 'text');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });

  it('LINE 360 - Asker approve case Task prepay', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      financialAccountData: { FMainAccount: 1000000 },
    });
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          payment: {
            method: 'MOMO',
            status: 'PAID',
          },
          isPrepayTask: true,
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      newTaskDetail: NEW_TASK_DETAIL,
      action: 'UPDATE_DETAIL',
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('txtRequestUpdateTask');
    await expectElementVisible('extraMoneyTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });
});
