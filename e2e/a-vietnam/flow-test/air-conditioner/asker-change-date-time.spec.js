/* eslint-disable no-undef */
/**
 * @description Asker change date time AC task (3 test cases)
 *   case 1: <PERSON>INE 33 - Ask<PERSON> want to change task datetime - POSTED
 *   case 2: LINE 63 - Asker want to change task datetime - WAITING
 *   case 3: LINE 93 - Asker want to change task datetime - CONFIRMED
 * */
const { E2E<PERSON>elpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  selectTime24h,
  swipe,
  postTask,
  waitForElement,
  typePromotionCode,
  ADDRESS_KEY,
} = require('../../../step-definition');

describe('FILE: e2e/a-vietnam/flow-test/air-conditioner/asker-change-date-time.spec.js - Change datetime Deep Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      { isoCode: 'VN', phone: '0834567890', name: 'Asker', type: 'ASKER', status: 'ACTIVE' },
      { isoCode: 'VN', phone: '0834567891', name: 'Tasker 01', type: 'TASKER', status: 'ACTIVE' },
      { isoCode: 'VN', phone: '0834567892', name: 'Tasker 02', type: 'TASKER', status: 'ACTIVE' },
      { isoCode: 'VN', phone: '0834567893', name: 'Tasker 03', type: 'TASKER', status: 'ACTIVE' },
    ]);
    await E2EHelpers.onHaveLogin('0834567890', '123456');
  });
  it('LINE 36 - AC Asker want to change task datetime - POSTED', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '0834567890',
        description: 'My Task',
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy lạnh');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
      500,
      'text',
    );
    await tapText('Đóng');
    await selectTime24h(16, 22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('quantityAC_Wall', '2');
  });

  it('LINE 73 - Asker want to change task datetime - WAITING', async () => {
    // TODO: Run on Android
    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: 'VN',
          serviceName: 'AIR_CONDITIONER_SERVICE',
          askerPhone: '0834567890',
          description: 'My Task',
        },
      ]);

      await initData('task/acceptedTask', [
        { status: 'WAITING_ASKER_CONFIRMATION', taskerAccepted: ['0834567891'], description: 'My Task', isoCode: 'VN' },
      ]);
      await tapText('Hoạt động');
      await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy lạnh');
      await expectIdToHaveText('taskStatusMy Task', 'Chờ xác nhận');
      await expectIdToHaveText('taskerName0', 'Tasker 01');
      await tapId('taskMy Task');
      await expectElementVisible('tasker-Tasker 01');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await tapId('btnGoToUpdateDateTime');
      await selectTime24h(16);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');
      await tapId('taskMy Task');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
      await expectIdToHaveText('quantityAC_Wall', '2');
    }
  });

  it('LINE 100 - Asker want to change air conditioner task time include promotion', async () => {
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'VN',
        code: 'abc123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: '',
      },
    ]);
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', ADDRESS_KEY.HCM);
    // post task step 1
    await tapText('Dưới 2HP');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');

    // Áp dụng mã khuyến mãi giảm 50,000
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectIdToHaveText('txtPromotionCode', 'abc123');

    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy lạnh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(17);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
    await tapId('taskMy Task');
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '216,000 ₫');
    await expectIdToHaveText('discount', '-50,000 ₫');
    await expectIdToHaveText('finalCost', '166,000 ₫');
  });
});
