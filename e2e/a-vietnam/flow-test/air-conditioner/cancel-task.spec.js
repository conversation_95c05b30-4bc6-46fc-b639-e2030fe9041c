/**
 * @description Asker cancel task (7 test cases)
 *   case 1: <PERSON><PERSON><PERSON> 33 - <PERSON><PERSON> cancel posted task
 *   case 2: <PERSON><PERSON><PERSON> 56 Ask<PERSON> cancel task - WAITING
 *   case 3: <PERSON><PERSON><PERSON> 77 - <PERSON><PERSON> cancel task - Confirmed task before working time
 *   case 4: <PERSON><PERSON><PERSON> 118 - Ask<PERSON> cancel confirmed cleaning task before working time, find same task for Tasker
 *   case 5: <PERSON><PERSON><PERSON> 147 - Ask<PERSON> cancel confirmed cleaning task after task began 15 minutes
 *   case 6: <PERSON><PERSON><PERSON> 178 - <PERSON><PERSON> cancel confirmed cleaning task with fee 20k
 *   case 7: <PERSON><PERSON><PERSON> 210 - <PERSON><PERSON> cancel waiting cleaning task with fee 0k
 *   case 8: L<PERSON><PERSON> 235 - Ask<PERSON> cancel posted task with free charge
 *   case 9: LINE 263 - <PERSON><PERSON> cancel confirmed cleaning task with reason Tasker not comming free
 *   case 10: LINE 383 - <PERSON><PERSON> cancel cleaning task before task begining 2 hours
 * */
const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  waitForElement,
  tapText,
  expectElementNotExist,
  reloadApp,
} = require('../../../step-definition');

const moment = require('moment');
const expect = require('chai').expect;

const F_MAIN_ACCOUNT = 1000000;
describe('FILE: e2e/a-vietnam/flow-test/air-conditioner/cancel-task.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [
      { isoCode: 'VN', phone: '**********', name: 'Asker', type: 'ASKER', status: 'ACTIVE' },
      { isoCode: 'VN', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);
    await initData('update-user/financialAccount', {
      phone: '**********',
      isoCode: 'VN',
      financialAccountData: { FMainAccount: F_MAIN_ACCOUNT },
    });
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 01',
      },
      {
        isoCode: 'VN',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 02',
      },
      {
        isoCode: 'VN',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 03',
      },
      {
        isoCode: 'VN',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 04',
      },
    ]);
    await initData('task/acceptedTask', [
      {
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: ['**********'],
        description: 'My Task 01',
        isoCode: 'VN',
      },
      { status: 'CONFIRMED', taskerAccepted: ['**********'], description: 'My Task 02', isoCode: 'VN' },
    ]);

    await E2EHelpers.onHaveLogin('**********', '123456');
  });

  it('LINE 83 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await waitForElement('taskMy Task 04', 500);
    await tapId('taskMy Task 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 04');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 04', isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 115 Asker cancel task - WAITING', async () => {
    await tapText('Hoạt động');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 01');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 01', isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 144 - Asker cancel task - Confirmed task before working time', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'My Task 04',
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: '**********', favouriteTasker: ['**********'], isoCode: 'VN' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // await tapText('Đồng ý');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await expectElementNotExist('Tasker tự ý không đến.', 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskMy Task 02');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 02', isoCode: 'VN' });
    expect(task.status).to.equal('POSTED');

    const notify = await initData('notification/get-notification', { isoCode: 'VN', phone: '**********', type: 30 });
    expect(notify.length).to.equal(1);

    const data3 = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'VN',
      taskDescription: 'My Task 02',
    });
    expect(data3.length).to.equal(1);
    expect(data3[0].type).to.equal(30);

    const data1 = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'VN',
      taskDescription: 'My Task 04',
    });
    expect(data1.length).to.equal(0);
  });

  it('LINE 223 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    // await loginWithPhoneAndPassword('**********', '123456');
    await initData('update-user/add-favourite-tasker', [
      { phone: '**********', favouriteTasker: ['**********'], isoCode: 'VN' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');

    const data = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'VN',
      taskDescription: 'My Task 03',
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'VN',
      taskDescription: 'My Task 04',
    });
    expect(data1.length).to.equal(0);

    await expectElementNotExist('taskMy Task 02');
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task 02',
      status: 'CANCELED',
      isoCode: 'VN',
    });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 273 - Asker cancel confirmed cleaning task after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'My Task 04',
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'My Task 02',
        isoCode: 'VN',
        dataUpdate: {
          date: moment().toDate(),
          createdAt: moment().subtract(15, 'minutes').toDate(),
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: '**********', favouriteTasker: ['**********'], isoCode: 'VN' },
    ]);
    // await loginWithPhoneAndPassword('**********', '123456');
    await tapText('Hoạt động');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker tự ý không đến.', 500, 'text');
    await tapText('Tasker tự ý không đến.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 02');
  });

  it('LINE 328 - Asker cancel confirmed cleaning task with fee 20k', async () => {
    // await loginWithPhoneAndPassword('**********', '123456');
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 01');

    const data1 = await initData('user/findFATransaction', {
      phone: '**********',
      accountType: 'M',
      type: 'C',
      amount: 20000,
      isoCode: 'VN',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(20000);

    const data2 = await initData('user/find-faccount', { phone: '**********', isoCode: 'VN' });
    expect(data2.FMainAccount).to.equal(F_MAIN_ACCOUNT - 20000);
    expect(data2.Promotion).to.equal(0);
  });

  it('LINE 384 - Asker cancel waiting cleaning task with fee 0k', async () => {
    // await loginWithPhoneAndPassword('**********', '123456');
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await waitForElement('cancelTask', 500);
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 01');
  });

  it('LINE 417 - Asker cancel posted task with free charge', async () => {
    // await loginWithPhoneAndPassword('**********', '123456');
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'My Task 04', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskMy Task 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 04');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 04');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 451 - Asker cancel confirmed cleaning task with reason Tasker not comming free', async () => {
    // await loginWithPhoneAndPassword('**********', '123456');
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskMy Task 01');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 633 - Asker cancel cleaning task before task begining 2 hours', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'My Task 02', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task 02',
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 02');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 20000);
    expect(data.Promotion).to.equal(0);
  });
});
