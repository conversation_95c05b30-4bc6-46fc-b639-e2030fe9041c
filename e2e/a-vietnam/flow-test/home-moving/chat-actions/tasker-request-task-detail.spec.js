const { SERVICE_NAME } = require('../../../../helpers/constants');
const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  callService,
  expectIdToHaveTextAtIndex,
  tapHeaderBack,
  expectElementNotVisible,
  swipe,
} = require('../../../../step-definition');
const moment = require('moment');
const expect = require('chai').expect;

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: ********,
};

const COMPANY = {
  _id: 'userId_**********',
  isCompany: true,
  phone: '**********',
  isoCode: 'VN',
  type: 'TASKER',
  name: 'Tasker <PERSON>',
  services: SERVICE_NAME.HOME_MOVING,
  FMainAccount: ********,
  Promotion: 5000,
};

const TASKER = {
  _id: 'userId_**********',
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  company: COMPANY.phone,
  oldUser: true,
  FMainAccount: *********,
  Promotion: 1000000,
  serviceName: SERVICE_NAME.HOME_MOVING,
};

const TASK1 = {
  isoCode: 'VN',
  serviceName: SERVICE_NAME.HOME_MOVING,
  askerPhone: ASKER.phone,
  description: 'My Task',
  viewedTaskers: [COMPANY.phone, TASKER.phone],
};

const NEW_TASK_DETAIL = {
  oldHomeDetail: {
    options: [
      {
        name: 'stairsTransport',
        option: {
          name: '1floor',
          text: {
            en: '1 floor',
            id: '1 Lantai',
            ko: '1 층',
            th: '1 ชั้น',
            vi: '1 tầng',
          },
        },
        text: {
          en: 'Staircase transportation',
          id: 'Transportasi melalui Tangga',
          ko: '계단 운송',
          th: 'ขนย้ายผ่านบันได',
          vi: 'Vận chuyển thang bộ',
        },
      },
      {
        name: 'garage',
        text: {
          en: 'Transportation from the parking garage',
          id: 'Transportasi dari garasi parkir',
          ko: '주차장에서 운송',
          th: 'ขนย้ายจากพื้นที่จอดรถ',
          vi: 'Vận chuyển từ hầm xe',
        },
      },
      {
        name: 'byroad',
        text: {
          en: 'House in the alley',
          id: 'Rumah di gang',
          ko: '골목길에 있는 집',
          th: 'พื้นที่อยู่ในซอย',
          vi: 'Nhà trong hẻm',
        },
      },
    ],
    areaOption: {
      description: {
        en: 'Up to 250m2',
        id: 'Hingga 250m2',
        ko: '250 제곱미터까지',
        th: 'สูงสุด 250 ตารางเมตร',
        vi: 'Tối đa 250m2',
      },
      name: '4floor',
      selected: true,
      text: {
        en: '1 ground + 4 floors',
        id: '1 lantai dasar + 4 atas',
        ko: '1 ground + 4 floors',
        th: '1 ชั้นล่างสุด + 4 ชั้น',
        vi: '1 trệt + 4 lầu',
      },
    },
  },
  newHomeDetail: {
    options: [
      {
        name: 'stairsTransport',
        option: {
          name: '1floor',
          text: {
            en: '1 floor',
            id: '1 Lantai',
            ko: '1 층',
            th: '1 ชั้น',
            vi: '1 tầng',
          },
        },
        text: {
          en: 'Staircase transportation',
          id: 'Transportasi melalui Tangga',
          ko: '계단 운송',
          th: 'ขนย้ายผ่านบันได',
          vi: 'Vận chuyển thang bộ',
        },
      },
      {
        name: 'garage',
        text: {
          en: 'Transportation from the parking garage',
          id: 'Transportasi dari garasi parkir',
          ko: '주차장에서 운송',
          th: 'ขนย้ายจากพื้นที่จอดรถ',
          vi: 'Vận chuyển từ hầm xe',
        },
      },
      {
        name: 'byroad',
        text: {
          en: 'House in the alley',
          id: 'Rumah di gang',
          ko: '골목길에 있는 집',
          th: 'พื้นที่อยู่ในซอย',
          vi: 'Nhà trong hẻm',
        },
      },
    ],
  },
  electronicFurnitures: [
    {
      name: 'airConditioner',
      quantity: 3,
      text: {
        en: 'Air conditioner',
        id: 'AC',
        ko: '에어컨',
        th: 'ปรับอากาศ',
        vi: 'Máy lạnh',
      },
    },
    {
      name: 'tanklessWaterHeater',
      quantity: 3,
      text: {
        en: 'Instant water heater',
        id: 'Pemanas air instan',
        ko: '즉시가열 온수기',
        th: 'เครื่องทำความอุ่นแบบทันที',
        vi: 'Máy nước nóng trực tiếp',
      },
    },
    {
      name: 'traditionalWaterHeater',
      quantity: 3,
      text: {
        en: 'Indirect water heater',
        id: 'Pemanas air tidak langsung',
        ko: '간접 온수기',
        th: 'เครื่องทำความอุ่นแบบทางอ้อม',
        vi: 'Máy nước nóng gián tiếp',
      },
    },
  ],
  taskerRequestExtraMoney: {
    extraMoney: 300000,
    reason: 'Reason moi ne',
  },
};

describe('FILE: e2e/a-vietnam/flow-test/home-moving/chat-actions/tasker-request-task-detail.spec.js - Tasker request task detail home moving', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER, COMPANY]);

    await initData('user/updateUser', {
      phone: COMPANY.phone,
      isoCode: COMPANY.isoCode,
      employees: [TASKER.phone],
    });
    await initData('task/createTask', [TASK1]);
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          date: moment().add(10, 'minutes').toDate(),
        },
      },
    ]);
    await initData('service/updateServiceChannel', [
      { isoCode: COMPANY.isoCode, serviceName: SERVICE_NAME.HOME_MOVING, taskerPhone: COMPANY.phone },
      { isoCode: TASKER.isoCode, serviceName: SERVICE_NAME.HOME_MOVING, taskerPhone: TASKER.phone },
    ]);
    await initData('update-user/financialAccount', {
      phone: TASKER.phone,
      isoCode: TASKER.isoCode,
      financialAccountData: { Promotion: 100000 },
    });

    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body1 = {
      taskId: task._id,
      taskerId: TASKER._id,
      companyId: COMPANY._id,
    };

    const response1 = await callService('/v3/accept-task-vn/accept', body1);

    expect(response1.status).to.equal('CONFIRMED');
    await E2EHelpers.onHaveLogin(ASKER.phone);
  });

  it('LINE 36 - Asker approve case Task Cash', async () => {
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          payment: {
            method: 'CASH',
          },
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: TASKER._id,
      taskId: task._id,
      action: 'UPDATE_DETAIL',
      newTaskDetail: NEW_TASK_DETAIL,
    };

    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);

    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('extraMoneyTxt');
    await expectElementVisible('totalAmountTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);

    //Xem history
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          status: 'DONE',
          date: moment().add(-1, 'days').toDate(),
        },
      },
    ]);
    device.reloadReactNative();
    await tapHeaderBack();
    await tapText('Hoạt động');
    await tapId('btnTaskHistory');
    await tapId('taskMy Task');
    await swipe('scrollHistoryDetail', 'up');
    await swipe('scrollHistoryDetail', 'up');
    await tapId('seeChatHistory');
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });

  it('LINE 185 - Asker reject', async () => {
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      action: 'UPDATE_DETAIL',
      newTaskDetail: NEW_TASK_DETAIL,
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('extraMoneyTxt');
    await expectElementVisible('totalAmountTxt');
    await tapId('rejectUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('rejectUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Rất tiếc vì bạn đã từ chối', 1);

    //Xem history
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          status: 'DONE',
          date: moment().add(-1, 'days').toDate(),
        },
      },
    ]);
    device.reloadReactNative();
    await tapHeaderBack();
    await tapText('Hoạt động');
    await tapId('btnTaskHistory');
    await tapId('taskMy Task');
    await swipe('scrollHistoryDetail', 'up');
    await tapId('seeChatHistory');
    await expectElementNotVisible('rejectUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Rất tiếc vì bạn đã từ chối', 1);
  });

  it('LINE 182 - Asker approve case Task bPay', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      financialAccountData: { FMainAccount: 1000000 },
    });
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          payment: {
            method: 'CREDIT',
          },
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      action: 'UPDATE_DETAIL',
      newTaskDetail: NEW_TASK_DETAIL,
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('extraMoneyTxt');
    await expectElementVisible('totalAmountTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementVisible('Thanh toán thêm', 'text');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });

  it('LINE 247 - Asker approve case Task bPay but enough money and recharge bPay', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: 0 },
    });
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          payment: {
            method: 'CREDIT',
          },
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      action: 'UPDATE_DETAIL',
      newTaskDetail: NEW_TASK_DETAIL,
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('extraMoneyTxt');
    await expectElementVisible('totalAmountTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementVisible('Thanh toán thêm', 'text');
    await tapId('btnActionAlert0'); //Tap nút Nạp thêm
    await expectElementVisible('Nạp thêm', 'text');
    await tapHeaderBack();
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementVisible('Thanh toán thêm', 'text');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });

  it('LINE 261 - Asker approve case Task prepay', async () => {
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: TASK1.isoCode,
        dataUpdate: {
          payment: {
            method: 'MOMO',
            status: 'PAID',
          },
          isPrepayTask: true,
        },
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: TASK1.description,
      isoCode: TASK1.isoCode,
    });
    const body = {
      taskerId: COMPANY._id,
      taskId: task._id,
      action: 'UPDATE_DETAIL',
      newTaskDetail: NEW_TASK_DETAIL,
    };
    //Call api tasker request update task detail
    await callService('/v4/update-task-vn/tasker-create-request', body);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await tapText('Nhắn tin');
    await expectElementVisible('extraMoneyTxt');
    await expectElementVisible('totalAmountTxt');
    await tapId('approveUpdateDetailBtn');
    await tapId('btnActionAlert1'); //Tap nút Đồng ý
    await expectElementNotVisible('approveUpdateDetailBtn');
    await expectIdToHaveTextAtIndex('txtTitleMessageWithAction', 'Cảm ơn sự xác nhận của bạn', 1);
  });
});
