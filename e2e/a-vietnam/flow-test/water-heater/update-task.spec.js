/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-06-27 10:02:31
 * @modify date 2024-04-15 11:37:57
 * @description Update note
 *   case 1: Asker update note task
 *   case 2: Asker update date time task
 */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  typeToTextField,
  reloadApp,
  expectElementVisible,
  selectTime24h,
} = require('../../../step-definition');

describe('FILE: e2e/a-vietnam/flow-test/water-heater/update-task.spec.js - Update note', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [
      { isoCode: 'VN', phone: '0834567890', name: 'Asker', type: 'ASKER', status: 'ACTIVE' },
    ]);
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'WATER_HEATER',
        askerPhone: '0834567890',
        description: 'My Task',
      },
    ]);
    await E2EHelpers.onHaveLogin('0834567890');
  });

  it('LINE 42 - Asker update note task', async () => {
    await tapId('Tab_Activity');
    await expectElementVisible('txtQuantityWH_2');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await typeToTextField('taskNote', 'Lau don phong ngu\n');
    await tapId('updateTaskNote');
    await expectIdToHaveText('taskNote', 'Lau don phong ngu\n');
  });

  it('LINE 59 - Asker update date time task', async () => {
    await tapId('Tab_Activity');
    await expectElementVisible('txtQuantityWH_2');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(17);
    await tapText('Đồng ý');
    await tapText('Cập nhật');
    await tapText('Đồng ý');
    await expectElementVisible('Cập nhật thành công', 'text');
    await tapText('Đóng');
  });
});
