/**
 * @description Post task choose option premium
 *   case 1: LINE 51 - Ask<PERSON> choose option premium
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,

  postTask,
  expectElementVisible,
  tapHeaderBack,
  tapTextAtIndex,
  expectElementNotVisible,
  scrollTo,
  waitForElement,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
describe('FILE: e2e/a-vietnam/flow-test/water-heater/post-task/post-task-with-tet.spec.js - Post task Tet service', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('event-config/create-data', [{ isoCode: 'VN', services: ['WATER_HEATER'] }]);
    await initData('service/update-fields', {
      isoCode: 'VN',
      serviceName: 'WATER_HEATER',
      dataUpdate: {
        tetBookingDates: {
          bookTaskTime: {
            fromDate: moment().add(2, 'days').toDate(),
            toDate: moment().add(32, 'days').toDate(),
          },
          fromDate: moment().subtract(1, 'days').toDate(),
          toDate: moment().add(60, 'days').toDate(),
        },
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 34 - Asker choose option tet', async () => {
    await postTask('postTaskServiceTetWATER_HEATER');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await tapId('address1');

    // post task step 2
    await waitForElement('txtTitleStep2WH', 500);
    await tapId('btnChooseTypeWH_LV1');
    await expectElementVisible('txtTypeWH_LV1');
    await tapId('btnPlus');
    await tapId('btnChooseOptionWH');
    await expectElementVisible('txtQuantityOption');
    await expectElementVisible('txtPriceOption');
    await expectElementVisible('txtQuantityOptions');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2WH');
    await tapId('btnNextStep3WH');
    // Post task step 4
    await scrollTo('scrollViewStep4WH', 'bottom');
    await expectElementVisible('bPay', 'text');
  });

  it('LINE 34 - Asker change date tet booking', async () => {
    await postTask('postTaskServiceTetWATER_HEATER');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await tapId('address1');

    // post task step 2
    await expectElementVisible('txtTitleStep2WH');
    await tapId('btnChooseTypeWH_LV1');
    await expectElementVisible('txtTypeWH_LV1');
    await tapId('btnPlus');
    await tapId('btnChooseOptionWH');
    await expectElementVisible('txtQuantityOption');
    await expectElementVisible('txtPriceOption');
    await expectElementVisible('txtQuantityOptions');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2WH');
    await tapId('datePickerTet');

    const nextMonday = moment().add(10, 'days').date();
    try {
      await tapTextAtIndex(nextMonday.toString(), 0);
    } catch (error) {
      await tapTextAtIndex(nextMonday.toString(), 1);
    }
    await tapText('Đồng ý');
    await tapId('btnNextStep3WH');
    // Post task step 4
    await scrollTo('scrollViewStep4WH', 'bottom');
    await expectElementVisible('bPay', 'text');
    await tapText('Đăng việc');
    await waitForElement('Nạp thêm', 500, 'text');
    await tapText('Đóng');
    await tapId('choosePaymentMethod');
    await expectElementNotVisible('Tiền mặt', 'text');
    await tapHeaderBack();
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      financialAccountData: { FMainAccount: 5000000 },
    });
    await tapText('Đăng việc');

    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await scrollTo('scrollTaskDetail', 'bottom');
    await expectElementVisible('finalCost');
    await expectIdToHaveText('paymentMethod', 'bPay');
  });
});
