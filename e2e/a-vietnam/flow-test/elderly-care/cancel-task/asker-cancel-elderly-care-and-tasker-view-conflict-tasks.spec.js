/**
 * @description Asker cancel task
 *   case 1: LINE 32 - Ask<PERSON> cancel posted task
 *   case 2: <PERSON><PERSON>E 49 - Ask<PERSON> cancel posted task and other task of blacklist Asker
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  swipe,
  expectElementVisible,
  expectElementNotExist,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASKER2 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 9,
};

const TASK1 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};
const TASK2 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 02',
};
const TASK3 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 03',
};
const TASK4 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 04',
};

describe('FILE: e2e/a-vietnam/flow-test/elderly-care/cancel-task/asker-cancel-elderly-care-and-tasker-view-conflict-tasks.spec.js - Asker cancel task. Tasker will see the conflict tasks', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);
    await initData('task/createTask', [TASK1, TASK2, TASK3, TASK4]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 75 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
  });
});
