/**
 * @description Cancel task schedule
 *   case 1: LINE 26 - Post task and cancel schedule task
 * */

const { E2EHel<PERSON> } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  typeToTextField,
  expectIdToHaveText,
  expectElementVisible,
  waitForElement,
  tapHeaderBack,
  expectElementNotVisible,
  swipe,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
describe('FILE: e2e/a-vietnam/flow-test/elderly-care/cancel-task/asker-cancel-elderly-care-schedule.spec.js - Cancel task schedule', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: 1000000 },
    });

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 51 - Post task and cancel schedule task', async () => {
    await postTask('postTaskServiceELDERLY_CARE');
    await tapId('btnChooseDay');
    await tapId(`chooseDuration-4`);
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await typeToTextField('taskNote', 'Cham soc nguoi gia can than.\nTiem thuoc dung gio\n');
    await tapId('whatIsWeekly');
    await waitForElement('Lịch Lặp Lại là gì?', 1000, 'text');
    await tapText('Đã hiểu');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await tapId('DayOfWeek3');
    await tapId('DayOfWeek4');
    await tapId('DayOfWeek5');
    await tapId('DayOfWeek6');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await tapHeaderBack();
    await tapId('Tab_Schedule');
    await expectElementVisible('Đang hoạt động', 'text');
    await tapId('Tab_Upcoming');

    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}

    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementVisible('Bạn có muốn dừng lịch lặp lại ?', 'text');
    await tapText('Đồng ý');
    await waitForElement('postTaskNow', 1000);
    await expectElementNotVisible('TAB_UPCOMINGMy Task');
    await tapId('Tab_Schedule');
    await expectElementVisible('Tạm dừng', 'text');
  });
});
