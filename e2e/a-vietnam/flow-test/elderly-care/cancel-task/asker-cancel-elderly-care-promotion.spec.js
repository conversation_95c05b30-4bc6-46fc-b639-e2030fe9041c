/**
 * @description Asker cancel task with promotion
 *   case 1: LINE 36 - Asker cancel the task elderly is not include fee
 *   case 2: LINE 127 - Asker cancel the task include fee
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  expectIdToHaveText,
  waitForElement,
  postTask,
  tapText,
  typePromotionCode,
  typeToTextField,
  waitForLoading,
} = require('../../../../step-definition');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASK = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};
const PROMOTION = {
  isoCode: 'VN',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};
describe('FILE: e2e/a-vietnam/flow-test/elderly-care/cancel-task/asker-cancel-elderly-care-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1]);
    await initData('promotion/create-promotion-code', [PROMOTION]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: 1000000 },
    });

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 69 - Asker cancel the task elderly is not include fee', async () => {
    await initData('task/createTask', [TASK]);
    await initData('promotion/apply-promotion-code-to-task', [
      { description: TASK.description, isoCode: 'VN', promotionCode: PROMOTION.code },
    ]);

    await tapText('Hoạt động');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
    await tapId('btnEditTask');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementVisible(
      'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
      'text',
    );
    await tapText('Tiếp tục');
    await tapId('postTaskNow');
    await postTask('postTaskServiceELDERLY_CARE');
    await tapId('btnChooseDay');

    await tapId('chooseDuration-4');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await typeToTextField('taskNote', 'Cham soc nguoi gia can than.\nTiem thuoc dung gio\n');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION.code);
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 132 - Asker cancel the task include fee', async () => {
    await initData('task/createTask', [TASK]);
    await initData('promotion/apply-promotion-code-to-task', [
      { description: TASK.description, isoCode: 'VN', promotionCode: PROMOTION.code },
    ]);
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await waitForLoading(300);
    await expectElementVisible(
      'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
      'text',
    );
    await tapText('Tiếp tục');
    await tapId('postTaskNow');
    await postTask('postTaskServiceELDERLY_CARE');
    await tapId('btnChooseDay');

    await tapId('chooseDuration-4');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await typeToTextField('taskNote', 'Cham soc nguoi gia can than.\nTiem thuoc dung gio\n');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION.code);
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });
});
