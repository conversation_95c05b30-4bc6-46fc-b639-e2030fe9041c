/**
 * @description Asker cancel task
 *  case 1: '<PERSON><PERSON><PERSON> 53 - <PERSON><PERSON> cancel posted task',
 *  case 2: '<PERSON>IN<PERSON> 76 Ask<PERSON> cancel waiting cleaning task',
 *  case 3: '<PERSON><PERSON>E 97 - <PERSON><PERSON> cancel confirmed cleaning task before working time',
 *  case 4: '<PERSON><PERSON><PERSON> 136 - Ask<PERSON> cancel confirmed cleaning task before working time, find same task for Task<PERSON>',
 *  case 5: 'LINE 165 - <PERSON><PERSON> cancel confirmed cleaning task after task began 15 minutes',
 *  case 6: 'LINE 196 - <PERSON><PERSON> cancel confirmed cleaning task with fee 20k',
 *  case 7: 'LINE 228 - <PERSON><PERSON> cancel waiting cleaning task with fee 20k',
 *  case 8: 'LINE 281 - Ask<PERSON> cancel confirmed cleaning task with reason Tasker not comming free',
 *  case 9: 'LINE 303 - <PERSON><PERSON> cancel task and input the reason',
 *  case 10: 'LINE 326 - <PERSON><PERSON> cancel cleaning task after task begining',
 *  case 11: 'LINE 349 - <PERSON><PERSON> cancel cleaning task before task begining less than 1 hour',
 *  case 12: 'LINE 372 - <PERSON><PERSON> cancel cleaning task before task begining less than 1 hour (Max fee)',
 *  case 13: 'LINE 395 - Asker cancel cleaning task before task begining 2 hours',
 *  case 14: 'LINE 435 - Ask<PERSON> cancel CONFIRMED cleaning task because Task<PERSON> request - Before 8 hours when task is started',
 * */

const { E2E<PERSON>elpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  waitForElement,
  typeToTextField,
  tapText,
  expectElementNotExist,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASKER2 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 9,
};

const TASK1 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};
const TASK2 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 02',
};
const TASK3 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 03',
};
const TASK4 = {
  isoCode: 'VN',
  serviceName: 'ELDERLY_CARE',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 04',
};
const F_MAIN_ACCOUNT = ********;
describe('FILE: e2e/a-vietnam/flow-test/elderly-care/cancel-task/asker-cancel-elderly-care.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);
    await initData('task/createTask', [TASK1, TASK2, TASK3, TASK4]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: F_MAIN_ACCOUNT },
    });
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK1.description,
        taskerAccepted: [TASKER1.phone],
        status: 'WAITING_ASKER_CONFIRMATION',
      },
      { isoCode: 'VN', description: TASK2.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 106 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { description: TASK4.description, isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 134 Asker cancel waiting cleaning task', async () => {
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    const task = await initData('task/getTaskByDescription', { description: 'Don dep nha 01', isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 160 - Asker cancel confirmed cleaning task before working time', async () => {
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASK1.phone], isoCode: 'VN' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await expectElementNotExist('Tasker tự ý không đến.', 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskDon dep nha 02');
    const task = await initData('task/getTaskByDescription', { description: TASK2.description, isoCode: 'VN' });
    expect(task.status).to.equal('POSTED');

    const notify = await initData('notification/get-notification', { isoCode: 'VN', phone: TASKER1.phone, type: 30 });
    expect(notify.length).to.equal(1);

    const data3 = await initData('notification/get-notification', {
      phone: TASKER1.phone,
      isoCode: 'VN',
      taskDescription: TASK2.description,
    });
    expect(data3.length).to.equal(1);
    expect(data3[0].type).to.equal(30);

    const data1 = await initData('notification/get-notification', {
      phone: TASKER1.phone,
      isoCode: 'VN',
      taskDescription: TASK3.description,
    });
    expect(data1.length).to.equal(0);

    const data2 = await initData('notification/get-notification', {
      phone: TASKER1.phone,
      isoCode: 'VN',
      taskDescription: TASK4.description,
    });
    expect(data2.length).to.equal(0);
  });

  it('LINE 242 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASK1.phone], isoCode: 'VN' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    const data = await initData('notification/get-notification', {
      phone: TASKER1.phone,
      isoCode: 'VN',
      taskDescription: TASK3.description,
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: TASKER1.phone,
      isoCode: 'VN',
      taskDescription: TASK4.description,
    });
    expect(data1.length).to.equal(0);
    await expectElementNotExist('tasktask 02');
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: TASK2.description });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 284 - Asker cancel confirmed cleaning task after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK4.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          date: moment().toDate(),
          createdAt: moment().subtract(1, 'hour').toDate(),
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER1.phone], isoCode: 'VN' },
    ]);

    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    // try {
    // await tapId('cancelTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // } catch (error) {}
    await waitForElement('Tasker tự ý không đến.', 500, 'text');
    await tapText('Tasker tự ý không đến.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
  });

  it('LINE 338 - Asker cancel confirmed cleaning task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK1.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 01');
    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 20000,
      isoCode: 'VN',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(20000);
    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data2.FMainAccount).to.equal(F_MAIN_ACCOUNT - 20000);
    expect(data2.Promotion).to.equal(0);
  });

  it('LINE 392 - Asker cancel waiting cleaning task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK1.description,
        taskerAccepted: [TASKER1.phone],
        status: 'WAITING_ASKER_CONFIRMATION',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 01');
  });

  it('LINE 435 - Asker cancel confirmed cleaning task with reason Tasker not comming free', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK1.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK1.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(3, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskDon dep nha 01');
    const data = await initData('user/find-faccount', { phone: TASKER1.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(-130000);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 486 - Asker cancel task and input the reason', async () => {
    const NOTE = 'I cancel';
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapId('btnOtherReason');
    await typeToTextField('inputOtherReason', NOTE);
    await tapId('txtOtherReason'); // click lần dầu tắt bàn phím
    await tapId('btnOKChooseReason');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', {
      description: TASK4.description,
      status: 'CANCELED',
      isoCode: 'VN',
    });
    expect(task.cancellationText).to.equal(NOTE);
  });

  it('LINE 527 - Asker cancel cleaning task after task begining', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK2.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 78000);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 570 - Asker cancel cleaning task before task begining less than 1 hour', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK2.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 260000 * 0.3);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 613 - Asker cancel cleaning task before task begining less than 1 hour (Max fee)', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK2.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          status: 'CONFIRMED',
          costDetail: {
            finalCost: 5000000,
            baseCost: 5000000,
            cost: 5000000,
          },
          cost: 5000000,
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');

    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 100000);
  });

  it('LINE 664 - Asker cancel cleaning task before task begining 2 hours', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK2.description, taskerAccepted: [TASKER1.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(6, 'hour').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(F_MAIN_ACCOUNT - 20000);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 708 - Asker cancel CONFIRMED cleaning task because Tasker request - Before 8 hours when task is started', async () => {
    await initData('task/updateTask', [
      {
        description: TASK2.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskDon dep nha 02');
  });
});
