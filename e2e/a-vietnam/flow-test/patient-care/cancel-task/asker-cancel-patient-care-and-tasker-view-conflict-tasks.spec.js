/**
 * @description Asker cancel task
 *   case 1: <PERSON>INE 32 - <PERSON><PERSON> cancel posted task
 *   case 2: <PERSON><PERSON><PERSON> 49 - Ask<PERSON> cancel posted task and other task of blacklist Asker
 * */

const { initData, tapId, tapText, swipe, scroll, expectElementVisible } = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

const TASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  taskDone: 0,
};

describe('FILE: e2e/a-vietnam/flow-test/patient-care/cancel-task/asker-cancel-patient-care-and-tasker-view-conflict-tasks.spec.js - Asker cancel task. Tasker will see the conflict tasks', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'PATIENT_CARE',
        askerPhone: '**********',
        description: 'My Task',
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 45 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
  });

  it('LINE 58 - Asker cancel posted task and other task of blacklist Asker', async () => {
    await initData('user/updateUser', [{ phone: '**********', isoCode: 'VN', dataUpdate: { isBlacklist: true } }]);
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: 'My Task',
        taskerAccepted: ['**********'],
        status: 'WAITING_ASKER_CONFIRMATION',
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
  });
});
