/**
 * @description Asker cancel task
 *   case 1: <PERSON><PERSON><PERSON> 107 - <PERSON><PERSON> cancel posted task
 *   case 2: <PERSON><PERSON><PERSON> 136 - Ask<PERSON> cancel waiting cleaning task
 *   case 3: <PERSON><PERSON><PERSON> 164 - Ask<PERSON> cancel confirmed cleaning task before working time
 *   case 4: LINE 239 - Asker cancel confirmed cleaning task before working time, find same task for Tasker
 *   case 5: LINE 284 - <PERSON><PERSON> cancel confirmed cleaning task after task began 15 minutes
 *   case 6: <PERSON><PERSON><PERSON> 339 - <PERSON><PERSON> cancel confirmed cleaning task with fee 20k
 *   case 7: <PERSON><PERSON><PERSON> 400 - <PERSON><PERSON> cancel waiting cleaning task with fee 20k
 *   case 8: LINE 438 - <PERSON><PERSON> cancel posted task with free charge
 *   case 9: LINE 479 - <PERSON><PERSON> cancel confirmed cleaning task with reason Tasker not comming free
 *   case 10: LINE 531 - <PERSON><PERSON> cancel task and input the reason
 *   case 11: <PERSON><PERSON><PERSON> 561 - <PERSON><PERSON> cancel cleaning task after task begining
 *   case 13: LINE 605 - <PERSON><PERSON> cancel cleaning task before task begining less than 1 hour (Max fee
 *   case 14: LINE 666 - <PERSON><PERSON> cancel cleaning task before task begining 2 hours
 *   case 15: LINE 710 - Disconnect from server and cancel cleaning task
 *   case 16: LINE 733 - <PERSON><PERSON> cancel CONFIRMED cleaning task because Tasker request - Before 8 hours when task is started
 * */

const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  waitForElement,
  typeToText<PERSON>ield,
  tapText,
  expectElementNotExist,
  scroll,
  tapHeader<PERSON>ack,
} = require('../../../../step-definition');
const expect = require('chai').expect;

const moment = require('moment');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

const TASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  taskDone: 0,
};

const TASK_01 = {
  isoCode: 'VN',
  serviceName: 'PATIENT_CARE',
  askerPhone: '**********',
  description: 'task 01',
};

const TASK_02 = {
  isoCode: 'VN',
  serviceName: 'PATIENT_CARE',
  askerPhone: '**********',
  description: 'task 02',
};

const TASK_03 = {
  isoCode: 'VN',
  serviceName: 'PATIENT_CARE',
  askerPhone: '**********',
  description: 'task 03',
};

const TASK_04 = {
  isoCode: 'VN',
  serviceName: 'PATIENT_CARE',
  askerPhone: '**********',
  description: 'task 04',
};

describe('FILE: e2e/a-vietnam/flow-test/patient-care/cancel-task/asker-cancel-patient-care.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK_01, TASK_02, TASK_03, TASK_04]);
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK_01.description,
        taskerAccepted: [TASKER.phone],
        status: 'WAITING_ASKER_CONFIRMATION',
      },
      { isoCode: 'VN', description: TASK_02.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
    try {
      await tapHeaderBack();
    } catch (error) {}
  });

  it('LINE 104 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('TAB_UPCOMINGtask 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 04');
    const task = await initData('task/getTaskByDescription', { description: 'task 04', isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 133 - Asker cancel waiting cleaning task', async () => {
    await tapText('Hoạt động');
    await tapId('tasktask 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 01');
    const task = await initData('task/getTaskByDescription', { description: 'task 01', isoCode: 'VN' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 161 - Asker cancel confirmed cleaning task before working time', async () => {
    await initData('service/updateServiceChannel', [
      { isoCode: 'VN', serviceName: 'PATIENT_CARE', taskerPhone: TASKER.phone },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK_04.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'VN' },
    ]);

    await tapText('Hoạt động');
    await tapId('tasktask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');

    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await expectElementNotExist('Tasker tự ý không đến.', 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('tasktask 02');
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'task 02' });
    expect(task.status).to.equal('POSTED');

    const notify = await initData('notification/get-notification', { isoCode: 'VN', phone: TASKER.phone, type: 30 });
    expect(notify.length).to.equal(1);

    const notify2 = await initData('notification/get-notification', {
      isoCode: 'VN',
      phone: TASKER.phone,
      description: TASK_03.description,
    });
    expect(notify2.length).to.equal(0);

    const notify3 = await initData('notification/get-notification', {
      isoCode: 'VN',
      phone: TASKER.phone,
      description: TASK_04.description,
    });
    expect(notify3.length).to.equal(0);
  });

  it('LINE 236 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'VN' },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');

    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    const data = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: 'VN',
      taskDescription: TASK_03.description,
      type: 0,
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: 'VN',
      taskDescription: TASK_04.description,
      type: 0,
    });
    expect(data1.length).to.equal(0);
    await expectElementNotExist('tasktask 02');
    const task = await initData('task/getTaskByDescription', { isoCode: 'VN', description: TASK_02.description });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 281 - Asker cancel confirmed cleaning task after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK_04.description,
        isoCode: 'VN',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'VN' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          date: moment().subtract(15, 'minutes').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker tự ý không đến.', 500, 'text');
    await tapText('Tasker tự ý không đến.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 02');
  });

  it('LINE 336 - Asker cancel confirmed cleaning task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK_01.description,
        taskerAccepted: [TASKER.phone],
        status: 'CONFIRMED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 01');

    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 20000,
      isoCode: 'VN',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(20000);

    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data2.FMainAccount).to.equal(-20000);
    expect(data2.Promotion).to.equal(0);
  });

  it('LINE 397 - Asker cancel waiting cleaning task with fee 20k', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 01');
  });

  it('LINE 435 - Asker cancel posted task with free charge', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_04.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 04');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 04');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(0);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 476 - Asker cancel confirmed cleaning task with reason Tasker not comming free', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK_01.description,
        taskerAccepted: [TASKER.phone],
        status: 'CONFIRMED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_01.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(3, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('tasktask 01');
    const data = await initData('user/find-faccount', { phone: TASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(-140000); // 50% giá trị task
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 528 - Asker cancel task and input the reason', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('tasktask 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapId('btnOtherReason');
    await typeToTextField('inputOtherReason', 'I cancel');
    await tapId('txtOtherReason'); // click lần dầu tắt bàn phím
    await tapId('btnOKChooseReason');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 04');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 04');
    const task = await initData('task/getTaskByDescription', { description: 'task 04', isoCode: 'VN' });
    expect(task.cancellationText).to.equal('I cancel');
  });

  it('LINE 558 - Asker cancel cleaning task after task begining', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK_02.description,
        taskerAccepted: [TASKER.phone],
        status: 'CONFIRMED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(1, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(-84000); // 30% giá trị task
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 602 - Asker cancel cleaning task before task begining less than 1 hour (Max fee)', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK_02.description,
        taskerAccepted: [TASKER.phone],
        status: 'CONFIRMED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().subtract(1, 'hour').toDate(),
          cost: 500000,
          costDetail: {
            baseCost: 500000,
            cost: 500000,
            finalCost: 500000,
            duration: 4,
            currency: {
              sign: '₫',
              code: 'VND',
            },
            transportFee: 0,
            depositMoney: 0,
            newFinalCost: 0,
            vat: 0,
            taskerWithHoldingTax: 500000,
            totalCost: 0,
          },
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(-100000); // max 100,000
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 663 - Asker cancel cleaning task before task begining 2 hours', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: 'VN',
        description: TASK_02.description,
        taskerAccepted: [TASKER.phone],
        status: 'CONFIRMED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'VN' });
    expect(data.FMainAccount).to.equal(-20000);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 707 - Disconnect from server and cancel cleaning task', async () => {
    await tapText('Hoạt động');
    await tapId('tasktask 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('tasktask 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('tasktask 01');
  });

  it('LINE 730 - Asker cancel CONFIRMED cleaning task because Tasker request - Before 8 hours when task is started', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_02.description,
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(8, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('tasktask 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('tasktask 02');
  });
});
