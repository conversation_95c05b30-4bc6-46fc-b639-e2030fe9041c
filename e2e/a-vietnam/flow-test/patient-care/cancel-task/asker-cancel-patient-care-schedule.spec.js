/**
 * @description Cancel task schedule
 *   case 1: LINE 26 - Post task and cancel schedule task
 * */

const {
  initData,
  tapId,
  tapText,
  postTask,
  typeToTextField,
  expectIdToHaveText,
  expectElementVisible,
  waitForElement,
  tapHeaderBack,
  waitForLoading,
  expectElementNotVisible,
  swipe,
  scroll,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

describe('FILE: e2e/a-vietnam/flow-test/patient-care/cancel-task/asker-cancel-patient-care-schedule.spec.js - Cancel task schedule', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 40 - Post task and cancel schedule task', async () => {
    await postTask('postTaskServicePATIENT_CARE', 'Cho Ray Hospital', 'My Task');
    await tapId('btnChooseDay');
    await tapId(`chooseDuration-4`);
    await expectElementVisible('lbPrice', `280,000 VND/4h`);
    await tapId('btnNextStep2');

    await tapId('whatIsWeekly');
    await waitForElement('Lịch Lặp Lại là gì?', 1000, 'text');
    await tapText('Đã hiểu');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await tapId('DayOfWeek3');
    await tapId('DayOfWeek4');
    await tapId('DayOfWeek5');
    await tapId('DayOfWeek6');

    await typeToTextField('taskNote', 'Cham soc nguoi gia can than.\nTiem thuoc dung gio\n');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '280,000 ₫');
    await tapHeaderBack();
    await tapId('Tab_Schedule');
    await expectElementVisible('Đang hoạt động', 'text');
    await tapId('Tab_Upcoming');

    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await waitForLoading(500);
    await expectElementVisible('Bạn có muốn dừng lịch lặp lại ?', 'text');
    await tapText('Đồng ý');
    await waitForElement('postTaskNow', 1000);
    await expectElementNotVisible('TAB_UPCOMINGMy Task');
    await tapId('Tab_Schedule');
    await expectElementVisible('Tạm dừng', 'text');
    // await expectIdToHaveText('taskStatusMy Task', 'Tạm dừng');
  });
});
