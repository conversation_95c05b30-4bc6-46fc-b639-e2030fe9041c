/**
 * @description Asker cancel task with promotion
 *   case 1: LINE 31 - Asker cancel the task is not include fee
 *   case 2: LINE 92 - Asker cancel the task include fee
 * */

const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  expectIdToHaveText,
  waitForElement,
  postTask,
  tapText,
  typePromotionCode,
  expectElementNotVisible,
  typeToTextField,
  sleep,
} = require('../../../../step-definition');

const moment = require('moment');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

const TASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  taskDone: 0,
};

const TASK = {
  isoCode: 'VN',
  serviceName: 'PATIENT_CARE',
  askerPhone: '**********',
  description: 'My Task',
};

const PROMOTION = {
  isoCode: 'VN',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};

describe('FILE: e2e/a-vietnam/flow-test/patient-care/cancel-task/asker-cancel-patient-care-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('promotion/create-promotion-code', [PROMOTION]);
    await initData('task/createTask', [TASK]);
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION.code,
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 79 - Asker cancel the task patient is not include fee', async () => {
    await tapText('Hoạt động');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '280,000 ₫');
    await expectIdToHaveText('discount', '-50,000 ₫');
    await expectIdToHaveText('finalCost', '230,000 ₫');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await swipe('updatePage', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');

    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await sleep(500);
    await expectElementVisible(
      'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
      'text',
    );
    await tapText('Tiếp tục');
    await expectElementNotVisible('280,000 ₫', 'text');
    await expectElementNotVisible('50,000 ₫', 'text');
    await expectElementNotVisible('230,000 ₫', 'text');
    await waitForElement('postTaskNow', 1000);
    await expectElementNotVisible('taskDuration0');

    await tapId('postTaskNow');
    await postTask('postTaskServicePATIENT_CARE', 'Cho Ray Hospital', 'My Task');
    await tapId('btnChooseDay');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', `280,000 VND/4h`);
    await tapId('btnNextStep2');
    await typeToTextField('taskNote', 'Cham soc nguoi gia can than.\nTiem thuoc dung gio\n');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('def123');
    await expectIdToHaveText('originPrice', '280,000 VND');
    await expectIdToHaveText('price', '230,000 VND');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '280,000 ₫');
    await expectIdToHaveText('discount', '-50,000 ₫');
    await expectIdToHaveText('finalCost', '230,000 ₫');
  });

  it('LINE 136 - Asker cancel the task patient include fee', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'My Task', taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(1, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '280,000 ₫');
    await expectIdToHaveText('discount', '-50,000 ₫');
    await expectIdToHaveText('finalCost', '230,000 ₫');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');
    // await swipe('updatePage', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await sleep(500);
    await expectElementVisible('Thông báo', 'text');
    await expectElementVisible(
      'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
      'text',
    );
    await tapText('Tiếp tục');
    await expectElementNotVisible('280,000 ₫', 'text');
    await expectElementNotVisible('50,000 ₫', 'text');
    await expectElementNotVisible('230,000 ₫', 'text');
    await waitForElement('postTaskNow', 1000);
    await expectElementNotVisible('taskDuration0');

    await tapId('postTaskNow');
    await postTask('postTaskServicePATIENT_CARE', 'Cho Ray Hospital', 'My Task');
    await tapId('btnChooseDay');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', `280,000 VND/4h`);
    await tapId('btnNextStep2');
    await typeToTextField('taskNote', 'Cham soc nguoi gia can than.\nTiem thuoc dung gio\n');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('def123');
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });
});
