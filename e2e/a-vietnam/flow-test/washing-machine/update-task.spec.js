/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-06-27 10:02:31
 * @modify date 2024-03-28 21:42:30
 * @description Update note
 *   case 1: Asker update note task
 *   case 2: Asker update date time task
 */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  swipe,
  typeToTextField,
  reloadApp,
  // loginWithPhoneAndPassword,
  expectElementVisible,
  selectTime24h,
} = require('../../../step-definition');

describe('FILE: e2e/a-vietnam/flow-test/washing-machine/update-task.spec.js - Update note', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [
      { isoCode: 'VN', phone: '0834567890', name: 'Asker', type: 'ASKER', status: 'ACTIVE' },
    ]);
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'WASHING_MACHINE',
        askerPhone: '0834567890',
        description: 'My Task',
      },
    ]);
    await E2EHelpers.onHaveLogin('0834567890');
  });

  it('LINE 43 - Asker update note task', async () => {
    // await loginWithPhoneAndPassword('0834567890', '123456');
    await tapId('Tab_Activity');
    await expectElementVisible('txtNumberFrontLoading');
    await expectElementVisible('txtNumberTopLoading');
    await expectIdToHaveText('numberFrontLoading', 'x1');
    await expectIdToHaveText('numberTopLoading', 'x1');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await typeToTextField('taskNote', 'Lau dọn phong ngu tang 1\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn phong ngu tang 1\n', 500, 'text');
  });

  it('LINE 64 - Asker update date time task', async () => {
    await tapId('Tab_Activity');
    await expectElementVisible('txtNumberFrontLoading');
    await expectElementVisible('txtNumberTopLoading');
    await expectIdToHaveText('numberFrontLoading', 'x1');
    await expectIdToHaveText('numberTopLoading', 'x1');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(17);
    await tapText('Đồng ý');
    await tapText('Cập nhật');
    await tapText('Đồng ý');
    await expectElementVisible('Cập nhật thành công', 'text');
    await tapText('Đóng');
  });
});
