/**
 * @description Update note
 *   case 1: Asker update note normal task
 *   case 2: Asker update note prepay task
 * */

const {
  initData,
  tapText,
  waitForElement,
  swipe,
  clearTextInput,
  tapId,
  typeToTextField,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  SERVICE_NAME: { HAIR_STYLING },
  ISO_CODE: { VN },
} = require('../../../../helpers/constants');

const ASKER = {
  isoCode: VN,
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

const TASK = {
  isoCode: VN,
  serviceName: HAIR_STYLING,
  askerPhone: '0834567890',
  description: 'My Task',
};

describe('FILE: e2e/a-vietnam/flow-test/hair-styling/update-date-time/update-note.spec.js - Update note', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 44 - Asker update note normal task', async () => {
    await tapText('Hoạt động');
    await tapText('Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await clearTextInput('taskNote');
    await typeToTextField('taskNote', 'Lau dọn phong ngu tang 1\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn phong ngu tang 1\n', 500, 'text');
  });
});
