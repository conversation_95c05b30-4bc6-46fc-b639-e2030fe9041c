/**
 * @description Asker post task with promotion code (10 test cases)
 *   case 1: <PERSON><PERSON><PERSON> 37 - <PERSON><PERSON> post task with promotion code money "def123
 *   case 2: <PERSON><PERSON><PERSON> 60 - <PERSON><PERSON> post task with promotion code percentage "opq123
 *   case 3: <PERSON><PERSON><PERSON> 84 - <PERSON><PERSON> post task with promotion code percentage "OPQ123
 *   case 4: <PERSON><PERSON><PERSON> 108 - <PERSON><PERSON> post task with promotion code percentage "OpQ123
 *   case 5: <PERSON><PERSON><PERSON> 132 - <PERSON><PERSON> post task with invalid promotion code
 *   case 6: <PERSON><PERSON><PERSON> 158 - <PERSON><PERSON> post task with promotion code percentage "opq789" and max value is 30K
 *   case 7: LINE 183 - Ask<PERSON> post task with invalid promotion code and remove promotion field
 *   case 8: LINE 210 - <PERSON><PERSON> post task with refferal code
 *   case 9: LINE 227 - <PERSON><PERSON> post task with promotion, payment with bPay, check enough money
 *   case 10: LINE 261 - Ask<PERSON> post task with promotion, payment with bPay, check not enough money
 * */

const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  waitForElement,
  clearTextInput,
  waitForLoading,
  expectElementVisible,
  typePromotionCode,
  tapHeaderBack,
  tapIdService,
} = require('../../../../../step-definition');

const { E2EHelpers } = require('../../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

const ASKER_02 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

const PROMOTION_01 = {
  code: 'abc123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'CURRENT',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
  isoCode: 'VN',
};

const PROMOTION_02 = {
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
  isoCode: 'VN',
};

const PROMOTION_03 = {
  code: 'ghk123',
  value: 50000,
  target: 'TASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
  isoCode: 'VN',
};

const PROMOTION_04 = {
  code: 'lmn123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 1,
  maxValue: '',
  isoCode: 'VN',
};

const PROMOTION_05 = {
  code: 'opq123',
  value: 0.4,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'PERCENTAGE',
  limit: 100,
  maxValue: '',
  isoCode: 'VN',
};

const PROMOTION_06 = {
  code: 'opq789',
  value: 0.4,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'PERCENTAGE',
  maxValue: 30000,
  isoCode: 'VN',
};

const PROMOTION_07 = {
  isoCode: 'VN',
  code: 'opq799',
  value: 1,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'PERCENTAGE',
  limit: 100,
  maxValue: 3000000,
};

const postTaskToStep4 = async () => {
  await tapIdService('postTaskServiceBEAUTY_CARE');
  await tapText('Làm tóc');
  await tapId('1-people');
  await tapId('relaxingShampoo');
  await tapText('Tiếp tục');
  await tapId('address-0');
  await waitForElement('Tiếp tục', 500, 'text');
  await tapText('Tiếp tục');
  await waitForElement('taskNoteConfirm', 500);
  await tapId('taskNoteConfirm');
};

describe('FILE: e2e/a-vietnam/flow-test/hair-styling/post-task/campaign-promotion/asker-post-task-with-promotion-code.spec.js - Asker post task with promotion code', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, ASKER_02]);
    await initData('promotion/create-promotion-code', [
      PROMOTION_01,
      PROMOTION_02,
      PROMOTION_03,
      PROMOTION_04,
      PROMOTION_05,
      PROMOTION_06,
      PROMOTION_07,
    ]);
    await initData('promotion/usersAppliedPromotion', {
      isoCode: 'VN',
      phone: ASKER_02.phone,
      promotionCode: 'lmn123',
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 138 - Asker post task patient with promotion code money "def123"', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('def123');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
    await waitForElement('taskDuration0', 500);
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 164 - Asker post task patient with promotion code percentage "opq123"', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('opq123');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
    await waitForElement('taskDuration0', 500);
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 191 - Asker post task patient with promotion code percentage "OPQ123"', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('OPQ123');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
    await waitForElement('taskDuration0', 500);
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 218 - Asker post task patient with promotion code percentage "OpQ123"', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('OpQ123');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
    await waitForElement('taskDuration0', 500);
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 245 - Asker post task patient with invalid promotion code', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await waitForElement('Mã ưu đãi này không áp dụng cho tài khoản của bạn.', 500, 'text');
    await tapText('Đóng');
    await clearTextInput('textInputPromotion');
    await typePromotionCode('ghk123');
    await waitForLoading(500);
    await waitForElement('Mã ưu đãi chỉ áp dụng cho Tasker.', 500, 'text');
    await tapText('Đóng');
    await clearTextInput('textInputPromotion');
    await typePromotionCode('lmn123');
    await waitForLoading(500);
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 272 - Asker post task patient with promotion code percentage "opq789" and max value is 30K', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('opq789');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
    await waitForElement('taskDuration0', 500);
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 300 - Asker post task patient with refferal code', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    const data = await initData('user/getUserByPhone', { phone: ASKER_02.phone });
    await tapId('promotionCode');
    await typePromotionCode(data.referralCode);
    await waitForLoading(500);
    await waitForElement('Mã ưu đãi không hợp lệ. Vui lòng kiểm tra lại.', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 319 - Asker post task patient with promotion, payment with bPay, check enough money', async () => {
    await initData('faccount/updateFinancialAccount', [
      {
        phone: ASKER.phone,
        isoCode: ASKER.isoCode,
        dataUpdate: {
          FMainAccount: 1000000,
        },
      },
    ]);
    await tapText('Tài khoản');
    await tapText('bPay');
    await expectElementVisible('1,000,000', 'text');
    await tapHeaderBack();

    await tapText('Trang chủ');
    await postTaskToStep4(4);
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('def123');

    await expectElementVisible('originPrice');
    await expectElementVisible('price');

    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
    await waitForElement('taskDuration0', 500);
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 342 - Asker post task with promotion code percentage "opq799" and max value 100%', async () => {
    await postTaskToStep4();
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('opq799');
    await expectIdToHaveText('price', '0₫');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
  });
});
