/**
 * @description Asker cancel task
 *   case 1: LINE 32 - <PERSON><PERSON> cancel posted task
 *   case 2: <PERSON><PERSON>E 49 - Ask<PERSON> cancel posted task and other task of blacklist Asker
 * */

const { initData } = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const {
  SERVICE_NAME: { HAIR_STYLING },
  ISO_CODE: { VN },
} = require('../../../../helpers/constants');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: VN,
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER1 = {
  isoCode: VN,
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
  services: [HAIR_STYLING],
};
const TASKER2 = {
  isoCode: VN,
  phone: '0834567892',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASK_01 = {
  isoCode: VN,
  serviceName: HAIR_STYLING,
  askerPhone: '0834567890',
  description: 'Task 01',
  visibility: 3,
};
const TASK_02 = {
  isoCode: VN,
  serviceName: HAIR_STYLING,
  askerPhone: '0834567892',
  description: 'Task 02',
  visibility: 3,
};

const TASK_03 = {
  isoCode: VN,
  serviceName: HAIR_STYLING,
  askerPhone: '0834567890',
  description: 'Task 03',
  visibility: 3,
  viewedTaskers: [TASKER2.phone],
  date: moment().add(1, 'days').set('hour', 14).startOf('hour').toDate(),
};

describe('FILE: e2e/a-vietnam/flow-test/hair-styling/cancel-task/asker-cancel-task-and-tasker-view-conflict-tasks.spec.js - Asker cancel task. Tasker will see the conflict tasks', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);
    await initData('task/createTask', [TASK_01, TASK_02, TASK_03]);
    const tasker = await initData('user/getUserByPhone', { phone: TASKER1.phone });
    await initData('task/updateTask', [
      {
        description: 'Task 03',
        isoCode: VN,
        dataUpdate: {
          status: 'CONFIRMED',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 89 - Asker cancel posted task', async () => {
    await CancelTaskHelpers.cancelTaskAndTaskerViewConflictTask({
      tasker: TASKER1,
      asker: ASKER,
      isoCode: VN,
      task: TASK_03,
    });
  });
});
