/**
 * @description Asker cancel task
 * */

const { initData } = require('../../../../step-definition');
const moment = require('moment');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');

const {
  SERVICE_NAME: { MAKEUP },
  ISO_CODE: { VN },
} = require('../../../../helpers/constants');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER_02 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASK = {
  isoCode: 'VN',
  serviceName: MAKEUP,
  askerPhone: '0834567890',
  description: 'Don dep nha 01',
};
const TASK_02 = {
  isoCode: 'VN',
  serviceName: MAKEUP,
  askerPhone: '0834567890',
  description: 'Don dep nha 02',
  date: moment().add(1, 'days').set('hour', 14).startOf('hour').toDate(),
};
const TASK_03 = {
  _id: 'x65e82458dab676918b287f8f',
  isoCode: 'VN',
  serviceName: MAKEUP,
  askerPhone: '0834567890',
  description: 'Don dep nha 03',
};
const TASK_04 = {
  isoCode: 'VN',
  serviceName: MAKEUP,
  askerPhone: '0834567890',
  description: 'Don dep nha 04',
};

describe('FILE: e2e/a-vietnam/flow-test/makeup/cancel-task/asker-cancel-task.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER, TASKER_02]);
    await initData('task/createTask', [TASK, TASK_02, TASK_03, TASK_04]);
    const tasker = await initData('user/getUserByPhone', { phone: TASKER.phone });

    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'VN',
        dataUpdate: {
          status: 'WAITING_ASKER_CONFIRMATION',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
      {
        description: 'Don dep nha 02',
        isoCode: 'VN',
        dataUpdate: {
          status: 'CONFIRMED',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 96 - [REQUIRED] - Asker cancel posted task', async () => {
    await CancelTaskHelpers.cancelTaskPosted({ isoCode: VN, task: TASK_04, asker: ASKER });
  });

  it('LINE 104 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await CancelTaskHelpers.cancelTaskConfirmed({ isoCode: VN, task: TASK_02, asker: ASKER });
  });

  it('LINE 108 - [REQUIRED] - Makeup Asker cancel confirmed cleaning task with fee', async () => {
    await initData('task/acceptedTask', [
      { isoCode: TASK.isoCode, description: TASK.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: VN,
      tasker: TASKER,
      task: TASK,
      asker: ASKER,
      cancelFee: 0,
    });
  });

  it('LINE 132 - Asker cancel task and input the reason', async () => {
    await CancelTaskHelpers.cancelTaskWithOtherReason({ isoCode: VN, tasker: TASKER, task: TASK_04, asker: ASKER });
  });
});
