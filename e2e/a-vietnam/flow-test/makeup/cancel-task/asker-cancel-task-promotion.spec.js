/**
 * @description Asker cancel task with promotion
 *   case 1: LINE 63 - Ask<PERSON> cancel the task is not include fee
 *   case 2: LINE 141 - Asker cancel the task include fee
 * */

const { initData } = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');
const expect = require('chai').expect;

const {
  SERVICE_NAME: { MAKEUP },
  ISO_CODE: { VN },
} = require('../../../../helpers/constants');

const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASK = {
  removeRequirements: true,
  isoCode: 'VN',
  serviceName: MAKEUP,
  askerPhone: ASKER.phone,
  description: 'My task',
};

const PROMOTION_01 = {
  isoCode: 'VN',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};

describe('FILE: e2e/a-vietnam/flow-test/makeup/cancel-task/asker-cancel-task-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK]);

    await initData('promotion/create-promotion-code', PROMOTION_01);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 58 - Makeup Asker cancel the task is not include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);

    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: VN,
      tasker: TASKER,
      task: TASK,
      asker: ASKER,
      taskUsedPromotion: true,
    });

    const promotionHistory = await initData('promotion/getPromotionHistory', {
      promotionCode: PROMOTION_01.code,
      isoCode: PROMOTION_01.isoCode,
    });

    await expect(promotionHistory).to.equal(null);
  });

  it('LINE 136 - Makeup Asker cancel the task include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);

    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          status: 'CONFIRMED',
        },
      },
    ]);

    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: VN,
      tasker: TASKER,
      task: TASK,
      asker: ASKER,
      taskUsedPromotion: true,
      cancelFee: 0,
    });
  });
});
