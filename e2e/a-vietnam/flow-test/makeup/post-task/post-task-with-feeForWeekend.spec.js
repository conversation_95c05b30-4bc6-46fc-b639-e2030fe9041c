/**
 * @description Post task with feeForWeekend (3 test cases)
 *   case 1: LINE 35 - Post task with duration is 2h weekend
 *   case 2: LINE 56 - Post task with duration is 3h
 *   case 3: LINE 77 - Post task with duration is 2h not weekend
 * */

const {
  initData,
  tapId,
  tapText,
  swipe,
  expectElementVisible,
  isWeekend,
  tapIdService,
  waitForElement,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');

const {
  SERVICE_NAME: { MAKEUP },
  ISO_CODE: { VN },
} = require('../../../../helpers/constants');

const ASKER = {
  isoCode: VN,
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  taskDone: 0,
  oldUser: true,
};

//Không run vào t7 và cn
if (isWeekend()) {
  return;
}

describe('FILE: e2e/a-vietnam/flow-test/makeup/post-task/post-task-with-feeForWeekend.spec.js - Post task with feeForWeekend', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('service/updateService', {
      isoCode: ASKER.isoCode,
      serviceName: MAKEUP,
      dataUpdate: {
        priceSetting: {
          costForChooseTasker: 20000,
          feeForEmergencyTask: 20000,
          emergencyTaskWithin: 180,
          feeWeekendApplyForCity: ['Hồ Chí Minh'],
          surgePriceTime: [],
          feeForWeekend: 0.2,
        },
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 58 - Post task makeup with weekend', async () => {
    await tapIdService('postTaskServiceBEAUTY_CARE');
    await tapText('Trang điểm & Tạo mẫu tóc');
    await tapId('1-people');
    await tapId('graduationMakeup');
    await tapText('Tiếp tục');
    await tapId('graduationNaturalMakeup');
    await tapText('Tiếp tục');
    await tapId('address-0');
    await waitForElement('Tiếp tục', 500, 'text');
    try {
      await tapText('T7');
    } catch (error) {
      await tapText('CN');
    }
    await swipe('scrollDetailStep3', 'up');
    await expectElementVisible('Giá tăng do nhu cầu công việc tăng cao vào thời điểm này.', 'text');
    await tapText('Tiếp tục');
    await waitForElement('taskNoteConfirm', 500);
    await tapId('taskNoteConfirm');
    await waitForElement('scrollViewStep4', 1000);
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTask');
    await tapText('Tiếp tục');
    await tapText('Xem công việc');
  });
});
