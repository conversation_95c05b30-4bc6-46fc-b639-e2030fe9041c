const { initData } = require('../../../../step-definition');
const { UpdateDateTimeHelpers } = require('../../../../helpers/task/updateDateTime.helpers');
const {
  SERVICE_NAME: { MAKEUP },
  ISO_CODE: { VN },
} = require('../../../../helpers/constants');

const params = {
  serviceName: MAKEUP,
  isoCode: VN,
};

describe('FILE: e2e/a-vietnam/flow-test/makeup/update-date-time/asker-change-date-time-and-keep-tasker.spec.js - Change datetime  Patient Care task', () => {
  beforeEach(async () => {
    await initData('resetData');
  });

  it('LINE 18 - Asker want to change task WAITING_ASKER_CONFIRMATION and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskWaitingAndKeepTasker(params);
  });

  it('LINE 22 - Asker want to change task CONFIRMED and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTasker(params);
  });

  it('LINE 26 - Asker change datetime task CONFIRMED and keep tasker conflict time', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerConflictTime(params);
  });

  it('LINE 30 - Aakeup Asker want to change task CONFIRMED and keep tasker and not enough money', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerNotEnoughMoney(params);
  });

  it('LINE 34 - Asker want to change task CONFIRMED and keep tasker reject', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerReject(params);
  });
});
