const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  postTask,
  tapHeaderBack,
  swipe,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

describe('FILE: e2e/a-vietnam/flow-test/ironing/update-date-time/update-duration.spec.js - Change duration Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 46 - Asker update duration task', async () => {
    await postTask('postTaskServiceIRONING');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapText('Ngày giờ làm việc');
    await tapText('3 giờ');
    await expectElementVisible('lbPrice');
    await tapText('Cập nhật');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await tapId('taskMy Task');
  });
});
