const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  swipe,
  waitForElement,
  selectTime24h,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER_01 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER_02 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER_03 = {
  isoCode: 'VN',
  phone: '0834567893',
  name: 'Tasker 03',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASK = {
  isoCode: 'VN',
  serviceName: 'IRONING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};
describe('FILE: e2e/a-vietnam/flow-test/ironing/update-date-time/asker-change-date-time.spec.js - Change datetime Ironing task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER_01, TASKER_02, TASKER_03]);
  });

  it('LINE 73 - Asker want to change task datetime - POSTED', async () => {
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');

    await tapText('Hoạt động');
    await expectElementVisible('serviceNameDon dep nha 01');
    await expectIdToHaveText('taskStatusDon dep nha 01', 'Mới đăng');
    await tapId('taskDon dep nha 01');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await expectElementVisible('Ngày giờ làm việc', 'text');
    await tapText('Ngày giờ làm việc');
    // await selectTime(1, true, 'AM');
    await selectTime24h(23);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
      500,
      'text',
    );
    await tapText('Đóng');
    await selectTime24h(6);
    await tapText('Đồng ý');

    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 1000, 'text');

    await tapText('Đóng');
    await tapId('taskDon dep nha 01');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
  });
});
