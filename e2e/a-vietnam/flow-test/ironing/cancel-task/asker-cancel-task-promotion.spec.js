/**
 * @description Asker cancel task with promotion
 *   case 1: LINE 63 - Ask<PERSON> cancel the task is not include fee
 *   case 2: LINE 141 - Asker cancel the task include fee
 * */

const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  expectIdToHaveText,
  waitForElement,
  postTask,
  tapText,
  typePromotionCode,
  expectElementNotVisible,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');

const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASK = {
  removeRequirements: true,
  isoCode: 'VN',
  serviceName: 'IRONING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};

const PROMOTION_01 = {
  isoCode: 'VN',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};

const F_MAIN_ACCOUNT = 1000000;

describe('FILE: e2e/a-vietnam/flow-test/ironing/cancel-task/asker-cancel-task-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK]);

    await initData('promotion/create-promotion-code', PROMOTION_01);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      financialAccountData: { FMainAccount: F_MAIN_ACCOUNT },
    });

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 58 - Asker cancel the task is not include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('discount', '-50,000 ₫');
    await tapId('btnEditTask');
    // await tapText('Ngày giờ làm việc');
    await tapId('cancelTask');
    await waitForElement('btnConfirmCancel', 500);
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Tiếp tục');
    await expectElementNotVisible('50,000 ₫', 'text');
    await waitForElement('postTaskNow', 1000);
    await expectElementNotVisible('taskDuration0');

    await device.reloadReactNative();
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-3');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION_01.code);
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectIdToHaveText('discount', '-50,000 ₫');
    await expectElementVisible('finalCost');
  });

  it('LINE 136 - Asker cancel the task include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);

    await tapText('Hoạt động');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('discount', '-50,000 ₫');

    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: TASK.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          status: 'CONFIRMED',
        },
      },
    ]);
    await tapId('btnEditTask');
    // await tapText('Ngày giờ làm việc');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementVisible(
      'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
      'text',
    );
    await tapText('Tiếp tục');
    await expectElementNotVisible('50,000 ₫', 'text');
    await waitForElement('postTaskNow', 1000);
    await expectElementNotVisible('taskDuration0');

    await device.reloadReactNative();
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-3');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION_01.code);
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });
});
