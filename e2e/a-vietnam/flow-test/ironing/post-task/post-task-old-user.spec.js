const { initData, tapId, tapText, postTask, expectIdToHaveText } = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};

describe('FILE: e2e/a-vietnam/flow-test/ironing/post-task/post-task-old-user.spec.js - Old user post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 21 - Old user post new task', async () => {
    await postTask('postTaskServiceIRONING');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  });
});
