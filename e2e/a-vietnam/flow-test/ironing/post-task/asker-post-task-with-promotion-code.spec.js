const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  waitForElement,
  postTask,
  expectElementVisible,
  typePromotionCode,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const PROMOTION = {
  isoCode: 'VN',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};

describe('FILE: e2e/a-vietnam/flow-test/ironing/post-task/asker-post-task-with-promotion-code.spec.js - Asker post task with promotion code', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('promotion/create-promotion-code', [PROMOTION]);
    await initData('promotion/usersAppliedPromotion', {
      isoCode: 'VN',
      phone: ASKER.phone,
      promotionCode: 'lmn123',
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 46 - Asker post task with promotion code money "def123"', async () => {
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-2');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION.code);
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectIdToHaveText('discount', '-50,000 ₫');
    await expectElementVisible('finalCost');
  });
});
