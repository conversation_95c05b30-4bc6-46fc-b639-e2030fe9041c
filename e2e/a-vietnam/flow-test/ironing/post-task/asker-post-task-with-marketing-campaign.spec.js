const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  expectElementVisible,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER_01 = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const ASKER_02 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Asker 02',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

describe('FILE: e2e/a-vietnam/flow-test/ironing/post-task/asker-post-task-with-marketing-campaign.spec.js - Asker post task with marketing campaign', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_01, ASKER_02]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'VN',
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: 30000,
      },
    ]);
    await initData('campaign/createMarketingCampaign', {
      isoCode: 'VN',
      status: 'ACTIVE',
      code: 'def123',
      serviceName: 'CLEANING',
      type: 'PROMOTION',
      primaryNavigate: 'PostTaskStep1',
      secondaryNavigate: 'Home',
    });
    await E2EHelpers.onHaveLogin(ASKER_02.phone, '123456');
  });

  it('LINE 49 - Asker post task with promotion code money "def123"', async () => {
    await tapId('marketingCampaign_PROMOTION');
    await tapText('Đăng việc ngay');
    await tapId('chooseDuration-3');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount', '-50,000 ₫');
    await expectElementVisible('finalCost');
  });
});
