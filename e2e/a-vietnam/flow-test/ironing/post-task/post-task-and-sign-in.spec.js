const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  loginWithModal,
  signUpWithModal,
  forgotPasswordWithModal,
  scroll,
  swipe,
  ADDRESS_KEY,
  expectElementVisible,
  waitForElement,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const postTaskToStep4 = async () => {
  await postTask('postTaskServiceIRONING', ADDRESS_KEY.HCM);
  await expectElementVisible('lbPrice');
  await tapId('btnNextStep2');
  await tapId('btnNextStep3');
  await waitForElement('scrollViewStep4', 500);
  await swipe('scrollViewStep4', 'up');
  await tapId('choosePaymentMethod');
  await tapId('paymentMethodCash');
  await tapText('Đăng việc');
};

const checkDataAfterPosttask = async () => {
  await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
  await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  await tapId('taskDuration0');
  await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
  await swipe('scrollTaskDetail', 'up');
  await expectElementVisible('finalCost');
};

describe('FILE: e2e/a-vietnam/flow-test/ironing/post-task/post-task-and-sign-in.spec.js - New User post cleaning task before sign in', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.byPassUnauthorize();
    await E2EHelpers.onHaveLogout();
  });

  it('LINE 48 - [REQUIRED] - New customer post cleaning task and sign in', async () => {
    await postTaskToStep4();
    await loginWithModal(ASKER.phone, '123456');
    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });

  it('LINE 61 - [REQUIRED] - New customer post cleaning task and sign up', async () => {
    await postTaskToStep4();
    await signUpWithModal('Bill Gate', '0939504182');

    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });

  it('LINE 75 - [REQUIRED] - New customer post cleaning task and for got password', async () => {
    await postTaskToStep4();
    await forgotPasswordWithModal('0834567890');

    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });
});
