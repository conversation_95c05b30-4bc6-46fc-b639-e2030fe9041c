const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  waitForElement,
  typeToTextField,
  expectElementVisible,
  swipe,
  scroll,
  tapHeaderBack,
  getDateFromWeekday,
  getPriceSubscription,
  formatMoney,
} = require('../../../step-definition');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const F_MAIN_ACCOUNT = *********;
describe('FILE: e2e/a-vietnam/flow-test/subscription-office-cleaning/posted-task.spec.js - New User posted task Office cleaning Subscription', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: F_MAIN_ACCOUNT },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 40 - [REQUIRED] - New customer post Subscription office cleaning with bPay', async () => {
    const schedule = getDateFromWeekday([0, 1, 2, 3, 4, 5, 6], 1);
    const price = await getPriceSubscription({
      schedule: schedule,
      serviceName: 'OFFICE_CLEANING_SUBSCRIPTION',
      duration: 3,
      detailOfficeCleaning: {
        area: 100,
        duration: 2,
        numberOfTaskers: 1,
      },
      requirements: [
        {
          type: 6,
        },
        {
          type: 7,
        },
      ],
    });
    await postTask('postTaskServiceOFFICE_CLEANING');
    // Chọn dịch vụ
    await tapText('Dịch vụ theo gói tháng');
    // Chọn ngày làm
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await tapId('DayOfWeek3');
    await tapId('DayOfWeek4');
    await tapId('DayOfWeek5');
    await tapId('DayOfWeek6');
    // Thời gian làm việc
    await expectElementVisible('Chọn lịch làm việc', 'text');
    // Loại gói
    await expectElementVisible('Loại gói', 'text');
    await tapId('moth0');
    // Scroll
    await scroll('scrollStep2', 500, 'down', 0.5, 0.5);
    await tapId('chooseServies-0');
    await tapId('chooseServies-1');
    // Chọn thời lượng
    // 100m2
    await tapId('area_0');
    await expectElementVisible('lbPrice', `${formatMoney(price.totalCost)} VND`);
    await expectElementVisible('numberSessions', `${schedule.length} buổi`);
    // step 2
    await tapId('btnNextStep2');
    await waitForElement('Ghi chú cho Tasker', 500, 'text');
    await typeToTextField('taskNote', 'bTaskee xin chào');
    await tapText('Tiếp tục');
    // step 3
    await waitForElement('Vị trí làm việc', 500, 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('3 giờ, 08:00 đến 11:00', 'text');
    await scroll('scrollStep4', 500, 'down', 0.5, 0.5, 0.5);

    // Chi tiết thanh toán
    await expectElementVisible('Chi tiết thanh toán', 'text');
    await expectElementVisible('VAT', 'text');
    await expectElementVisible('Giá dịch vụ', 'text');
    await expectElementVisible(`${formatMoney(price.baseCost)} ₫`, 'text');
    await expectElementVisible(`${formatMoney(price.vat)} ₫`, 'text');

    // VAT
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail', 'id');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');
    await tapText('Gửi yêu cầu');
    await swipe('scrollStep4', 'up');
    await expectElementVisible('Phương thức thanh toán', 'text');
    await tapText('Chọn trong danh sách', 'text');
    // bPay
    await tapText('bPay');
    await expectIdToHaveText('price', `${formatMoney(price.totalCost)} VND`);
    await tapText('Đặt gói');
    await tapText('Xem chi tiết');
  });

  it('LINE 123 - New customer post Subscription office cleaning with Momo', async () => {
    const schedule = getDateFromWeekday([0, 1], 1);
    await postTask('postTaskServiceOFFICE_CLEANING');
    // Chọn dịch vụ
    await tapText('Dịch vụ theo gói tháng');
    // Chọn ngày làm
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');

    // Thời gian làm việc
    await expectElementVisible('Chọn lịch làm việc', 'text');
    // Loại gói
    await expectElementVisible('Loại gói', 'text');
    await tapId('moth0');
    // Scroll
    await scroll('scrollStep2', 500, 'down', 0.5, 0.5);
    // Chọn thời lượng
    // 100m2
    await tapId('area_0');
    await expectElementVisible('numberSessions', `${schedule.length} buổi`);
    // step 2
    await tapId('btnNextStep2');
    await waitForElement('Ghi chú cho Tasker', 500, 'text');
    await typeToTextField('taskNote', 'bTaskee xin chào');
    await tapText('Tiếp tục');
    // step 3
    await waitForElement('Vị trí làm việc', 500, 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('2 giờ, 08:00 đến 10:00', 'text');
    await scroll('scrollStep4', 500, 'down', 0.5, 0.5);
    // VAT
    await waitForElement('btnChangeCompanyEmailAddress', 1000, 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail', 'id');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');
    await tapText('Gửi yêu cầu');
    await swipe('scrollStep4', 'up');
    await expectElementVisible('Phương thức thanh toán', 'text');
    await tapText('Chọn trong danh sách', 'text');
    // bPay
    await tapText('MoMo');
  });
});
