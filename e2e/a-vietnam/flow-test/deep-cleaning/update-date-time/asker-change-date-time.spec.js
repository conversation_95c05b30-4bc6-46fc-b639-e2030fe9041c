/**
 * @description Change datetime Deep Cleaning task
 *   case 1: Asker want to change task datetime - POSTED
 *   case 2: Asker want to change task datetime - WAITING
 *   case 3: Asker want to change task datetime - CONFIRMED
 *   case 4: Asker want to change deep cleaning task time include promotion
 *   case 5: Asker want to change deep cleaning task time include promotion 156m2 720k
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  swipe,
  postTask,
  waitForElement,
  expectElementNotVisible,
  typePromotionCode,
  selectTime,
  expectElementVisibleAtIndex,
  selectTime24h,
} = require('../../../../step-definition');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASKER2 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 9,
};

const TASK = {
  isoCode: 'VN',
  serviceName: 'DEEP_CLEANING',
  askerPhone: ASKER.phone,
  description: 'My Task',
};

const checkTimeChangeSuccess = async () => {
  await tapText('Đồng ý');
  await waitForElement('Cập nhật thành công', 500, 'text');
  await tapText('Đóng');
  await tapId('taskMy Task');
  await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
  await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
  await expectIdToHaveText('duration', '4 giờ, 16:00 đến 20:00');
};

describe('FILE: e2e/a-vietnam/flow-test/deep-cleaning/update-date-time/asker-change-date-time.spec.js - Change datetime Deep Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 77 - Asker want to change task datetime - POSTED', async () => {
    await initData('task/createTask', [TASK]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Mới đăng', 0, 'text');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
      500,
      'text',
    );
    await tapText('Đóng');
    await selectTime24h(16, 22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await checkTimeChangeSuccess();
  });

  it('LINE 102 - Asker want to change task datetime - WAITING', async () => {
    await initData('task/createTask', [TASK]);
    await initData('task/acceptedTask', [
      {
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: [TASKER1.phone],
        description: TASK.description,
        isoCode: 'VN',
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Chờ người nhận', 0, 'text');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(16);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await checkTimeChangeSuccess();
  });

  it('LINE 124 - Asker want to change disinfection task time include promotion 156m2 720k', async () => {
    const PROMOTION = {
      isoCode: 'VN',
      code: 'abc123',
      value: 50000,
      target: 'ASKER',
      typeOfPromotion: 'BOTH',
      typeOfValue: 'MONEY',
      limit: 100,
      maxValue: '',
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);
    await postTask('postTaskServiceDEEP_CLEANING');
    await tapId('area150');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('price', '1,080,000 VND');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectIdToHaveText('txtPromotionCode', PROMOTION.code);
    await expectIdToHaveText('originPrice', '1,080,000 VND');
    await expectIdToHaveText('price', '1,030,000 VND');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');

    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    const nextDay = moment().add(3, 'd');
    if (moment().add(1, 'd').date() !== 1 && moment().month() !== nextDay.month()) {
    }
    await tapId(`days_${nextDay.date()}`);
    await selectTime24h(16);
    await tapText('Đồng ý');
    await expectIdToHaveText('lbPrice', '1,030,000 VND/4h');
    await tapId('btnUpdateDateTime');
    await checkTimeChangeSuccess();
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '1,030,000 ₫');
  });

  it('LINE 169 - Go update task for the first time, no call the api get price', async () => {
    await initData('task/createTask', [TASK]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Mới đăng', 0, 'text');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    // await expectElementNotVisible('btnUpdateDateTime');
    await selectTime(10, true, 'AM');
    await tapText('Đồng ý');
    await expectElementVisible('btnUpdateDateTime');
  });
});
