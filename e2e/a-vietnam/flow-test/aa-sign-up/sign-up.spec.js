const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  typeToTextField,
  expectIdToHaveText,
  waitForElement,
  tapText,
  fillActiveCode,
  postTask,
  swipe,
  expectElementVisible,
  tapIdAtIndex,
  signUpWithModal,
  reloadApp,
  expectElementNotVisible,
  clearTextInput,
  ADDRESS_KEY,
  expectElementNotExist,
  tapHeaderBack,
} = require('../../../step-definition');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  email: '<EMAIL>',
  referralCode: 'asker123',
};

const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

describe('FILE: e2e/a-vietnam/flow-test/aa-sign-up/sign-up.spec.js - Sign up Asker', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.byPassUnauthorize();
    try {
      await waitForElement('cancelVerify', 1000);
      await tapId('cancelVerify');
    } catch (error) {}
    await E2EHelpers.onHaveLogout();
  });

  afterEach(async () => {
    try {
      await tapHeaderBack();
    } catch (error) {}
  });

  it('LINE 44 - [REQUIRED] - New Asker sign up without sign up promotion code', async () => {
    // Click login button in home screen and open modal
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');
    await signUpWithModal('bill gate', '0834567898');
    await waitForElement('Tab_Activity', 500);
  });

  it('LINE 52 - [REQUIRED] - New Asker sign up with exist phone number', async () => {
    // TODO: Run on Android
    // Click login button in home screen and open modal
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');

    await typeToTextField('txtName', 'Nathan');
    await typeToTextField('txtPhone', ASKER.phone);
    await typeToTextField('txtEmail', '<EMAIL>');
    await expectElementVisible('Số điện thoại đã được sử dụng. Vui lòng chọn số khác!', 'text');
  });

  it('LINE 70 - [REQUIRED] - New Asker sign up with exists email', async () => {
    // await initData('user/updateUser', [USER_ASKER_DATA_UPDATE]);

    // Click login button in home screen and open modal
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');

    await typeToTextField('txtName', 'Nathan');
    await typeToTextField('txtPhone', '0923456789');
    await typeToTextField('txtEmail', ASKER.email);
    await expectElementVisible('Email đã được đăng ký.', 'text');
  });

  it('LINE 91 - New Asker sign up with short name and check referral code', async () => {
    const PHONE = '0923456789';
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtName', 'Linh');
    await typeToTextField('txtPhone', PHONE);
    await typeToTextField('txtEmail', '<EMAIL>');
    await tapId('checkboxPolicy');
    await tapId('btnSignup');
    await waitForElement('Xác thực tài khoản', 1000, 'text');
    await initData('user/activationCodeExist', { phone: PHONE });
    await fillActiveCode(PHONE);
    await typeToTextField('txtPassword', '113114115');
    await typeToTextField('txtSecondPassword', '113114115');
    try {
      await tapIdAtIndex('btnSavePassword', 0);
    } catch (e) {
      await tapIdAtIndex('btnSavePassword', 1);
    }
    await waitForElement('Tab_Activity', 500);
    await tapText('Tài khoản');
    await tapText('Săn quà giới thiệu');
    const referralCode = await initData('user/getUserByPhone', { phone: PHONE });
    await expectIdToHaveText('referralCode', referralCode.referralCode?.toUpperCase());
  });

  // Does not have this feature yet
  it('LINE 118 - New Asker sign up with referral code', async () => {
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtName', 'Linh');
    await typeToTextField('txtPhone', '0923456789');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtSignUpPromotionCode', ASKER.referralCode);
    await tapId('checkboxPolicy');
    await tapId('btnSignup');
    await waitForElement('Xác thực tài khoản', 1000, 'text');
    await initData('user/activationCodeExist', { phone: '0923456789' });
    await fillActiveCode('0923456789');
    await typeToTextField('txtPassword', '113114115');
    await typeToTextField('txtSecondPassword', '113114115');
    try {
      await tapIdAtIndex('btnSavePassword', 0);
    } catch (e) {
      await tapIdAtIndex('btnSavePassword', 1);
    }
    await waitForElement('Tab_Activity', 500);
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.HCM);
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapText('Khuyến mãi');
    await tapText('Sử dụng');
    await expectIdToHaveText('originPrice', '210,000 VND');
    await expectIdToHaveText('price', '180,000 VND');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '210,000 ₫');
    await expectIdToHaveText('discount', '-30,000 ₫');
    await expectIdToHaveText('finalCost', '180,000 ₫');
  });

  it('LINE 163 - New Asker sign up with rule password', async () => {
    const PHONE = '0923456789';
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtName', 'Linh');
    await typeToTextField('txtPhone', PHONE);
    await typeToTextField('txtEmail', '<EMAIL>');
    await tapId('checkboxPolicy');
    await tapId('btnSignup');
    await waitForElement('Xác thực tài khoản', 1000, 'text');
    await initData('user/activationCodeExist', { phone: PHONE });
    await fillActiveCode(PHONE);
    await typeToTextField('txtPassword', '123456');
    await waitForElement(
      'Mật khẩu phải có ít nhất 6 ký tự và không được trùng với số điện thoại, trong đó không chứa 3 chữ số liên tiếp hay 3 chữ số liền kề giống nhau. Ví dụ: 123, 321, 888,...',
      1000,
      'text',
    );

    await clearTextInput('txtPassword');
    await typeToTextField('txtPassword', '112113114');
    await expectElementNotVisible(
      'Mật khẩu phải có ít nhất 6 ký tự và không được trùng với số điện thoại, trong đó không chứa 3 chữ số liên tiếp hay 3 chữ số liền kề giống nhau. Ví dụ: 123, 321, 888,...',
      1000,
      'text',
    );

    await clearTextInput('txtPassword');

    await typeToTextField('txtPassword', '222222');
    await waitForElement(
      'Mật khẩu phải có ít nhất 6 ký tự và không được trùng với số điện thoại, trong đó không chứa 3 chữ số liên tiếp hay 3 chữ số liền kề giống nhau. Ví dụ: 123, 321, 888,...',
      1000,
      'text',
    );

    await clearTextInput('txtPassword');

    await typeToTextField('txtPassword', '112113114');
    await typeToTextField('txtSecondPassword', '123456');
    await waitForElement('Mật khẩu không khớp, vui lòng thử lại', 1000, 'text');

    await clearTextInput('txtSecondPassword');
    await typeToTextField('txtSecondPassword', '112113114');
    await expectElementNotVisible('Mật khẩu không khớp, vui lòng thử lại', 1000, 'text');

    try {
      await tapIdAtIndex('btnSavePassword', 0);
    } catch (e) {
      await tapIdAtIndex('btnSavePassword', 1);
    }
    await waitForElement('Tab_Activity', 500);
    await tapText('Tài khoản');
    await tapText('Săn quà giới thiệu');
    const referralCode = await initData('user/getUserByPhone', { phone: PHONE });
    await expectIdToHaveText('referralCode', referralCode.referralCode?.toUpperCase());
  });

  it('LINE 229 - New Asker sign up and resend code', async () => {
    const PHONE = '0923456789';
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtName', 'Linh');
    await typeToTextField('txtPhone', PHONE);
    await typeToTextField('txtEmail', '<EMAIL>');
    await tapId('checkboxPolicy');
    await tapId('btnSignup');
    await waitForElement('Xác thực tài khoản', 1000, 'text');
    await tapText('Gửi lại mã');
    await tapText('SMS');
    await waitForElement('Gửi lại mã', 2000, 'text');
    await tapText('Gửi lại mã');
    await tapText('SMS');
    await waitForElement('Gửi lại mã', 2000, 'text');
    await tapText('Gửi lại mã');
    await tapText('SMS');
    await expectElementNotExist('Gửi lại mã', 'text');
  });
});
