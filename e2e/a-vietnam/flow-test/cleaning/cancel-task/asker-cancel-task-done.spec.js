/**
 * @description Asker cancel task done
 *   case 1: LINE 28 - Asker cancel posted task
 * */

const {
  initData,
  tapId,
  swipe,
  tapText,
  expectElementNotVisible,
  expectElementVisible,
  waitForElement,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
describe('FILE: e2e/a-vietnam/flow-test/cleaning/cancel-task/asker-cancel-task-done.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
      },
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 02',
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 55 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('TAB_UPCOMINGDon dep nha 01');

    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'VN',
        dataUpdate: {
          status: 'DONE',
        },
      },
    ]);
    await initData('task/acceptedTask', [
      { isoCode: 'VN', description: 'Don dep nha 01', taskerAccepted: [TASKER.phone], status: 'DONE' },
    ]);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementVisible('Bạn không thể hủy công việc đã hoàn thành.', 'text');
    await tapText('Đóng');
  });
});
