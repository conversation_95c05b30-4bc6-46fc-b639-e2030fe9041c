/**
 * @description Asker cancel task
 *   case 1: LINE 32 - <PERSON><PERSON> cancel posted task
 *   case 2: <PERSON><PERSON><PERSON> 49 - Ask<PERSON> cancel posted task and other task of blacklist Asker
 * */

const {
  initData,
  tapId,
  tapText,
  swipe,
  expectElementVisible,
  waitForElement,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER2 = {
  isoCode: 'VN',
  phone: '0834567892',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

describe('FILE: e2e/a-vietnam/flow-test/cleaning/cancel-task/asker-cancel-task-and-tasker-view-conflict-tasks.spec.js - Asker cancel task. Tasker will see the conflict tasks', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Task 01',
        visibility: 3,
      },
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567892',
        description: 'Task 02',
        visibility: 3,
      },
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Task 03',
        visibility: 3,
        viewedTaskers: [TASKER2.phone],
      },
    ]);

    const tasker = await initData('user/getUserByPhone', { phone: TASKER1.phone });
    await initData('task/updateTask', [
      {
        description: 'Task 03',
        isoCode: 'VN',
        dataUpdate: {
          status: 'CONFIRMED',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 89 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGTask 03');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    const tasker = await initData('user/getUserByPhone', { phone: TASKER1.phone });
    const task1 = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'Task 01' });
    expect(task1.viewedTaskers).to.include(tasker._id);
    const task2 = await initData('task/getTaskByDescription', { isoCode: 'VN', description: 'Task 02' });
    expect(task2.viewedTaskers).to.include(tasker._id);
  });
});
