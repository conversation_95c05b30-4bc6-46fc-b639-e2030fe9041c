/**
 * @description Asker cancel task limit
 *   case 1: LINE 21 - Asker cancel task with task createdAt + 10 minutes is less than or equal current date and total task canceled is less than limit
 *   case 2: LINE 42 - Ask<PERSON> cancel many tasks within 24h
 *   case 3: LINE 112 - Ask<PERSON> cancel task with task createdAt + 10 minutes is greater than current date
 * */

const {
  initData,
  tapId,
  tapText,
  expectElementNotExist,
  waitForElement,
  expectElementVisible,
  swipe,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
describe('FILE: e2e/a-vietnam/flow-test/cleaning/cancel-task/asker-cancel-task-limit.spec.js - <PERSON><PERSON> cancel task limit', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 39 - Asker cancel task with task createdAt + 10 minutes is less than or equal current date and total task canceled is less than limit', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
        status: 'POSTED',
      },
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 02',
        status: 'CANCELED',
      },
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 03',
        status: 'CANCELED',
      },
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 04',
        status: 'CANCELED',
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Ngày giờ làm việc');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 1000, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDuration0');
  });

  it('LINE 247 - Asker cancel task with task createdAt + 10 minutes is greater than current date', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'VN',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
        status: 'POSTED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'VN',
        dataUpdate: {
          createdAt: moment().subtract(-11, 'minute').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');

    await tapId('TAB_UPCOMINGDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // await tapText('Đồng ý');
    await waitForElement('Không cần công việc này nữa.', 1000, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGDon dep nha 05');
  });
});
