const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  loginWithModal,
  signUpWithModal,
  forgotPasswordWithModal,
  scroll,
  swipe,
  ADDRESS_KEY,
  expectElementVisible,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const postTaskToStep4 = async () => {
  await postTask('postTaskServiceCLEANING', ADDRESS_KEY.HCM);
  await scroll('scrollStep2Cleaning', 500, 'down', 0.5, 0.5);
  await tapId('chooseServies-0');
  await expectElementVisible('lbPrice');
  await tapId('btnNextStep2');
  await tapId('btnNextStep3');
  await swipe('scrollViewStep4', 'up');
  await tapId('choosePaymentMethod');
  await tapId('paymentMethodCash');
  await tapText('Đăng việc');
};

const checkDataAfterPosttask = async () => {
  await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
  await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  await tapId('taskDuration0');
  await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
  await swipe('scrollTaskDetail', 'up');
  await expectIdToHaveText('finalCost', '280,000 ₫');
  await expectIdToHaveText('valueOptionals', 'Nấu ăn');
};

describe('FILE: e2e/a-vietnam/flow-test/cleaning/post-task/post-task-and-sign-in.spec.js - New User post cleaning task before sign in', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.byPassUnauthorize();
    await E2EHelpers.onHaveLogout();
  });

  it('LINE 48 - [REQUIRED] - New customer post cleaning task and sign in', async () => {
    await postTaskToStep4();
    await loginWithModal(ASKER.phone, '123456');
    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });

  it('LINE 61 - [REQUIRED] - New customer post cleaning task and sign up', async () => {
    await postTaskToStep4();
    await signUpWithModal('Bill Gate', '0939504182');

    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });

  it('LINE 75 - [REQUIRED] - New customer post cleaning task and for got password', async () => {
    await postTaskToStep4();
    await forgotPasswordWithModal('0834567890');

    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });
});
