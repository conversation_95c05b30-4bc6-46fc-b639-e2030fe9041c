/**
 * @description New User post task
 *   case 1: New User post deep cleaning task
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  swipe,
  expectIdToHaveText,
  expectElementVisible,
  expectIdToHaveTextAtIndex,
  reloadApp,
} = require('../../../../step-definition');
const { ASKER_INDO } = require('../../../../helpers/constants');

const TASKER = {
  isoCode: 'ID',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

describe('FILE: e2e/c-indonesia/flow-test/deep-cleaning/post-task/post-task-new-user.spec.js - New User post task', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [ASKER_INDO, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
  });

  it('LINE 41 - New User post deep cleaning task', async () => {
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectIdToHaveText('lbPrice', '640,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('4 giờ, 14:00 đến 18:00', 'text');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectIdToHaveText('area', 'Tối đa 80m²');
    await expectIdToHaveText('numberOfTasker', '2 người');
    await expectIdToHaveText('price', '640,000 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveTextAtIndex('areaNumber', 'Tối đa 80m² - 2 người', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '640,000 Rp');
    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Tối đa 80m²', 'text');
    await expectElementVisible('Thời lượng', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectElementVisible('2 người', 'text');
    await expectIdToHaveText('finalCost', '640,000 Rp');
  });
});
