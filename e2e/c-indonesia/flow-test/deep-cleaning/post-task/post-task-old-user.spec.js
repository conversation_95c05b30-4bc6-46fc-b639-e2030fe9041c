/**
 * @description Old user post task
 *   case 1: Old user post deep cleaning task
 *   case 2: user post task pay by bpay (not enough money
 *   case 3: user post task pay by bpay (enough money
 *   case 4: User post task weekend and check price
 *   case 5: User post task with promotion
 *   case 6: User post task with promotion
 *   case 7: User post task change address, caculate price again
 *   case 8: LINE 339 - DEEP_CLEANING Prepayment GO_PAY
 *   case 9: LINE 362 - DEEP_CLEANING Prepayment DANA
 *   case 10: LINE 383 - DEEP_CLEANING Prepayment QRIS
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  waitForElement,
  swipe,
  expectIdToHaveText,
  expectElementVisible,
  expectIdToHaveTextAtIndex,
  expectElementNotVisible,
  typePromotionCode,
  reloadApp,
  scroll,
  isWeekend,
} = require('../../../../step-definition');
const { ASKER_INDO } = require('../../../../helpers/constants');

const { device } = require('detox');

const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  FMainAccount: 1000000,
};

describe('FILE: e2e/c-indonesia/flow-test/deep-cleaning/post-task/post-task-old-user.spec.js - Old user post task', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [ASKER_INDO, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
  });
  it('LINE 57 - Old user post deep cleaning task', async () => {
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('4 giờ, 14:00 đến 18:00', 'text');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectIdToHaveText('area', 'Tối đa 80m²');
    await expectIdToHaveText('numberOfTasker', '2 người');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveTextAtIndex('areaNumber', 'Tối đa 80m² - 2 người', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Tối đa 80m²', 'text');
    await expectElementVisible('Thời lượng', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectElementVisible('2 người', 'text');
    await expectElementVisible('finalCost');
  });

  it('LINE 101 - user post task pay by bpay (not enough money)', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: 0 },
    });
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('4 giờ, 14:00 đến 18:00', 'text');
    await expectElementVisible('Chi tiết công việc', 'text');
    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectIdToHaveText('area', 'Tối đa 80m²');
    await expectIdToHaveText('numberOfTasker', '2 người');
    await expectElementVisible('price');

    // choose pay by bpay
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Nạp thêm', 'text');
  });

  it('LINE 135 - user post task pay by bpay (success)', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_INDO.phone,
        isoCode: ASKER_INDO.isoCode,
        financialAccountData: {
          ID_FMainAccount: 5000000,
        },
      },
    ]);
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('4 giờ, 14:00 đến 18:00', 'text');
    await expectElementVisible('Chi tiết công việc', 'text');
    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectIdToHaveText('area', 'Tối đa 80m²');
    await expectIdToHaveText('numberOfTasker', '2 người');
    await expectElementVisible('price');

    // choose pay by bpay
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveTextAtIndex('areaNumber', 'Tối đa 80m² - 2 người', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
    await expectElementVisible('finalCost');
  });

  it('LINE 184 - User post task weekend and check price', async () => {
    //Không run vào t7 và cn
    if (isWeekend()) {
      return;
    }
    await initData('service/update-feeWeekendApplyForCity', {
      isoCode: 'ID',
      serviceName: 'DEEP_CLEANING',
      city: ['Jakarta'],
    });
    await device.reloadReactNative();
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');

    await tapId('btnNextStep2');
    try {
      await tapText('CN');
    } catch (e) {
      await tapText('T7');
    }
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('4 giờ, 14:00 đến 18:00', 'text');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectIdToHaveText('area', 'Tối đa 80m²');
    await expectIdToHaveText('numberOfTasker', '2 người');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveTextAtIndex('areaNumber', 'Tối đa 80m² - 2 người', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
    await expectElementVisible('Tối đa 80m²', 'text');
    await expectElementVisible('2 người', 'text');
  });
  it('LINE 239 - User post task with promotion', async () => {
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'ID',
        code: 'abc123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
    ]);
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('price');

    // fill promotion code
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectElementVisible('2 người', 'text');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });

  it('LINE 282 - User post task weekend with promotion', async () => {
    //Không run vào t7 và cn
    if (isWeekend()) {
      return;
    }
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'ID',
        code: 'abc123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
    ]);
    await initData('service/update-feeWeekendApplyForCity', {
      isoCode: 'ID',
      serviceName: 'DEEP_CLEANING',
      city: ['Jakarta'],
    });
    await device.reloadReactNative();
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');

    await tapId('btnNextStep2');
    try {
      await tapText('CN');
    } catch (e) {
      await tapText('T7');
    }
    // POST TASK STEP 2
    await tapId('btnNextStep3');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('price');
    // fill promotion code
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');

    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
    await expectElementVisible('2 người', 'text');
  });

  it('LINE 338 - User post task change address, caculate price again', async () => {
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');
    await tapId('openModalAddress');
    await tapId('address0');

    // show price button
    await expectElementNotVisible('lbPrice');
    await expectElementNotVisible('640,000 IDR/4h', 'text');
  });

  it('LINE 351 - DEEP_CLEANING Prepayment GO_PAY', async () => {
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('GoPay');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('amountConfirmedPayment-GO_PAY');
    await tapText('Hủy');
    await tapText('Thanh toán lại');
    await swipe('scrollTaskDetail', 'up');
    await tapId('choosePaymentMethod');
    await tapText('GoPay');
    await tapText('Đăng việc');
    await expectElementVisible('amountConfirmedPayment-GO_PAY');
  });

  it('LINE 372 - DEEP_CLEANING Prepayment DANA', async () => {
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('DANA');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('amountConfirmedPayment-DANA');
    await tapText('Hủy');
    await tapText('Thanh toán lại');
    await swipe('scrollTaskDetail', 'up');
    await tapId('choosePaymentMethod');
    await tapText('DANA');
    await tapText('Đăng việc');
    await expectElementVisible('amountConfirmedPayment-DANA');
  });

  it('LINE 393 - DEEP_CLEANING Prepayment QRIS', async () => {
    await postTask('postTaskServiceDEEP_CLEANING', 'Jakarta Barat');
    // POST TASK STEP 2
    await tapId('area80');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('QRIS');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Lưu hình', 'text');
    await device.reloadReactNative();
    await tapText('Hoạt động');
    await tapText('Thanh toán lại');
    await swipe('scrollTaskDetail', 'up');
    await tapId('choosePaymentMethod');
    await tapText('QRIS');
    await tapText('Đăng việc');
    await expectElementVisible('Lưu hình', 'text');
  });
});
