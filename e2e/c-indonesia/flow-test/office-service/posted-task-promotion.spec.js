/**
 * case 1: <PERSON><PERSON><PERSON> 130 - <PERSON><PERSON> post task office cleaning with promotion code money "def123"
 * case 1: LINE 172 - <PERSON><PERSON> post task office cleaning with promotion code percentage "opq123"
 * case 1: LINE 191 - OFFICE_CLEANING promotion
 * */
const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  typeToTextField,
  expectIdToHaveText,
  waitForElement,
  swipe,
  expectElementVisible,
  typePromotionCode,
  scroll,
  ADDRESS_KEY,
} = require('../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const ASKER1 = {
  isoCode: 'ID',
  phone: '0834567891',
  name: '<PERSON>er 02',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const PROMO_ABC123 = {
  isoCode: 'ID',
  code: 'abc123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'CURRENT',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};
const PROMO_DEF123 = {
  isoCode: 'ID',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};
const PROMO_GHK123 = {
  isoCode: 'ID',
  code: 'ghk123',
  value: 50000,
  target: 'TASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};
const PROMO_LMN123 = {
  isoCode: 'ID',
  code: 'lmn123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 1,
  maxValue: '',
};
const PROMO_OPQ123 = {
  isoCode: 'ID',
  code: 'opq123',
  value: 0.4,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'PERCENTAGE',
  limit: 100,
  maxValue: '',
};
const PROMO_OPQ789 = {
  isoCode: 'ID',
  code: 'opq789',
  value: 0.4,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'PERCENTAGE',
  limit: 100,
  maxValue: 30000,
};
const F_MAIN_ACCOUNT = 1000000;
describe('FILE: e2e/c-indonesia/flow-test/office-service/posted-task-promotion.spec.js - Post task promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, ASKER1]);
    await initData('promotion/create-promotion-code', [
      PROMO_ABC123,
      PROMO_DEF123,
      PROMO_GHK123,
      PROMO_LMN123,
      PROMO_OPQ123,
      PROMO_OPQ789,
    ]);
    await initData('promotion/usersAppliedPromotion', {
      isoCode: 'ID',
      phone: ASKER1.phone,
      promotionCode: PROMO_LMN123.code,
    });
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'ID',
      financialAccountData: { ID_FMainAccount: F_MAIN_ACCOUNT },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  const pressCode = async (code) => {
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(code);
  };

  // def123
  it('LINE 131 - Asker post task office cleaning with promotion code money "def123"', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.ID);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 IDR/2h');
    // step 2 & 3
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await swipe('scrollViewStep4', 'up');
    // VAT
    await expectElementVisible('txtPanel', 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail', 'id');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await pressCode(PROMO_DEF123.code);
    await expectIdToHaveText('price', '169,400 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('Giảm giá', 'text');
    await expectElementVisible('Phương thức thanh toán', 'text');
    await expectIdToHaveText('finalCost', '169,400 Rp');
  });

  // TODO: Open when update API
  // opq123
  it.skip('LINE 167 - Asker post task office cleaning with promotion code percentage "opq123"', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.ID);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 IDR/2h');
    // step 2 & 3
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await swipe('scrollViewStep4', 'up');
    // VAT
    await expectElementVisible('txtPanel', 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail', 'id');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await pressCode(PROMO_OPQ123.code);
    await expectIdToHaveText('price', '149,600 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('Giảm giá', 'text');
    await expectElementVisible('Phương thức thanh toán', 'text');
    await expectIdToHaveText('finalCost', '149,600 Rp');
  });

  // TODO: Open when update API
  // lmn123
  it.skip('LINE 203 - OFFICE_CLEANING promotion', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.ID);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 IDR/2h');
    // step 2 & 3
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await swipe('scrollViewStep4', 'up');
    // VAT
    await expectElementVisible('txtPanel', 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail', 'id');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await pressCode(PROMO_OPQ123.code);
    await pressCode('lmn123');
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });
});
