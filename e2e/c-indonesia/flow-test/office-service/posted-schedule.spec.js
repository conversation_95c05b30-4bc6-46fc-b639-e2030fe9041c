/**
 * case 1: LINE 73 - New customer post Office cleaning task and sign in and Status: DONE
 * */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  typeToTextField,
  expectElementVisible,
  swipe,
  scroll,
  tapHeaderBack,
  postTask,
  ADDRESS_KEY,
} = require('../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};

const F_MAIN_ACCOUNT = 1000000;

describe('FILE: e2e/c-indonesia/flow-test/office-service/posted-schedule.spec.js - New User posted task Office cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'ID',
      financialAccountData: { ID_FMainAccount: F_MAIN_ACCOUNT },
    });
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'ID',
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  // Status: DONE
  it('LINE 63 - New customer post Office cleaning task and sign in and Status: DONE', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.ID);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 IDR/2H');
    // step 2 & 3
    await tapId('btnNextStep2');
    // await tapId('cbWeeklyRepeater');
    // await tapText('Đồng ý');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    // VAT
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('txtPanel', 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await expectIdToHaveText('price', '224,400 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await initData('task/acceptedTask', [
      {
        isoCode: 'ID',
        status: 'DONE',
        taskerAccepted: [TASKER1.phone],
        description: 'My Task',
      },
    ]);
    await tapHeaderBack();
    await tapText('Lịch sử');
    await expectElementVisible('taskMy Task');
  });
});
