/**
 * LINE 61 - New customer post office cleanind and sign in
 */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  typeToTextField,
  expectElementVisible,
  swipe,
  scroll,
  reloadApp,
  postTask,
  loginWithModal,
  ADDRESS_KEY,
} = require('../../../step-definition');

const ASKER = {
  randomId: true,
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

const F_MAIN_ACCOUNT = 1000000;

describe('FILE: e2e/c-indonesia/flow-test/office-service/posted-task-sign-in.spec.js - New User posted task Office cleaning', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'ID',
      financialAccountData: { ID_FMainAccount: F_MAIN_ACCOUNT },
    });
    await E2EHelpers.byPassUnauthorize();
  });

  it('LINE 51 - New customer post office cleanind and sign in', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.ID);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 IDR/2h');
    // step 2 & 3
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await swipe('scrollViewStep4', 'up');
    // VAT
    await expectElementVisible('txtPanel', 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail', 'id');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '**********');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await tapText('Đăng việc');
    await loginWithModal(ASKER.phone, '123456', '+62');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapId('paymentMethodBpay');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
  });
});
