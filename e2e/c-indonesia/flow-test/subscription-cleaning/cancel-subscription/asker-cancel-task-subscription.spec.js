/**
 * @Filename: flow-test/subscription/cancel-subscription/asker-cancel-task-subscription.spec.js
 * @Description: Test case cancel subscription
 * @CreatedAt: 08/12/2020 11:06
 * @Author: HuuToan
 * @UpdatedAt: 17/02/2021 15:30
 * @UpdatedBy: HuuToan
 **/

/**
 *   Case 1: LINE 46: <PERSON><PERSON> post subscription and cant cancel subscription
 *   Case 2: LINE 65: Asker cant cancel task in subscription
 **/

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  waitForElement,
  swipe,
  postTask,
  typeToTextField,
  expectElementNotExist,
  expectElementVisible,
} = require('../../../../step-definition');
const expect = require('chai').expect;

const ASKER = {
  isoCode: 'ID',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

describe('FILE: e2e/c-indonesia/flow-test/subscription-cleaning/cancel-subscription/asker-cancel-task-subscription.spec.js - Asker cancel cleaning subscription', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 44 - Asker post subscription and cant cancel subscription', async () => {
    await postTask('postTaskServiceCLEANING_SUBSCRIPTION');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek3');
    await tapId('chooseDuration-2');
    await swipe('scrollStep2Cleaning', 'up');
    await tapId('moth0');
    await tapId('btnNextStep2');
    await typeToTextField('taskNote', 'lau don 2 phong ngu va nha ve sinh');
    await tapText('Tiếp tục');
    await swipe('scrollStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Chuyển khoản trực tiếp');
    await tapText('Đặt gói');
    await tapText('Hoàn tất');
    await expectElementVisible('TAB_SUBSCRIPTIONMy Task');
    await expectElementNotExist('task_0');
  });

  it('LINE 63 - Asker cant cancel task in subscription', async () => {
    await postTask('postTaskServiceCLEANING_SUBSCRIPTION');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek3');
    await tapId('chooseDuration-2');
    await swipe('scrollStep2Cleaning', 'up');
    await tapId('moth0');
    await tapId('btnNextStep2');
    await typeToTextField('taskNote', 'lau don 2 phong ngu va nha ve sinh');
    await tapText('Tiếp tục');
    await swipe('scrollStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Chuyển khoản trực tiếp');
    await tapText('Đặt gói');
    await tapText('Hoàn tất');

    await initData('subscription/updateSubscription', {
      description: 'My Task',
      isoCode: 'ID',
      data: { status: 'ACTIVE' },
    });
    await initData('subscription/postTaskInSubscription', { isoCode: 'ID', description: 'My Task' });

    await waitForElement('Tab_Activity', 500);
    await tapId('Tab_Activity');
    await waitForElement('Tab_Upcoming', 500);
    await tapId('Tab_Upcoming');
    await tapId('TAB_UPCOMINGMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapId('weekdays_1');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    try {
      await waitForElement('Cập nhật thành công', 500, 'text');
    } catch (error) {
      // Khi update có tiền chênh lệch
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
    }
    await tapText('Đóng');
    await expectElementVisible('txtPayForTasker');
    await tapId('TAB_UPCOMINGMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGMy Task');

    //LH a Toàn để biết chi tiết flow
    try {
      //Trường hợp chạy test buổi sáng phí hủy sẽ là 100%
      const data1 = await initData('user/findFATransaction', {
        phone: ASKER.phone,
        accountType: 'M',
        type: 'D',
        amount: 150000,
        isoCode: 'ID',
      });
      expect(data1.length).to.equal(1);
      expect(data1[0].amount).to.equal(150000);
    } catch (error) {
      //Trường hợp chạy test buổi tối phí hủy sẽ là 50%
      const data1 = await initData('user/findFATransaction', {
        phone: ASKER.phone,
        accountType: 'M',
        type: 'D',
        amount: 75000,
        isoCode: 'ID',
      });
      expect(data1.length).to.equal(1);
      expect(data1[0].amount).to.equal(75000);
    }
  });
});
