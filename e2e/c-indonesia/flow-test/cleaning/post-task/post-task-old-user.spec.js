/**
 * @description Old user post task (8 test cases)
 *   case 1: <PERSON><PERSON><PERSON> 30 - Already Loged-in and I want to post a Task for me
 *   case 2: <PERSON><PERSON><PERSON> 45 - Check Choose Tasker Does It Mean
 *   case 3: <PERSON><PERSON><PERSON> 59 - Old user post new task
 *   case 4: <PERSON><PERSON><PERSON> 88 - New User post task cleaning 2h
 *   case 5: <PERSON><PERSON><PERSON> 131 - New User post task cleaning 3h
 *   case 6: LINE 149 - New User post task cleaning 4h
 *   case 7: LINE 167 - bPay New User post task cleaning error
 *   case 8: LINE 187 - bPay New User post task cleaning
 *   case 9: LINE 369 - CLEANING Prepayment GO_PAY
 *   case 10: LINE 388 - CLEANING Prepayment DANA
 *   case 11: LINE 407 - CLEANING Prepayment QRIS
 * */

const {
  initData,
  tapId,
  tapText,
  postTask,
  waitForElement,
  scroll,
  expectIdToHaveText,
  expectElementVisible,
  expectElementNotVisible,
  expectElementVisibleAtIndex,
  tapHeaderBack,
  typeToTextField,
  scrollTo,
  tapTextAtIndex,
  swipe,
} = require('../../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');
const { ASKER_INDO } = require('../../../../helpers/constants');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/post-task-old-user.spec.js - Old user post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      ASKER_INDO,
      { isoCode: ASKER_INDO.isoCode, phone: '0834567891', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);

    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
  });

  it('LINE 55 - Already Loged-in and I want to post a Task for me', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat', 'My Task');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    const data = await initData('user/getUserByPhone', { phone: ASKER_INDO.phone, countryCode: '+62' });
    expect(data.cities[0].country).to.equal('ID');
    expect(data.cities[0].city).to.equal('Jakarta');
  });

  it('LINE 69 - Check Choose Tasker Does It Mean', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await scroll('scrollStep2Cleaning', 700, 'down', 0.5, 0.5);
    await expectIdToHaveText('txtChooseTasker', 'Bạn tự chọn Tasker');
    await tapId('whatIsChooseTasker');
    await expectElementVisible('Phí: 20,000 Rp', 'text');
    await tapText('Đã hiểu');
  });

  it('LINE 88 - Old user post new task', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapText('Trang chủ');
    await tapId('postTaskServiceCLEANING');
    await tapText('Đặt công việc mới');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Thông báo', 'text');
    await expectElementVisible('Bạn đang có 1 công việc vào giờ này, bạn muốn có thêm 1 Tasker nữa ?', 'text');
    await expectElementVisible('Đóng', 'text');
    await expectElementVisible('Đồng ý', 'text');
    await tapText('Đồng ý');
    await waitForElement('Đã có công việc được đăng vào thời gian này, vui lòng chọn thời gian khác', 500, 'text');
  });

  it('LINE 114 - New User post task cleaning 2h', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '320,000 IDR/4h');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await scroll('scrollStep2Cleaning', 500, 'down', 0.5, 0.5);
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('chooseServies-1');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('chooseServies-1');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('whatIsChooseTasker');
    await expectElementVisibleAtIndex('Bạn tự chọn Tasker', 0, 'text');
    await tapText('Đã hiểu');
    await tapId('ChooseTasker');
    await tapText('Đã hiểu');
    await expectIdToHaveText('lbPrice', '180,000 IDR/2h');
    await tapId('ChooseTasker');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '160,000 Rp');
  });

  it('LINE 152 - New User post task cleaning 3h', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '240,000 Rp');
  });

  it('LINE 169 - New User post task cleaning 4h', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '320,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '320,000 Rp');
  });

  it('LINE 186 - bPay New User post task cleaning error', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: 0 },
    });
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Thông báo', 'text');
    await expectElementVisible(
      'Bạn cần thêm 160,000 IDR để có thể đăng công việc này. Vui lòng nạp thêm hoặc chọn hình thức thanh toán khác.',
      'text',
    );
    await tapText('Đóng');
    await expectElementVisible('bPay', 'text');
    await expectElementNotVisible('Tiền mặt', 'text');
  });

  it('LINE 207 - bPay New User post task cleaning', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: 200000 },
    });
    await device.reloadReactNative();
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await expectElementNotVisible('Tiền mặt', 'text');
    await expectElementVisible('bPay', 'text');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementNotVisible('Thông báo', 'text');
    await expectElementNotVisible(
      'Bạn cần thêm 160,000 IDR để có thể đăng công việc này. Vui lòng nạp thêm hoặc chọn hình thức thanh toán khác.',
      'text',
    );
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '160,000 Rp');
    await expectIdToHaveText('paymentMethod', 'bPay');
  });

  it('LINE 237 - bPay debt cant post task', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: -100000 },
    });
    await device.reloadReactNative();
    await waitForElement(
      'Số tiền nợ bPay của bạn hiện tại là: 100,000 Rp, những tài khoản có phát sinh nợ sẽ bị giảm điểm uy tín và không được ưu tiên khi đặt lịch trên hệ thống. Vui lòng thanh toán số tiền trên để luôn có trải nghiệm tốt nhất với bTaskee. Xin cảm ơn.',
      500,
      'text',
    );
    await tapTextAtIndex('Thanh toán nợ ngay', 1);
    await tapHeaderBack();
    await tapHeaderBack();
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await waitForElement('Vui lòng thanh toán nợ. Sau khi thanh toán xong mới có thể đăng việc.', 1000, 'text');
    await tapText('Thanh toán');
  });
  it('LINE 260 - bPay debt cant quick post task', async () => {
    await initData('task/createTask', [
      {
        isoCode: ASKER_INDO.isoCode,
        serviceName: 'CLEANING',
        askerPhone: ASKER_INDO.phone,
        description: 'Don dep nha 01',
        status: 'DONE',
        rated: true,
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: -100000 },
    });
    await device.reloadReactNative();
    await waitForElement(
      'Số tiền nợ bPay của bạn hiện tại là: 100,000 Rp, những tài khoản có phát sinh nợ sẽ bị giảm điểm uy tín và không được ưu tiên khi đặt lịch trên hệ thống. Vui lòng thanh toán số tiền trên để luôn có trải nghiệm tốt nhất với bTaskee. Xin cảm ơn.',
      500,
      'text',
    );
    await tapTextAtIndex('Thanh toán nợ ngay', 1);
    await tapHeaderBack();
    await tapHeaderBack();
    await tapId('postTaskServiceCLEANING');
    await tapText('Đăng lại');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await waitForElement('Vui lòng thanh toán nợ. Sau khi thanh toán xong mới có thể đăng việc.', 1000, 'text');
    await tapText('Thanh toán');
  });
  it('LINE 290 - bPay debt cant quick post task history', async () => {
    await initData('task/createTask', [
      {
        isoCode: ASKER_INDO.isoCode,
        serviceName: 'CLEANING',
        askerPhone: ASKER_INDO.phone,
        description: 'Don dep nha 01',
        status: 'DONE',
        rated: true,
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: -100000 },
    });
    await device.reloadReactNative();
    await waitForElement(
      'Số tiền nợ bPay của bạn hiện tại là: 100,000 Rp, những tài khoản có phát sinh nợ sẽ bị giảm điểm uy tín và không được ưu tiên khi đặt lịch trên hệ thống. Vui lòng thanh toán số tiền trên để luôn có trải nghiệm tốt nhất với bTaskee. Xin cảm ơn.',
      500,
      'text',
    );
    await tapTextAtIndex('Thanh toán nợ ngay', 1);
    await tapHeaderBack();
    await tapHeaderBack();
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapText('Đăng lại');
    await tapText('Đăng việc');
    await waitForElement('Vui lòng thanh toán nợ. Sau khi thanh toán xong mới có thể đăng việc.', 1000, 'text');
    await tapText('Thanh toán');
  });

  it('LINE 322 - Asker choose address default when post task', async () => {
    await tapText('Tài khoản');
    await tapText('Xem hồ sơ');
    await tapText('Thêm địa chỉ mới');
    await typeToTextField('txtInputAddress', 'Central Park');
    await tapText(
      'Central Park Mall, Jl. Letjen S. Parman, RW.1, Tanjung Duren Selatan, Kota Jakarta Barat, Daerah Khusus Ibukota Jakarta, Indonesia',
    );
    await tapId('btnSelectLocation');
    await typeToTextField('txtDescriptionMap', 'My Task');
    await tapText('Đồng ý');
    await scrollTo('scrollAddLocation', 'bottom');
    await tapText('Đặt làm địa chỉ đăng việc mặc định');
    await tapText('Lưu');
    await tapText('Đóng');
    await tapHeaderBack();
    await tapText('Trang chủ');
    await tapId('postTaskServiceCLEANING');
    await waitForElement('Central Park Mall RT.12', 1000, 'text');
    await tapHeaderBack();
    await tapText('Tài khoản');
    await tapText('Xem hồ sơ');
    await tapText('Central Park Mall RT.12');
    await scrollTo('scrollUpdateLocation', 'bottom');
    await tapText('Đặt làm địa chỉ đăng việc mặc định');
    await tapId('updateLocation');
    await waitForElement('Đóng', 500, 'text');
    await tapText('Đóng');
    await tapHeaderBack();
    await tapText('Trang chủ');
    await tapId('postTaskServiceCLEANING');
    await waitForElement('Central Park Mall RT.12', 1000, 'text');
  });

  it('LINE 354 - CLEANING Prepayment GO_PAY', async () => {
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('GoPay');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Số tiền cần thanh toán qua GO_PAY là 160,000 IDR', 'text');
    await tapText('Hủy');
    await tapText('Thanh toán lại');
    await tapId('choosePaymentMethod');
    await tapText('GoPay');
    await tapText('Đăng việc');
    await expectElementVisible('Số tiền cần thanh toán qua GO_PAY là 160,000 IDR', 'text');
  });

  it('LINE 373 - CLEANING Prepayment DANA', async () => {
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('DANA');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Số tiền cần thanh toán qua DANA là 160,000 IDR', 'text');
    await tapText('Hủy');
    await tapText('Thanh toán lại');
    await tapId('choosePaymentMethod');
    await tapText('DANA');
    await tapText('Đăng việc');
    await expectElementVisible('Số tiền cần thanh toán qua DANA là 160,000 IDR', 'text');
  });

  it('LINE 392 - CLEANING Prepayment QRIS', async () => {
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('QRIS');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('160,000 IDR', 'text');
    await expectElementVisible('Lưu hình', 'text');
    await device.reloadReactNative();
    await tapText('Hoạt động');
    await tapText('Thanh toán lại');
    await tapId('choosePaymentMethod');
    await tapText('QRIS');
    await tapText('Đăng việc');
    await expectElementVisible('160,000 IDR', 'text');
    await expectElementVisible('Lưu hình', 'text');
  });
  it('LINE 412 - Post task with gender Male', async () => {
    await initData('service/updateService', {
      isoCode: ASKER_INDO.isoCode,
      serviceName: 'CLEANING',
      dataUpdate: {
        optional: {
          isAutoChooseTaskerEnabled: true,
          isGenderEnabled: true,
        },
      },
    });
    await device.reloadReactNative();
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await scroll('scrollStep2Cleaning', 500, 'down', 0.5, 0.5);
    await tapId('whatIsChooseGender');
    await expectElementVisibleAtIndex('Chọn giới tính Tasker là gì?', 0, 'text');
    await tapText('Đã hiểu');
    await tapId('ChooseGender');
    await tapText('Nam');
    await tapText('Đồng ý');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await expectElementVisibleAtIndex('Chọn giới tính Tasker', 0, 'text');
    await expectElementVisible('Nam', 'text');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('taskDuration0');
    await expectIdToHaveText('valueGender', 'Nam');
  });

  it('LINE 443 - Post task with gender Female', async () => {
    await initData('service/updateService', {
      isoCode: ASKER_INDO.isoCode,
      serviceName: 'CLEANING',
      dataUpdate: {
        optional: {
          isAutoChooseTaskerEnabled: true,
          isGenderEnabled: true,
        },
      },
    });
    await device.reloadReactNative();
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await scroll('scrollStep2Cleaning', 500, 'down', 0.5, 0.5);
    await tapId('whatIsChooseGender');
    await expectElementVisibleAtIndex('Chọn giới tính Tasker là gì?', 0, 'text');
    await tapText('Đã hiểu');
    await tapId('ChooseGender');
    await tapText('Nữ');
    await tapText('Đồng ý');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await expectElementVisibleAtIndex('Chọn giới tính Tasker', 0, 'text');
    await expectElementVisible('Nữ', 'text');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('taskDuration0');
    await expectIdToHaveText('valueGender', 'Nữ');
  });
});
