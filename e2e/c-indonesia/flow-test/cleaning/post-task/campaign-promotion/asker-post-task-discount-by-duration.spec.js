/**
 * @description Post task - check cost by duration (1 test case)
 *   case 1: LINE 22 - Discount by duration
 * */

const { tapId, tapText, initData, postTask, expectIdToHaveText } = require('../../../../../step-definition');

const { E2EHelpers } = require('../../../../../e2e.helpers');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/campaign-promotion/asker-post-task-discount-by-duration.spec.js - Discount by duration', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      { isoCode: 'ID', phone: '**********', name: 'Asker', type: 'ASKER', status: 'ACTIVE', FMainAccount: 1000000 },
    ]);
    await initData('service/updateDiscountDurationService', { serviceName: 'CLEANING', isoCode: 'ID' });

    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
  });

  it('LINE 21 - Discount by duration', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '216,000 IDR/3h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '272,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  });
});
