/**
 * @description Asker post task with promotion code (10 test cases)
 *   case 1: <PERSON><PERSON><PERSON> 37 - <PERSON><PERSON> post task with promotion code money "def123
 *   case 2: <PERSON><PERSON><PERSON> 60 - <PERSON><PERSON> post task with promotion code percentage "opq123
 *   case 3: <PERSON><PERSON><PERSON> 84 - <PERSON><PERSON> post task with promotion code percentage "OPQ123
 *   case 4: <PERSON><PERSON><PERSON> 108 - <PERSON><PERSON> post task with promotion code percentage "OpQ123
 *   case 5: <PERSON><PERSON><PERSON> 132 - <PERSON><PERSON> post task with invalid promotion code
 *   case 6: <PERSON><PERSON><PERSON> 158 - <PERSON><PERSON> post task with promotion code percentage "opq789" and max value is 30K
 *   case 7: LINE 183 - Ask<PERSON> post task with invalid promotion code and remove promotion field
 *   case 8: LINE 210 - <PERSON><PERSON> post task with refferal code
 *   case 9: LINE 227 - <PERSON><PERSON> post task with promotion, payment with bPay, check enough money
 *   case 10: LINE 261 - Ask<PERSON> post task with promotion, payment with bPay, check not enough money
 * */

const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  waitForElement,
  postTask,
  clearTextInput,
  waitForLoading,
  expectElementVisible,
  typePromotionCode,
  tapHeaderBack,
} = require('../../../../../step-definition');

const { E2EHelpers } = require('../../../../../e2e.helpers');
const { ASKER_INDO } = require('../../../../../helpers/constants');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/campaign-promotion/asker-post-task-with-promotion-code.spec.js - Asker post task with promotion code', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      ASKER_INDO,
      { isoCode: ASKER_INDO.isoCode, phone: '0834567892', name: 'Asker 02', type: 'ASKER', status: 'ACTIVE' },
    ]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: ASKER_INDO.isoCode,
        code: 'abc123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'CURRENT',
        typeOfValue: 'MONEY',
        limit: 100,
      },
      {
        isoCode: ASKER_INDO.isoCode,
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
      {
        isoCode: ASKER_INDO.isoCode,
        code: 'ghk123',
        value: 50000,
        target: 'TASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
      {
        isoCode: ASKER_INDO.isoCode,
        code: 'lmn123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 1,
      },
      {
        isoCode: ASKER_INDO.isoCode,
        code: 'opq123',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
      },
      {
        isoCode: ASKER_INDO.isoCode,
        code: 'opq789',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'PERCENTAGE',
        maxValue: 30000,
      },
    ]);
    await initData('promotion/usersAppliedPromotion', {
      isoCode: ASKER_INDO.isoCode,
      phone: '0834567892',
      promotionCode: 'lmn123',
    });

    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
  });

  it('LINE 100 - Asker post task with promotion code money "def123"', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('def123');
    await expectIdToHaveText('originPrice', '160,000 IDR');
    await expectIdToHaveText('price', '110,000 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '160,000 Rp');
    await expectIdToHaveText('discount', '-50,000 Rp');
    await expectIdToHaveText('finalCost', '110,000 Rp');
  });

  it('LINE 123 - Asker post task with promotion code percentage "opq123"', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('opq123');
    await expectIdToHaveText('originPrice', '240,000 IDR');
    await expectIdToHaveText('price', '144,000 IDR');

    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '240,000 Rp');
    await expectIdToHaveText('discount', '-96,000 Rp');
    await expectIdToHaveText('finalCost', '144,000 Rp');
  });

  it('LINE 147 - Asker post task with promotion code percentage "OPQ123"', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('OPQ123');
    await expectIdToHaveText('originPrice', '240,000 IDR');
    await expectIdToHaveText('price', '144,000 IDR');

    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '240,000 Rp');
    await expectIdToHaveText('discount', '-96,000 Rp');
    await expectIdToHaveText('finalCost', '144,000 Rp');
  });

  it('LINE 171 - Asker post task with promotion code percentage "OpQ123"', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('OpQ123');
    await expectIdToHaveText('originPrice', '240,000 IDR');
    await expectIdToHaveText('price', '144,000 IDR');

    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '240,000 Rp');
    await expectIdToHaveText('discount', '-96,000 Rp');
    await expectIdToHaveText('finalCost', '144,000 Rp');
  });

  it('LINE 195 - Asker post task with invalid promotion code', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await waitForElement('Mã ưu đãi này không áp dụng cho tài khoản của bạn.', 500, 'text');
    await tapText('Đóng');
    await clearTextInput('textInputPromotion');
    await typePromotionCode('ghk123');
    await waitForLoading(500);
    await waitForElement('Mã ưu đãi chỉ áp dụng cho Tasker.', 500, 'text');
    await tapText('Đóng');
    await clearTextInput('textInputPromotion');
    await typePromotionCode('lmn123');
    await waitForLoading(500);
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 219 - Asker post task with promotion code percentage "opq789" and max value is 30K', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('opq789');

    await expectIdToHaveText('originPrice', '240,000 IDR');
    await expectIdToHaveText('price', '210,000 IDR');

    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '240,000 Rp');
    await expectIdToHaveText('discount', '-30,000 Rp');
    await expectIdToHaveText('finalCost', '210,000 Rp');
  });

  it('LINE 244 - Asker post task with refferal code', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    const data = await initData('user/getUserByPhone', { phone: ASKER_INDO.phone, countryCode: '+62' });
    await tapId('promotionCode');
    await typePromotionCode(data.referralCode);
    await waitForLoading(500);
    await waitForElement('Mã ưu đãi không hợp lệ. Vui lòng kiểm tra lại.', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 259 - Asker post task with promotion, payment with bPay, check enough money', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: 190000 },
    });
    await tapText('Tài khoản');
    await tapText('bPay');
    await expectElementVisible('190,000', 'text');
    await tapHeaderBack();

    await tapText('Trang chủ');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('def123');

    await expectIdToHaveText('originPrice', '240,000 IDR');
    await expectIdToHaveText('price', '190,000 IDR');

    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', '240,000 Rp');
    await expectIdToHaveText('discount', '-50,000 Rp');
    await expectIdToHaveText('finalCost', '190,000 Rp');
  });

  it('LINE 297 - Asker post task with promotion, payment with bPay, check not enough money', async () => {
    await initData('update-user/financialAccount', {
      phone: ASKER_INDO.phone,
      isoCode: ASKER_INDO.isoCode,
      financialAccountData: { ID_FMainAccount: 150000 },
    });
    await tapText('Tài khoản');
    await tapText('bPay');
    await expectElementVisible('150,000', 'text');
    await tapHeaderBack();

    await tapText('Trang chủ');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '240,000 IDR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('def123');

    await expectIdToHaveText('originPrice', '240,000 IDR');
    await expectIdToHaveText('price', '190,000 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible(
      'Bạn cần thêm 40,000 IDR để có thể đăng công việc này. Vui lòng nạp thêm hoặc chọn hình thức thanh toán khác.',
      'text',
    );
    await tapText('Đóng');
  });
});
