/**
 * @description Asker post task with marketing campaign (1 test case)
 *   case 1: LINE 53 - <PERSON><PERSON> post task with promotion code money "def123"
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  expectElementVisible,
} = require('../../../../../step-definition');

const { E2EHelpers } = require('../../../../../e2e.helpers');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 1000000,
};

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/campaign-promotion/asker-post-task-with-marketing-campaign.spec.js - Asker post task with marketing campaign', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'ID',
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: 30000,
      },
    ]);
    await initData('campaign/createMarketingCampaign', {
      isoCode: 'ID',
      status: 'ACTIVE',
      code: 'def123',
      action: 'app_direction',
      name: '001',
      serviceName: 'CLEANING',
      type: 'PROMOTION',
      primaryNavigate: 'PostTaskStep1',
      secondaryNavigate: 'Home',
    });

    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
  });

  it('LINE 43 - Asker post task with promotion code money "def123"', async () => {
    await tapId('marketingCampaign_PROMOTION');
    await tapText('Đăng việc ngay');
    await tapId('chooseDuration-2');
    await expectElementVisible('lbOriginPrice');
    await expectElementVisible('lbPrice');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount');
    await expectElementVisible('finalCost');
  });
});
