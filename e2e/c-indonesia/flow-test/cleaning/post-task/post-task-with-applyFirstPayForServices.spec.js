/**
 * @description Post task with pet (3 test cases)
 *   case 1: <PERSON>IN<PERSON> 35 - Post task with cat
 *   case 2: <PERSON><PERSON><PERSON> 56 - Post task with dog
 *   case 3: <PERSON><PERSON><PERSON> 77 - Post task with cat and dog
 * */

const { initData, tapId, tapText, postTask, swipe, expectElementNotVisible } = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/post-task-with-applyFirstPayForServices.spec.js - Post task with applyFirstPayForServices', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        isoCode: 'ID',
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        email: '<EMAIL>',
        FMainAccount: 1000000,
      },
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);
  });

  it('LINE 29 - Post task with applyFirstPayForServices null', async () => {
    await initData('settingCountry/update', [
      {
        condition: { 'paymentMethods.bookTask.name': 'CASH' },
        field: 'paymentMethods.bookTask.$.applyFirstPayForServices',
        value: ['DEEP_CLEANING'],
        isoCode: 'ID',
      },
    ]);
    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await expectElementNotVisible('Đăng việc', 'text');
  });

  it('LINE 38 - Post task with applyFirstPayForServices', async () => {
    await initData('settingCountry/update', [
      {
        condition: { 'paymentMethods.bookTask.name': 'CASH' },
        field: 'paymentMethods.bookTask.$.applyFirstPayForServices',
        value: ['CLEANING', 'DEEP_CLEANING'],
        isoCode: 'ID',
      },
    ]);

    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
  });

  it('LINE 71 - Post task with applyFirstPayForServices and has taskDone', async () => {
    await initData('user/updateUser', [
      {
        phone: '**********',
        isoCode: 'ID',
        dataUpdate: {
          taskDone: 10,
        },
      },
    ]);
    await initData('settingCountry/update', [
      {
        condition: { 'paymentMethods.bookTask.name': 'CASH' },
        field: 'paymentMethods.bookTask.$.applyFirstPayForServices',
        value: ['CLEANING', 'DEEP_CLEANING'],
        isoCode: 'ID',
      },
    ]);

    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
  });
});
