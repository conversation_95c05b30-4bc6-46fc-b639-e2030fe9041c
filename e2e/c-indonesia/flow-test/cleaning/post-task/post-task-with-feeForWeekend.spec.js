/**
 * @description Post task with feeForWeekend (3 test cases)
 *   case 1: LINE 35 - Post task with duration is 2h weekend
 *   case 2: LINE 56 - Post task with duration is 3h
 *   case 3: LINE 77 - Post task with duration is 2h not weekend
 * */

const {
  initData,
  tapId,
  tapText,
  postTask,
  scroll,
  expectIdToHaveText,
  expectElementVisible,
  isWeekend,
  swipe,
} = require('../../../../step-definition');
const moment = require('moment');

const { E2EHelpers } = require('../../../../e2e.helpers');

//Không run vào t7 và cn
if (isWeekend()) {
  return;
}

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/post-task-with-feeForWeekend.spec.js - Post task with feeForWeekend', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('service/update-feeWeekendApplyForCity', {
      isoCode: 'ID',
      serviceName: 'CLEANING',
      city: ['Jakarta'],
    });
    await initData('user/createUser', [
      {
        isoCode: 'ID',
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        email: '<EMAIL>',
        FMainAccount: 1000000,
      },
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);

    await E2EHelpers.onHaveLogin('**********');
  });

  it('LINE 50 - Post task with duration is 2h weekend', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    try {
      await tapText('T7');
    } catch (error) {
      await tapText('CN');
    }
    await expectElementVisible('Giá tăng do nhu cầu công việc tăng cao vào thời điểm này.', 'text');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '192,000 Rp');
  });

  it('LINE 70 - Post task with duration is 3h', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-3');
    await tapId('btnNextStep2');
    try {
      await tapText('T7');
    } catch (error) {
      await tapText('CN');
    }
    await expectElementVisible('Giá tăng do nhu cầu công việc tăng cao vào thời điểm này.', 'text');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '288,000 Rp');
  });

  it('LINE 90 - Post task with duration is 2h not weekend', async () => {
    const price = moment().day() === 6 || moment().day() === 7 ? '192,000' : '160,000';
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', `${price} IDR/2h`);
    await scroll('scrollStep2Cleaning', 300, 'down', 0.5, 0.5);
    await tapId('whatIsChoosePet');
    await expectElementVisible('txtTitleAlert_Nhà có vật nuôi');
    await tapText('Đã hiểu');
    await tapId('ChoosePet');
    await tapId('Pet0');
    await tapId('Pet1');
    await tapText('Đồng ý');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', `${price} Rp`);
    await expectIdToHaveText('valuePets', 'Chó, Mèo');
  });
});
