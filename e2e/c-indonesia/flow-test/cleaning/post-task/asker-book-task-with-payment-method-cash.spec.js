/**
 * @description Asker post task with promotion code (10 test cases)
 *   case 1: LINE 31 - Not login can not choose payment method Cash
 *   case 2: LINE 44 - Asker have not any task done can not choose payment method Cash
 *   case 3: LINE 57 - Asker have task done can choose payment method Cash
 * */

const {
  initData,
  tapId,
  tapText,
  swipe,
  postTask,
  expectElementVisible,
  expectElementNotExist,
  expectElementNotVisible,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { ASKER_INDO } = require('../../../../helpers/constants');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/asker-book-task-with-payment-method-cash.spec.js - Asker post task with payment method cash', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      ASKER_INDO,
      { isoCode: ASKER_INDO.isoCode, phone: '0834567892', name: '<PERSON><PERSON> 02', type: 'ASKER', status: 'ACTIVE' },
    ]);
    await initData('settingCountry/update', [
      {
        condition: { 'paymentMethods.bookTask.name': 'CASH' },
        field: 'paymentMethods.bookTask.$.applyFirstPayForServices',
        value: ['DEEP_CLEANING'],
        isoCode: 'ID',
      },
    ]);
  });

  it('LINE 31 - Not login can not choose payment method Cash', async () => {
    await E2EHelpers.onHaveLogout();
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await expectElementVisible('paymentMethodCash');
    await expectElementVisible('txtDisabledCash');
    await tapId('paymentMethodCash');
    await expectElementNotVisible('choosePaymentMethod');
  });

  it('LINE 44 - Asker have not any task done can not choose payment method Cash', async () => {
    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await expectElementVisible('paymentMethodCash');
    await expectElementVisible('txtDisabledCash');
    await tapId('paymentMethodCash');
    await expectElementNotVisible('choosePaymentMethod');
  });

  it('LINE 57 - Asker have task done can choose payment method Cash', async () => {
    await initData('user/updateUser', [
      {
        phone: ASKER_INDO.phone,
        isoCode: ASKER_INDO.isoCode,
        dataUpdate: {
          taskDone: 100,
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await expectElementVisible('paymentMethodCash');
    await expectElementNotExist('txtDisabledCash');
    await tapId('paymentMethodCash');
    await expectElementVisible('Xác nhận và thanh toán', 'text');
    await expectElementVisible('Tiền mặt', 'text');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('Tiền mặt', 'text');
  });

  it('LINE 85 - Choose payment method Virtual Account', async () => {
    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');

    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await expectElementVisible('Chuyển khoản định danh', 'text');
    await expectElementVisible('BNI Virtual Account', 'text');
  });
});
