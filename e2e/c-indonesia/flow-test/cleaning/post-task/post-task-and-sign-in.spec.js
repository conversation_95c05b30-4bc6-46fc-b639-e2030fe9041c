const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  loginWithModal,
  signUpWithModal,
  forgotPasswordWithModal,
  swipe,
} = require('../../../../step-definition');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/post-task-and-sign-in.spec.js - New User post cleaning task before sign in', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        randomId: true,
        isoCode: 'ID',
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        email: '<EMAIL>',
        FMainAccount: 1000000,
      },
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);

    await E2EHelpers.byPassUnauthorize();
    try {
      await tapId('cancelVerify');
    } catch (error) {}
  });

  const checkDataAfterPosttask = async () => {
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '320,000 Rp');
    await expectIdToHaveText('valueOptionals', 'Nấu ăn');
  };

  it('LINE 45 - New customer post cleaning task and sign in', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '320,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');

    await loginWithModal('**********', '123456', '+62');
    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });

  it('LINE 58 - New customer post cleaning task and sign up', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '320,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await signUpWithModal('Bill Gate', '**********', '<EMAIL>', '+62');
    await initData('update-user/financialAccount', {
      phone: '**********',
      isoCode: 'ID',
      financialAccountData: { ID_FMainAccount: 1000000 },
    });
    await tapText('Đóng');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });

  it('LINE 71 - New customer post cleaning task and for got password', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '320,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');

    await forgotPasswordWithModal('**********', '+62');

    await tapText('Theo dõi công việc');
    await checkDataAfterPosttask();
  });
});
