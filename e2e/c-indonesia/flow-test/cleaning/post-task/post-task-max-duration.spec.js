/**
 * @description Post task max duration is 4 hours
 *   case 1: LINE 51 - Ask<PERSON> can not post a task over 4 hours
 * */

const { initData, tapId, tapText, expectIdToHaveText, expectElementVisible } = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/post-task/post-task-max-duration.spec.js - Post task max duration is 4 hours', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        isoCode: 'ID',
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        email: '<EMAIL>',
        FMainAccount: 1000000,
        oldUser: true,
      },
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);

    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
  });

  it('LINE 29 - Asker can not post a task over 4 hours', async () => {
    await tapId('postTaskServiceCLEANING');
    await tapId('chooseDuration-4');
    await tapId('chooseServies-0');
    await expectElementVisible(
      'Thời gian làm tối đa là 4h. Bạn vui lòng giảm thời gian làm việc để có thể chọn thêm dịch vụ',
      'text',
    );
    await tapText('Đóng');
    await tapId('chooseDuration-3');
    await tapId('chooseServies-0');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  });
});
