const {
  initData,
  tapId,
  tapText,
  postTask,
  expectElementVisible,
  expectIdToHaveText,
  tapHeaderBack,
  tapTask,
  swipe,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'ID',
  phone: '0834567890',
  name: '<PERSON><PERSON>',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASKER = {
  _id: 'userId_0834567891',
  isoCode: 'ID',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
  workingPlaces: [
    {
      country: 'ID',
      city: 'Bali',
    },
  ],
};

const BALI = {
  _id: 'x20c7f34f231ee1e2061f2c5626ccf1ef',
  lat: -8.4095178,
  lng: 115.188916,
  country: 'ID',
  city: 'Bali',
  district: 'Denpasar',
  address: 'Bali, Indonesia',
  contact: '<PERSON><PERSON>',
  phoneNumber: '0834567890',
  shortAddress: 'Bali Indonesia',
  countryCode: '+62',
  isoCode: 'ID',
  homeType: 'HOME',
  description: 'Task Description',
  isAddressMaybeWrong: true,
};

describe('FILE: e2e/c-indonesia/flow-test/cleaning/timezone/check-timezone.spec.js - Check timezone cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('user/updateUser', [
      {
        phone: ASKER.phone,
        isoCode: ASKER.isoCode,
        dataUpdate: {
          locations: [BALI],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 50 - Check flow post task', async () => {
    const currentHour = moment().get('hours');
    // Không chạy trong không giờ này vì khung giờ này ko update đc giữ tasker
    if (currentHour <= 5 || currentHour >= 23) return;
    const durationGmt = '2 giờ, 14:00 đến 16:00 (GMT+8)';
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('weekdays_1');
    await tapId('btnNextStep3');

    // Check post task step 4
    await expectIdToHaveText('duration', durationGmt);
    await tapId('btnSubmitPostTask');

    // Check in task item
    await device.reloadReactNative();
    await tapId('Tab_Activity');
    await expectIdToHaveText('taskDuration0', durationGmt);

    // Check in re-payment
    await tapId('btnRepay');
    await expectIdToHaveText('valueDuration', durationGmt);

    // Check in task detail
    await initData('task/updateTask', [
      {
        description: BALI.description,
        isoCode: BALI.isoCode,
        dataUpdate: {
          isPrepayTask: false,
          status: 'CONFIRMED',
          acceptedTasker: [
            {
              taskerId: TASKER._id,
              name: TASKER.name,
            },
          ],
        },
      },
    ]);
    await tapHeaderBack();
    await tapTask(BALI.description);
    await expectIdToHaveText('valueDuration', durationGmt);

    // Tạo chat ID để update task khi có tasker
    await tapId('btnChat');
    await tapHeaderBack();

    // Check update task in Chat
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapText('Giữ Tasker hiện tại');
    await tapId('weekdays_2');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await expectElementVisible('oldDateGMT');
    await expectElementVisible('newDateGMT');

    // Check in history
    await initData('task/updateTask', [
      {
        description: BALI.description,
        isoCode: BALI.isoCode,
        dataUpdate: {
          status: 'DONE',
        },
      },
    ]);
    await device.reloadReactNative();
    await tapHeaderBack();
    await tapId('Tab_Activity');
    await tapId('btnTaskHistory');
    await tapTask(BALI.description);
    await swipe('scrollHistoryDetail', 'up');
    await expectIdToHaveText('valueDuration', durationGmt);

    // Check quick post task
    await device.reloadReactNative();
    await tapId('postTaskServiceCLEANING');
    await expectIdToHaveText('durationQuickPostTask', durationGmt);
    await tapId('rePostTaskBtn');
    await expectIdToHaveText('duration', durationGmt);
  });

  it('LINE 146 - Check flow post task schedule', async () => {
    const durationGmt = '2 giờ, 14:00 đến 16:00 (GMT+8)';
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('weekdays_1');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('btnNextStep3');

    // Check post task step 4
    await expectIdToHaveText('duration', durationGmt);
    await tapId('btnSubmitPostTask');

    // Check in task item
    await device.reloadReactNative();
    await tapId('Tab_Activity');
    await expectIdToHaveText('taskDuration0', durationGmt);

    // Check in task item schedule
    await tapId('Tab_Schedule');
    await expectIdToHaveText('scheduleDuration0', durationGmt);

    // Check in task item schedule sau khi cập nhật
    await tapId('task_0');
    await tapId('DayOfWeek2');
    await tapId('btnUpdateSchedule');
    await expectIdToHaveText('scheduleDuration0', durationGmt);
  });
});
