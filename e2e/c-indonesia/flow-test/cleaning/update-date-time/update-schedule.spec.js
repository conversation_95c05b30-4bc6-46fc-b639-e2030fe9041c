const {
  initData,
  tapId,
  tapText,
  waitForElement,
  postTask,
  expectElementNotExist,
  expectElementVisible,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { ASKER_INDO } = require('../../../../helpers/constants');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/update-date-time/update-schedule.spec.js - Update schedule cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      ASKER_INDO,
      { isoCode: 'ID', phone: '0834567891', name: '<PERSON>er 02', type: 'ASKER', status: 'ACTIVE', taskDone: 1 },
    ]);

    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
  });

  it('LINE 23 - Can not Update schedule when data not change', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('Tab_Schedule');

    await tapId('task_0');

    await tapId('btnUpdateSchedule');
    await expectElementVisible('btnUpdateSchedule');
    await expectElementNotExist('task_0', 'text');
  });

  it('LINE 43 - Update duration task schedule', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('Tab_Schedule');

    await tapId('task_0');

    // Change time picker
    await tapText('3 giờ');

    await tapId('btnUpdateSchedule');
    await waitForElement('3 giờ, 14:00 đến 17:00', 500, 'text');
  });

  it('LINE 68 - Update weekly task schedule', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await tapId('DayOfWeek3');
    await tapId('DayOfWeek4');
    await tapId('DayOfWeek5');
    await tapId('DayOfWeek6');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('Tab_Schedule');

    await tapId('task_0');

    // Change time picker
    await tapText('CN');

    await tapId('btnUpdateSchedule');
    await waitForElement('Thứ hai - Thứ ba - Thứ tư - Thứ năm - Thứ sáu - Thứ bảy', 500, 'text');
  });
});
