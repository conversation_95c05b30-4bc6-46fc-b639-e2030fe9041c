/**
 * @description Asker cancel task limit
 *   case 1: LINE 21 - <PERSON><PERSON> cancel task with task createdAt + 10 minutes is less than or equal current date and total task canceled is less than limit
 *   case 2: LINE 42 - Ask<PERSON> cancel many tasks within 24h
 *   case 3: LINE 112 - Ask<PERSON> cancel task with task createdAt + 10 minutes is greater than current date
 * */

const {
  initData,
  tapId,
  tapText,
  expectElementNotExist,
  waitForElement,
  waitForLoading,
  swipe,
  postTask,
  ADDRESS_KEY,
} = require('../../../../step-definition');
const moment = require('moment');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/cancel-task/asker-cancel-task-limit.spec.js - Asker cancel task limit', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      { isoCode: 'ID', phone: '0834567890', name: 'Asker', type: 'ASKER', status: 'ACTIVE' },
    ]);
    await initData('settingSystem/update-fields', {
      isoCode: 'ID',
      dataUpdate: {
        limitNumberCancelTask: 6,
      },
    });

    await E2EHelpers.onHaveLogin('0834567890', '123456', '+62');
  });

  it('LINE 41 - Asker cancel task with task createdAt + 10 minutes is less than or equal current date and total task canceled is less than limit', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
        status: 'POSTED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 02',
        status: 'CANCELED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 03',
        status: 'CANCELED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 04',
        status: 'CANCELED',
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 1000, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDuration0');
  });

  // TODO: Open when update API
  it.skip('LINE 87 - Asker cancel many tasks within 24h', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
        status: 'CANCELED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 02',
        status: 'CANCELED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 03',
        status: 'CANCELED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 04',
        status: 'POSTED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 05',
        status: 'POSTED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 06',
        status: 'POSTED',
      },
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 07',
        status: 'POSTED',
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 07');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 1000, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await expectElementNotExist('TAB_UPCOMINGDon dep nha 07');
    await tapId('TAB_UPCOMINGDon dep nha 06');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await expectElementNotExist('TAB_UPCOMINGDon dep nha 06');
    await tapId('TAB_UPCOMINGDon dep nha 05');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await expectElementNotExist('TAB_UPCOMINGDon dep nha 05');
    await tapId('TAB_UPCOMINGDon dep nha 04');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await expectElementNotExist('TAB_UPCOMINGDon dep nha 04');
    await tapText('Trang chủ');
    await tapId('postTaskServiceCLEANING');
    await waitForElement('Bạn đã hủy quá nhiều công việc, bạn không thể đăng việc trong vòng 24h.', 1000, 'text');
    await tapText('Đóng');
    // SyncedCron will reactive users
    await initData('user/updateUser', [
      {
        isoCode: 'ID',
        phone: '0834567890',
        dataUpdate: {
          status: 'ACTIVE',
        },
      },
    ]);

    await initData('update-user/unset-fields', [
      {
        isoCode: 'ID',
        phone: '0834567890',
        fields: ['cancelBlockedAt', 'numberOfCanceledTasks'],
      },
    ]);
    await waitForLoading(1000);
    await initData('notification/send', [
      { phone: '0834567890', isoCode: 'ID', description: '', type: 25, text: 'Bây giờ bạn có thể đăng việc' },
    ]);

    await device.reloadReactNative();
    await tapText('Tin nhắn');
    await waitForElement('tabNotification', 1000);
    await tapId('tabNotification');
    await waitForElement('Bây giờ bạn có thể đăng việc', 1000, 'text');
    await tapText('Trang chủ');
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.ID);
  });

  it('LINE 221 - Asker cancel task with task createdAt + 10 minutes is greater than current date', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'ID',
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
        status: 'POSTED',
      },
    ]);
    await initData('task/updateTask', {
      description: 'Don dep nha 01',
      isoCode: 'ID',
      dataUpdate: { createdAt: moment().subtract(11, 'minute').toDate() },
    });
    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('TAB_UPCOMINGDon dep nha 05');
  });
});
