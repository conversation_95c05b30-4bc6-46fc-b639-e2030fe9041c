/**
 * @description Asker accept task cleaning
 *   case 1:  Asker accept-task cleaning - UI Item task
 *   case 2:  Asker accept-task cleaning - UI task detail
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  postTask,
  callService,
  expectElementNotExist,
  swipe,
} = require('../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');
const { ASKER_INDO } = require('../../../helpers/constants');

describe('FILE: e2e/c-indonesia/flow-test/cleaning/tasker-accept-task.spec.js - Asker accept task cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      ASKER_INDO,
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);

    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
  });

  it('LINE 33 - Asker accept-task cleaning - UI Item task', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '320,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await initData('service/updateServiceChannel', [
      { isoCode: 'ID', serviceName: 'CLEANING', taskerPhone: '**********' },
    ]);
    await initData('update-user/financialAccount', {
      phone: '**********',
      isoCode: 'ID',
      financialAccountData: { ID_Promotion: 100000 },
    });
    const task = await initData('task/getTaskByDescription', { description: 'My Task', isoCode: 'ID' });
    const tasker = await initData('user/getUserByPhone', { phone: '**********', countryCode: '+62' });
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'ID',
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-indo/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await device.reloadReactNative();
    await tapText('Hoạt động');
    await waitForElement('taskerName0', 1000);
    await expectIdToHaveText('taskerName0', 'Tasker');
    await tapId('taskerName0');
    await tapId('seeMore');
    await expectElementNotExist('Chọn Tasker này', 'text');
  });

  it('LINE 80 -  Asker accept-task cleaning - UI task detail', async () => {
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 IDR/2h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '320,000 IDR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '320,000 Rp');
    await initData('service/updateServiceChannel', [
      { isoCode: 'ID', serviceName: 'CLEANING', taskerPhone: '**********' },
    ]);
    await initData('update-user/financialAccount', {
      phone: '**********',
      isoCode: 'ID',
      financialAccountData: { ID_Promotion: 100000 },
    });
    const task = await initData('task/getTaskByDescription', { description: 'My Task', isoCode: 'ID' });
    const tasker = await initData('user/getUserByPhone', { phone: '**********', countryCode: '+62' });
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'ID',
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-indo/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await device.reloadReactNative();
    await tapText('Hoạt động');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapId('taskerName0');
    await waitForElement('seeMore', 1000);
    await tapId('seeMore');
    await expectElementNotExist('Chọn Tasker này', 'text');
  });
});
