/**
 * @description Asker cancel Sofa task
 *  case 1: Asker cancel sofa task, fee = 0
 *  case 2: Asker cancel sofa task include cancel fee
 * <AUTHOR>
 */

const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  waitForElement,
  swipe,
} = require('../../../step-definition');
const moment = require('moment');

const { E2EHelpers } = require('../../../e2e.helpers');

const ASKER = {
  isoCode: 'ID',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASKER = {
  isoCode: 'ID',
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};

const TASK = {
  isoCode: 'ID',
  serviceName: 'SofaCleaning',
  askerPhone: ASKER.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/c-indonesia/flow-test/sofa/cancel-task.spec.js - Asker cancel Sofa task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER.phone);
  });

  it('LINE 55 - Asker cancel sofa task, fee = 0', async () => {
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await swipe('updatePage', 'up');
    await expectElementVisible('cancelTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    await initData('resetData');
  });

  it('LINE 93 - Asker cancel sofa task include cancel fee', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: TASK.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(5, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');
    await expectElementVisible('cancelTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    // choose reason cancel task, chon ly do thi moi dong y duoc
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    await initData('resetData');
  });
});
