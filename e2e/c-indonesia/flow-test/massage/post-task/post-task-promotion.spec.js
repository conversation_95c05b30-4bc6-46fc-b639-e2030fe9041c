/**
 * @description See marketing campaign for Massage
 *   case 1: See campaign marketing and post task Massage
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  expectIdToHaveText,
  tapText,
  waitForElement,
  swipe,
  expectElementVisible,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  FMainAccount: 2000000,
  oldUser: true,
};

const PROMOTION = {
  isoCode: 'ID',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'BOTH',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: 30000,
};

const CAMPAIGN = {
  isoCode: 'ID',
  status: 'ACTIVE',
  code: PROMOTION.code,
  serviceName: 'MASSAGE',
  type: 'PROMOTION',
  primaryNavigate: 'IntroDisinfectionService',
  secondaryNavigate: 'Home',
  isSpecialCampaign: true,
};

describe('FILE: e2e/c-indonesia/flow-test/massage/post-task/post-task-promotion.spec.js - See marketing campaign for Massage', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('promotion/create-promotion-code', [PROMOTION]);
  });

  it('LINE 46 - See campaign marketing and post task Massage', async () => {
    await initData('campaign/createMarketingCampaign', CAMPAIGN);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');

    await tapText('Đăng việc ngay');
    await tapId('address1');
    await tapId('btnSingle');
    // POST TASK STEP 2
    await expectElementVisible('itemPackage_fullBody');
    await tapId('itemPackage_fullBody');
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await expectElementVisible('btn_random');
    await tapId('btn_male');
    await tapId('option_package1');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await expectElementVisible('txtTimeWorking');
    await expectElementVisible('massagePackage_1');
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('originPrice', '120,000 IDR');
    await expectIdToHaveText('price', '70,000 IDR');
    await tapId('ckConfirmed');
    await tapId('btnSubmitPostTask');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await waitForElement('taskMy Task', 500);
  });
});
