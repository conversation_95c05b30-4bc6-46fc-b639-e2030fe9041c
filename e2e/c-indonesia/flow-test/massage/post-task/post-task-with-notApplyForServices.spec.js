/**
 * @description Post task with pet (3 test cases)
 *   case 1: LINE 35 - Post task with cat
 *   case 2: LIN<PERSON> 56 - Post task with dog
 *   case 3: LINE 77 - Post task with cat and dog
 * */

const { initData, tapId, tapText, postTask, swipe, expectElementNotVisible } = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');

const {
  SERVICE_NAME: { MASSAGE, DEEP_CLEANING },
  ISO_CODE: { ID },
} = require('../../../../helpers/constants');

describe('FILE: e2e/c-indonesia/flow-test/massage/post-task/post-task-with-notApplyForServices.spec.js - Post task with notApplyForServices', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        isoCode: ID,
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        email: '<EMAIL>',
        FMainAccount: 1000000,
      },
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);
  });

  it('LINE 29 - Post task with notApplyForServices null', async () => {
    await initData('settingCountry/update', [
      {
        condition: { 'paymentMethods.bookTask.name': 'CASH' },
        field: 'paymentMethods.bookTask.$.notApplyForServices',
        value: [DEEP_CLEANING],
        isoCode: 'ID',
      },
    ]);
    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
  });

  it('LINE 38 - Post task with notApplyForServices', async () => {
    await initData('settingCountry/update', [
      {
        condition: { 'paymentMethods.bookTask.name': 'CASH' },
        field: 'paymentMethods.bookTask.$.notApplyForServices',
        value: [MASSAGE],
        isoCode: ID,
      },
    ]);

    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
  });
});
