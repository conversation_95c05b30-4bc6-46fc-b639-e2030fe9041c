/**
 * @description New User post task massage
 *   case 1: New User post massage task with single option
 *   case 2: New User post massage task with couple option
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  expectElementVisible,
  reloadApp,
  postTask,
  expectElementNotExist,
  tapHeaderBack,
  scroll,
  waitForElement,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  FMainAccount: 2000000,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

describe('FILE: e2e/c-indonesia/flow-test/massage/post-task/post-task-new-user.spec.js - New User post task', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 43 - New User post massage task with single option', async () => {
    await postTask('postTaskServiceMASSAGE', 'Jakarta Barat', 'My Task');
    try {
      await tapId('btnSingle');
    } catch (error) {}
    // POST TASK STEP 2
    await expectElementVisible('txtNumberOfCustomer');
    await expectElementVisible('itemPackage_fullBody');
    await expectElementVisible('itemPackage_fullBodyReflexology');
    await swipe('scrollPackages', 'up');
    await expectElementVisible('itemPackage_lightMassageReflexology');
    await expectElementVisible('txtChooseFavoriteTasker');
    await tapId('itemPackage_lightMassageReflexology');
    await waitForElement('txt_lightMassageReflexology', 1000);
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await expectElementVisible('btn_random');
    await tapId('btn_male');
    await tapId('option_package1');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await expectElementVisible('txtTimeWorking');
    await expectElementVisible('massagePackage_1');
    await expectElementNotExist('massagePackage_2');
    await expectElementVisible('btnSubmitPostTask');
    await swipe('scrollViewStep4', 'up');
    await tapId('ckConfirmed');
    await tapId('btnSubmitPostTask');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    await expectElementVisible('serviceNameMy Task');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('massage_lightMassageReflexology');
    await tapId('taskMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectElementVisible('packageMassage_lightMassageReflexology');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
  });

  it('LINE 84 - New User post massage task with couple option', async () => {
    await postTask('postTaskServiceMASSAGE', 'Jakarta Barat', 'My Task');
    try {
      await tapId('btnCouple');
    } catch (error) {}
    // POST TASK STEP 2
    await tapId('btnTabBarMassage2');
    await expectElementVisible('txtNumberOfCustomer');
    await expectElementVisible('itemPackage_fullBody');
    await expectElementVisible('itemPackage_fullBodyReflexology');
    await swipe('scrollPackages', 'up');
    await expectElementVisible('itemPackage_lightMassageReflexology');
    await expectElementVisible('txtChooseFavoriteTasker');
    await tapId('itemPackage_lightMassageReflexology');
    await expectElementVisible('txt_lightMassageReflexology');
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await expectElementVisible('btn_random');
    await tapId('btn_male');
    await tapId('option_package1');
    await expectElementVisible('txtDuplicateOption');
    await expectElementVisible('btnDuplicate_option_package1');
    await tapId('btnDuplicate_option_package1');
    await tapId('btnNextStep2');
    await tapHeaderBack();
    await swipe('scrollPackages', 'down');
    await tapId('itemPackage_fullBody');
    await tapId('btn_male');
    await tapId('option_package1');
    await tapId('btnNextStep2');
    await swipe('scrollPackages', 'up');
    await expectElementVisible('itemPackage_lightMassageReflexology');
    await tapId('itemPackage_lightMassageReflexology');
    await tapId('option_package3');
    await scroll('scrollPackageDetail', 300, 'down', 0.5, 0.5);
    await tapId('btn_male');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await expectElementVisible('txtChooseExecutionOder');
    await tapId('btnChooseSameTime');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnChooseAskerGenderPackage2_female');
    await tapId('btnConfirmChooseAskerGender');
    await expectElementVisible('txtTimeWorking');
    await expectElementVisible('massagePackage_1');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('massagePackage_2');
    await expectElementVisible('btnSubmitPostTask');
    await tapId('ckConfirmed');
    await tapId('btnSubmitPostTask');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    await expectElementVisible('serviceNameMy Task');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('massage_fullBody');
    await expectElementVisible('massage_lightMassageReflexology');
    await tapId('taskMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectElementVisible('packageMassage_fullBody');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('packageMassage_lightMassageReflexology');
    await expectElementVisible('finalCost');
  });
});
