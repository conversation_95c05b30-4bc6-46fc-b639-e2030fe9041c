/**
 * @description Change date time Massage task
 *   case 1: Asker want to change task date time - POSTED
 *   case 2: Asker want to change massage task time include promotion
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  swipe,
  waitForElement,
  typePromotionCode,
  selectTime,
  postTask,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 2000000,
};

const TASK_POSTED = {
  isoCode: 'ID',
  serviceName: 'MASSAGE',
  askerPhone: ASKER.phone,
  description: 'My Task',
  status: 'POSTED',
};

describe('FILE: e2e/c-indonesia/flow-test/massage/update-date-time/asker-change-date-time.spec.js - Change date time Massage task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 48 - Asker want to change task date time - POSTED', async () => {
    await initData('task/createTask', [TASK_POSTED]);
    await tapText('Hoạt động');
    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Dịch vụ Massage');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectElementVisible('massage_fullBody');
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectElementVisible('packageMassage_fullBody');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapId('weekdays_3');

    await selectTime(10, true, 'AM');
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
      500,
      'text',
    );
    await tapText('Đóng');
    await selectTime(8, false, 'PM');
    await tapText('Đồng ý');

    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 83 - Asker want to change massage task time include promotion', async () => {
    const PROMOTION = {
      isoCode: 'ID',
      code: 'abc123',
      value: 50000,
      target: 'ASKER',
      typeOfPromotion: 'BOTH',
      typeOfValue: 'MONEY',
      limit: 100,
      maxValue: '',
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);

    await postTask('postTaskServiceMASSAGE', 'Jakarta Barat', 'My Task');
    await tapId('btnSingle');
    // POST TASK STEP 2
    await expectElementVisible('itemPackage_fullBody');
    await tapId('itemPackage_fullBody');
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await tapId('btn_male');
    await tapId('option_package1');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await expectElementVisible('txtTimeWorking');
    await expectElementVisible('massagePackage_1');
    await swipe('scrollViewStep4', 'up');
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectIdToHaveText('txtPromotionCode', 'abc123');
    await expectIdToHaveText('originPrice', '120,000 IDR');
    await expectIdToHaveText('price', '70,000 IDR');
    await tapId('ckConfirmed');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapId('weekdays_4');
    await expectIdToHaveText('lbOriginPrice', '120,000 IDR');
    await expectIdToHaveText('lbPrice', '70,000 IDR/1h');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
    await tapId('taskMy Task');
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '70,000 Rp');
  });
});
