const { initData, tapId, tapText, postTask, waitForElement, typeToTextField } = require('../../../step-definition');

const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');
const { ASKER_INDO } = require('../../../helpers/constants');
const expect = require('chai').expect;

describe('FILE: e2e/c-indonesia/flow-test/z-view-test/asker-choose-address-have-sub-district.spec.js - Old user post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_INDO]);
    await E2EHelpers.onHaveLogin(ASKER_INDO.phone, '123456', '+62');
  });

  it('LINE 15 - New User post task cleaning with address have sub-district', async () => {
    await device.reloadReactNative();
    await postTask('postTaskServiceCLEANING', 'Jakarta Barat');
    await tapId('openModalAddress');
    await tapId('btnNewAddress');
    await waitForElement('txtInputAddress', 3000);
    await typeToTextField('txtInputAddress', 'Depok city');
    await tapText('Depok City, Jawa Barat, Indonesia');
    await tapId('btnSelectLocation');
    await tapText('Limo');
    await typeToTextField('txtDescriptionMap', 'New district');
    await tapId('btnSubmitHomeTypeModal');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    const task = await initData('task/getTaskByDescription', { isoCode: 'ID', description: 'New district' });
    expect(task.taskPlace.district).to.equal('Kota Depok');
    expect(task.taskPlace.isAddressMaybeWrong).to.equal(true);
  });
});
