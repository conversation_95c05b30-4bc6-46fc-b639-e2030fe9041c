const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  postTask,
  callService,
  expectElementNotExist,
  swipe,
} = require('../../../step-definition');
const expect = require('chai').expect;

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  FMainAccount: 1000000,
  oldUser: true,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};

describe('FILE: e2e/c-indonesia/flow-test/air-conditioner-service/asker-chat.spec.js - Asker accept task cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  it('LINE 42 - Asker accept-task cleaning - UI Item task', async () => {
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapText('Dưới 2PK');
    await expectIdToHaveText('lbPrice', '216,000 IDR/1h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('price', '216,000 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    //   await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    //   await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await initData('service/updateServiceChannel', [
      {
        isoCode: 'ID',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        taskerPhone: '**********',
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: '**********',
      isoCode: 'ID',
      financialAccountData: { ID_Promotion: ********* },
    });
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task',
      isoCode: 'ID',
    });
    const tasker = await initData('user/getUserByPhone', {
      phone: '**********',
      countryCode: '+62',
    });
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'ID',
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-indo/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await device.reloadReactNative();
    await tapText('Hoạt động');
    await waitForElement('taskerName0', 1000);
    await expectIdToHaveText('taskerName0', 'Tasker');
    await tapId('taskerName0');
    await tapId('seeMore');
    await expectElementNotExist('Chọn Tasker này', 'text');
  });

  it('LINE 101 -  Asker accept-task air-conditioner - UI task detail', async () => {
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapText('Dưới 2PK');
    await expectIdToHaveText('lbPrice', '216,000 IDR/1h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('price', '216,000 IDR');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '216,000 Rp');
    await initData('service/updateServiceChannel', [
      {
        isoCode: 'ID',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        taskerPhone: '**********',
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: '**********',
      isoCode: 'ID',
      financialAccountData: { ID_Promotion: 100000 },
    });
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task',
      isoCode: 'ID',
    });
    const tasker = await initData('user/getUserByPhone', {
      phone: '**********',
      countryCode: '+62',
    });
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'ID',
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-indo/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await device.reloadReactNative();
    await tapText('Hoạt động');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapId('taskerName0');
    await waitForElement('seeMore', 1000);
    await tapId('seeMore');
    await expectElementNotExist('Chọn Tasker này', 'text');
  });
});
