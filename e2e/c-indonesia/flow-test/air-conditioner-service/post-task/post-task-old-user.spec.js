/**
 * @description Old user post AC task (1 test cases)
 *   case 1: LINE 28 - <PERSON><PERSON> post task with Old User
 *   case 2: LINE 68 - AC Prepayment GO_PAY
 *   case 3: LINE 83 - AC Prepayment DANA
 *   case 4: LINE 98 - AC Prepayment QRIS
 * */
const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  scroll,
  expectElementVisible,
  tapHeaderBack,
  tapIdAtIndex,
  swipe,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const { device } = require('detox');

const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 1000000,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};

const postTaskAC = async () => {
  await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
  await tapText('Dưới 2PK');
  await expectIdToHaveText('lbPrice', '216,000 IDR/1h');
  await tapId('btnNextStep2');
  await tapId('btnNextStep3');
  await tapText('Tiếp tục');
};

describe('FILE: e2e/c-indonesia/flow-test/air-conditioner-service/post-task/post-task-old-user.spec.js - Old User post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  it('LINE 55 - Already Loged-in and I want to post a Task for me', async () => {
    await postTaskAC();
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    const data = await initData('user/getUserByPhone', {
      phone: '**********',
      countryCode: '+62',
    });
    expect(data.cities[0].country).to.equal('ID');
    expect(data.cities[0].city).to.equal('Jakarta');
  });

  it('LINE 68 - AC Prepayment GO_PAY', async () => {
    await postTaskAC();
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('GoPay');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Số tiền cần thanh toán qua GO_PAY là 216,000 IDR', 'text');
    await tapText('Hủy');
    await tapText('Thanh toán lại');
    await swipe('scrollTaskDetail', 'up');
    await tapId('choosePaymentMethod');
    await tapText('GoPay');
    await tapText('Đăng việc');
    await expectElementVisible('Số tiền cần thanh toán qua GO_PAY là 216,000 IDR', 'text');
  });

  it('LINE 83 - AC Prepayment DANA', async () => {
    await postTaskAC();
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('DANA');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('Số tiền cần thanh toán qua DANA là 216,000 IDR', 'text');
    await tapText('Hủy');
    await tapText('Thanh toán lại');
    await swipe('scrollTaskDetail', 'up');
    await tapId('choosePaymentMethod');
    await tapText('DANA');
    await tapText('Đăng việc');
    await expectElementVisible('Số tiền cần thanh toán qua DANA là 216,000 IDR', 'text');
  });

  it('LINE 98 - AC Prepayment QRIS', async () => {
    await postTaskAC();
    await scroll('scrollViewStep4', 300, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('QRIS');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await expectElementVisible('216,000 IDR', 'text');
    await expectElementVisible('Lưu hình', 'text');
    await device.reloadReactNative();
    await tapText('Hoạt động');
    await tapText('Thanh toán lại');
    await swipe('scrollTaskDetail', 'up');
    await tapId('choosePaymentMethod');
    await tapText('QRIS');
    await tapText('Đăng việc');
    await expectElementVisible('216,000 IDR', 'text');
    await expectElementVisible('Lưu hình', 'text');
  });

  it('LINE 116 - Post task with option refill gas', async () => {
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapText('Dưới 2PK');
    await tapId('switchAC_Wall_Refill');
    await tapIdAtIndex('btnPlus', 0);
    await tapIdAtIndex('btnPlusGas', 0);
    await tapIdAtIndex('btnMinusGas', 0);
    await tapIdAtIndex('btnMinusGas', 0);
    await tapIdAtIndex('btnMinus', 0);
    await expectIdToHaveText('lbPrice', '216,000 IDR/1h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    const data = await initData('user/getUserByPhone', {
      phone: '**********',
      countryCode: '+62',
    });
    expect(data.cities[0].country).to.equal('ID');
    expect(data.cities[0].city).to.equal('Jakarta');
  });

  it('LINE 142 - Book task addons Apartment', async () => {
    await initData('service/updateService', {
      isoCode: 'ID',
      serviceName: 'AIR_CONDITIONER_SERVICE',
      dataUpdate: {
        addons: [
          {
            text: {
              vi: 'Nhà',
              en: 'Apartment',
              ko: '아파트',
              id: 'อพาร์ทเม้นต์',
            },
            name: 'APARTMENT',
            cost: 30000,
          },
        ],
      },
    });

    await device.reloadReactNative();

    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapId('chooseApartment');
    await tapText('Đã hiểu');

    await tapText('Dưới 2PK');
    await tapId('btnNextStep2');

    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    const data = await initData('task/getTaskByDescription', { description: 'My Task', isoCode: 'ID' });
    expect(data.addons[0].name).to.equal('APARTMENT');
    expect(data.addons[0].cost).to.equal(30000);
  });
});
