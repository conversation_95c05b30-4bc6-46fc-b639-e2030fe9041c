/**
 * @description
 *   case 1:
 * */

const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  waitForElement,
  swipe,
  expectElementVisible,
  typePromotionCode,
  expectElementNotVisible,
  tapHeaderBack,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 1000000,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};

describe('FILE: e2e/c-indonesia/flow-test/air-conditioner-service/post-task/post-task-promotion.spec.js - Post task promotion', () => {
  const checkPricePostTaskPromotion = async ({ code }) => {
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    // post task step 1
    await tapText('Dưới 2PK');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(code);
    await expectIdToHaveText('txtPromotionCode', code.toLowerCase());
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy lạnh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
  };
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'ID',
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
      {
        isoCode: 'ID',
        code: 'opq123',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
        MaxValue: '',
      },
      {
        isoCode: 'ID',
        code: 'lmn123',
        Value: 50000,
        Target: 'ASKER',
        TypeOfPromotion: 'NEW',
        TypeOfValue: 'MONEY',
        Limit: 1,
        MaxValue: '',
      },
      {
        isoCode: 'ID',
        code: 'btaskee789',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
        maxValue: 30000,
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  it('LINE 109 - AIR_SERVICE promotion', async () => {
    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: 'ID',
          serviceName: 'AIR_CONDITIONER_SERVICE',
          askerPhone: '**********',
          description: 'MAY LANH 01',
        },
      ]);
      await initData('promotion/apply-promotion-code-to-task', [
        {
          description: 'MAY LANH 01',
          isoCode: 'ID',
          promotionCode: 'def123',
        },
      ]);

      await tapText('Hoạt động');
      await tapId('taskDuration0');
      await swipe('scrollTaskDetail', 'up');

      // add Promotion và giá đã giảm
      await expectElementVisible('Giá dịch vụ', 'text');
      await expectElementVisible('216,000 Rp', 'text');
      await expectElementVisible('Giảm giá', 'text');
      await expectElementVisible('-50,000 Rp', 'text');
      await expectElementVisible('Phương thức thanh toán', 'text');
      await expectIdToHaveText('finalCost', '166,000 Rp');
    }
  });

  it('LINE 141 - Asker post task Indo with promotion code percentage "btaskee789"', async () => {
    await checkPricePostTaskPromotion({
      code: 'btaskee789',
    });
    await expectElementVisible('216,000 Rp', 'text');
    await expectElementVisible('-30,000 Rp', 'text');
    await expectIdToHaveText('finalCost', '186,000 Rp');
  });

  it('LINE 150 - Asker post task with invalid promotion code "lmn123" ', async () => {
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    // post task step 1
    await tapText('Dưới 2PK');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('lmn123');
    await expectElementNotVisible('Mã khuyến mãi không hợp lệ.');
    await tapText('Đóng');
    await tapHeaderBack();
    await expectElementNotVisible('Đăng việc');
  });

  it('LINE 167 - Asker post task Indo with promotion code percentage "opq123"', async () => {
    await checkPricePostTaskPromotion({
      code: 'opq123',
    });
    await expectElementVisible('216,000 Rp', 'text');
    await expectElementVisible('-86,000 Rp', 'text');
    await expectIdToHaveText('finalCost', '130,000 Rp');
  });
});
