const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');

const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  signUpWithModal,
  forgotPasswordWithModal,
  swipe,
  expectElementVisible,
  waitForElement,
  loginWithModal,
  tapIdAtIndex,
  selectTime24h,
  tapTask,
} = require('../../../../step-definition');

const ASKER = {
  randomId: true,
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 1000000,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};

describe('FILE: e2e/c-indonesia/flow-test/air-conditioner-service/post-task/post-task-and-sign-in.spec.js - New User post air-conditioner task before sign in', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.byPassUnauthorize();
    try {
      await tapId('cancelVerify');
    } catch (error) {}
  });

  const checkDataAfterPostTaskAC = async () => {
    const taskDescription = 'My Task';
    await initData('task/updateTask', [
      {
        description: taskDescription,
        isoCode: 'ID',
        dataUpdate: {
          payment: {
            method: 'GO_PAY',
            status: 'PAID',
          },
          isPrepayTask: false,
        },
      },
    ]);
    await device.reloadReactNative();
    await tapId('Tab_Activity');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy lạnh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapTask(taskDescription);
    await expectIdToHaveText('quantityAC_Wall', '1');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '216,000 Rp');
  };

  const postTaskAC = async () => {
    await waitForElement('postTaskServiceAIR_CONDITIONER_SERVICE', 500);
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapText('Dưới 2PK');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Treo tường', 'text');
    await expectElementVisible('Dưới 2PK', 'text');
    await expectElementVisible('price');
    await tapId('choosePaymentMethod');
    await tapId('paymentMethodGoPay');
    await tapText('Đăng việc');
  };

  it('LINE 74 - New customer post air-conditioner task and sign up', async () => {
    await postTaskAC();
    await signUpWithModal('Bill Gate', '0834567899', '<EMAIL>', '+62');
    await checkDataAfterPostTaskAC();
  });

  it('LINE 81 - New customer post air-conditioner task and sign in', async () => {
    await postTaskAC();
    await loginWithModal('**********', '123456');
    await checkDataAfterPostTaskAC();
  });

  it('LINE 88 - New customer post air-conditioner task and for got password', async () => {
    await postTaskAC();
    await forgotPasswordWithModal('**********', '+62');
    await checkDataAfterPostTaskAC();
  });

  // Gas and SERVICE 2h
  it('LINE 96 - New User post task AIR_CONDITIONER_SERVICE 2h and Gas', async () => {
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapText('Dưới 2PK');
    await tapIdAtIndex('btnPlus');
    await tapId('switchAC_Wall_Refill');
    await expectIdToHaveText('lbPrice', '582,000 IDR/2h');
    await expectElementVisible('Bơm gas chỉ bơm tối đa 30PSI/lần', 'text');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy lạnh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');
    await expectIdToHaveText('quantityAC_Wall', '2');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '582,000 Rp');
  });

  // Type Ceilling
  it('LINE 119 - Asker post task with Ceilling type', async () => {
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapId('Cassette');
    await tapId('Ceilling');
    await tapId('Ceilling_Dưới 5PK');
    await tapId('switchAC_Ceilling_Refill');
    await expectIdToHaveText('lbPrice', '940,000 IDR/1h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '940,000 Rp');
  });

  // Type Giấu Trần
  it('LINE 138 - Asker post task with Built-in type', async () => {
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', 'Jakarta Barat', 'My Task');
    await tapId('Cassette');
    await tapId('Ceilling');
    await tapId('Built-in');
    await tapId('Built-in_Giấu trần');
    await tapId('switchAC_Built-in_Refill');
    await expectIdToHaveText('lbPrice', '404,000 IDR/1h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('finalCost', '404,000 Rp');
  });

  // Change dateTime
  it('LINE 158 - Asker want to change task datetime - POSTED', async () => {
    // TODO: Run on Android

    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: 'ID',
          serviceName: 'AIR_CONDITIONER_SERVICE',
          askerPhone: '**********',
          description: 'Don dep nha 01',
          status: 'POSTED',
        },
      ]);
      await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
      await tapText('Hoạt động');
      await expectIdToHaveText('serviceNameDon dep nha 01', 'Vệ sinh máy lạnh');
      await expectIdToHaveText('taskStatusDon dep nha 01', 'Mới đăng');
      await tapId('taskDon dep nha 01');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await tapId('btnGoToUpdateDateTime');
      // await selectTime(1, true, 'AM');
      await tapId('weekdays_3');
      await selectTime24h(23);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await waitForElement(
        'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
        500,
        'text',
      );
      await tapText('Đóng');
      await selectTime24h(23, 10);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');
      await tapId('taskDon dep nha 01');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    }
  });

  // Change dateTime
  it('LINE 208 - Asker want to change task datetime - WAITING_ASKER_CONFIRMATION', async () => {
    // TODO: Run on Android

    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: 'ID',
          serviceName: 'AIR_CONDITIONER_SERVICE',
          askerPhone: '**********',
          description: 'Ve Sinh May Lanh',
          status: 'WAITING_ASKER_CONFIRMATION',
        },
      ]);
      await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
      await tapText('Hoạt động');
      await expectIdToHaveText('serviceNameVe Sinh May Lanh', 'Vệ sinh máy lạnh');
      await expectIdToHaveText('taskStatusVe Sinh May Lanh', 'Chờ xác nhận');
      await tapId('taskVe Sinh May Lanh');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await tapId('btnGoToUpdateDateTime');
      // await selectTime(1, true, 'AM');
      await tapId('weekdays_3');
      await selectTime24h(23);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await waitForElement(
        'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
        500,
        'text',
      );
      await tapText('Đóng');
      await selectTime24h(6);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');
      await tapId('taskVe Sinh May Lanh');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    }
  });

  // Change dateTime
  it('LINE 250 - Asker want to change task datetime - CONFIRMED', async () => {
    // TODO: Run on Android

    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: 'ID',
          serviceName: 'AIR_CONDITIONER_SERVICE',
          askerPhone: '**********',
          description: 'Ve Sinh May Lanh',
          status: 'CONFIRMED',
        },
      ]);
      await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
      await tapText('Hoạt động');
      await expectIdToHaveText('serviceNameVe Sinh May Lanh', 'Vệ sinh máy lạnh');
      await expectIdToHaveText('taskStatusVe Sinh May Lanh', 'Xác nhận');
      await tapId('taskVe Sinh May Lanh');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await tapId('btnGoToUpdateDateTime');
      // await selectTime(1, true, 'AM');
      await tapId('weekdays_3');
      await selectTime24h(23);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await waitForElement(
        'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
        500,
        'text',
      );
      await tapText('Đóng');
      await selectTime24h(6);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');
      await tapId('taskVe Sinh May Lanh');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    }
  });
});
