const {
  initData,
  tapId,
  tapText,
  postTask,
  expectElementVisible,
  tapHeaderBack,
  tapTask,
  swipe,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: *********,
};

const TASKER = {
  _id: 'userId_**********',
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
  workingPlaces: [
    {
      country: 'ID',
      city: 'Bali',
    },
  ],
};

const BALI = {
  _id: 'x20c7f34f231ee1e2061f2c5626ccf1ef',
  lat: -8.4095178,
  lng: 115.188916,
  country: 'ID',
  city: 'Bali',
  district: 'Denpasar',
  address: 'Bali, Indonesia',
  contact: '<PERSON><PERSON>',
  phoneNumber: '**********',
  shortAddress: 'Bali Indonesia',
  countryCode: '+62',
  isoCode: 'ID',
  homeType: 'HOME',
  description: 'Task Description Bali',
  isAddressMaybeWrong: true,
};

const JAKARTA = {
  _id: 'x20c7f34f231ee1e2061f2c5626cc',
  lat: 8.4095178,
  lng: 115.188916,
  country: 'ID',
  city: 'Jakarta',
  district: 'West Jakarta',
  address: 'Jakarta, Indonesia',
  contact: 'Asker',
  phoneNumber: '**********',
  shortAddress: 'Jakarta Indonesia',
  countryCode: '+62',
  isoCode: 'ID',
  homeType: 'HOME',
  description: 'Task Description Jakarta',
  isAddressMaybeWrong: true,
};

describe('FILE: e2e/c-indonesia/flow-test/home-moving/timezone/check-timezone.spec.js - Check timezone Home Moving', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('user/updateUser', [
      {
        phone: ASKER.phone,
        isoCode: ASKER.isoCode,
        dataUpdate: {
          homeMovingLocations: [BALI, JAKARTA],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 50 - Check flow post task', async () => {
    const OLD_HOME_DETAIL = {
      address: JAKARTA.address,
      shortAddress: JAKARTA.shortAddress,
      homeType: 'Chung cư / Căn hộ',
      acreage: '<15m2',
      options: [
        {
          title: 'Vận chuyển thang bộ',
          value: '3 tầng',
        },
        {
          title: 'Nhà trong hẻm',
          value: '100m',
        },
        {
          title: 'Vận chuyển từ hầm xe',
        },
        {
          title: 'Dọn nơi đi',
          value: '15:00',
        },
      ],
    };
    const NEW_HOME_DETAIL = {
      address: BALI.address,
      shortAddress: BALI.shortAddress,
      homeType: 'Chung cư / Căn hộ',
      acreage: '<15m2',
      options: [
        {
          title: 'Vận chuyển thang bộ',
          value: '3 tầng',
        },
        {
          title: 'Nhà trong hẻm',
          value: '100m',
        },
        {
          title: 'Vận chuyển từ hầm xe',
        },
        {
          title: 'Dọn nơi đi',
          value: '15:00',
        },
      ],
    };
    //Step 1
    await postTask('postTaskServiceHOME_MOVING');
    await tapText(OLD_HOME_DETAIL.address);
    await tapText(OLD_HOME_DETAIL.homeType);
    await tapText(OLD_HOME_DETAIL.acreage);
    await tapId('btnNextMoving');
    //Step 1

    //step 2
    await tapHeaderBack();
    await tapText(NEW_HOME_DETAIL.address);
    await tapText(NEW_HOME_DETAIL.homeType);
    await expectElementVisible(NEW_HOME_DETAIL.address, 'text');
    await tapId('btnNextMoving');

    //Step 3
    await tapId('btnNextMoving');
    //Step 3

    //step 4
    await tapId('weekdays_3');
    await tapId('btnNextMoving');
    await tapText('Tiếp theo');
    //step 4

    //step 5
    await swipe('scrollViewStep4', 'up');
    await tapId('checkboxPolicyPostTaskHomeMoving');
    await tapId('btnSubmitPostTask');
    //step 5
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapTask(JAKARTA.description);
  });
});
