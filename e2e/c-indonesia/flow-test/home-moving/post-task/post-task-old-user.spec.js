const {
  initData,
  postTask,
  tapText,
  swipe,
  expectElementNotVisible,
  tapId,
  expectElementVisible,
  expectIdToHaveText,
  scroll,
  typeToTextField,
  ADDRESS_KEY,
  waitForElement,
  ADDRESS_VALUE,
  loginWithModal,
  checkHomeTypeHomeMoving,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const {
  ISO_CODE: { ID },
} = require('../../../../helpers/constants');

const ASKER = {
  isoCode: ID,
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: ********,
};

const TASKER1 = {
  isoCode: ID,
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const LOCATION_HOME = {
  _id: 'x644a5199220423f294278b16',
  address: 'Jakarta Barat, West Jakarta City, Jakarta, Indonesia',
  city: 'Jakarta',
  contact: 'Asker',
  country: 'ID',
  countryCode: '+62',
  description: '123',
  district: 'West Jakarta',
  homeType: 'HOME',
  isoCode: 'ID',
  lat: -6.1674309,
  lng: 106.7637239,
  phoneNumber: '**********',
  shortAddress: 'West Jakarta Kebonjeruk',
};

const LOCATION_APARTMENT = {
  _id: 'x6603ce9ec7f05ae1153c95a8',
  address: 'Jakarta, Daerah Khusus Ibukota Jakarta, Indonesia',
  city: 'Jakarta',
  contact: 'Asker',
  country: 'ID',
  countryCode: '+62',
  description: '456',
  district: 'Central Jakarta',
  homeType: 'APARTMENT',
  isoCode: 'ID',
  lat: -6.1944491,
  lng: 106.8229198,
  phoneNumber: '**********',
  shortAddress: 'Jakarta Jakarta',
};

const LOCATION_VILLA = {
  _id: 'x6603ced5c7f05ae1153c95a9',
  address: 'Jakatarub, RT.1/RW.15, 15, Tomang, Kota Jakarta Barat, Daerah Khusus Ibukota Jakarta, Indonesia',
  city: 'Jakarta',
  contact: 'Asker',
  country: 'ID',
  countryCode: '+62',
  description: '789',
  district: 'West Jakarta',
  homeType: 'VILLA',
  isoCode: 'ID',
  lat: -6.1696345,
  lng: 106.8014076,
  phoneNumber: '**********',
  shortAddress: '1 15',
};

const LOCATION_DL = {
  _id: 'x65fbf298d9cda89c79cd11e6',
  address: 'Banten, Indonesia',
  city: 'Banten',
  contact: 'Asker DL',
  country: ID,
  countryCode: '+62',
  description: '159',
  district: 'Kota Tangerang',
  homeType: 'APARTMENT',
  isoCode: ID,
  lat: -6.4058172,
  lng: 106.0640179,
  phoneNumber: '**********',
  shortAddress: 'Banten Indonesia',
};

// TODO: Chờ update API
describe.skip('FILE: e2e/c-indonesia/flow-test/home-moving/post-task/post-task-old-user.spec.js - Asker post task Home Moving', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1]);
    await initData('user/updateUser', [
      {
        phone: ASKER.phone,
        isoCode: ASKER.isoCode,
        dataUpdate: {
          locations: [LOCATION_HOME, LOCATION_APARTMENT, LOCATION_VILLA, LOCATION_DL],
        },
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: ID,
      financialAccountData: { FMainAccount: ********0 },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  it('LINE 107 - Asker check and post task full option success', async () => {
    const OLD_HOME_DETAIL = {
      address: LOCATION_APARTMENT.address,
      shortAddress: LOCATION_APARTMENT.shortAddress,
      homeType: 'Chung cư',
      acreage: '1 phòng ngủ',
      options: [
        {
          title: 'Vận chuyển thang bộ',
          value: '3 tầng',
        },
        {
          title: 'Nhà trong hẻm',
          value: '100m',
        },
        {
          title: 'Vận chuyển từ hầm xe',
        },
        {
          title: 'Dọn dẹp nơi đi',
          value: '15:00',
        },
      ],
    };

    const NEW_HOME_DETAIL = {
      address: LOCATION_HOME.address,
      shortAddress: LOCATION_HOME.shortAddress,
      homeType: 'Căn hộ dịch vụ',
      acreage: '< 15 m2',
      options: [
        {
          title: 'Vận chuyển thang bộ',
          value: '2 tầng',
        },
        {
          title: 'Nhà trong hẻm',
          value: '50m',
        },
        {
          title: 'Dọn dẹp nơi đến',
          value: '18:00',
        },
      ],
    };

    const TEMPT_HOME_DETAIL = {
      address: LOCATION_VILLA.address,
      shortAddress: LOCATION_VILLA.shortAddress,
      homeType: 'Nhà phố',
      options: [
        {
          title: 'Nhà trong hẻm',
          value: '100m',
        },
        {
          title: 'Dọn dẹp nơi đến',
          value: '18:00',
        },
      ],
    };

    const FURNITURE = [
      {
        title: 'Đồ nội thất tháo lắp được',
        value: 'x1',
      },
      {
        title: 'Đồ nội thất nguyên khối',
        value: 'x2',
      },
      {
        title: 'Thiết bị điện tử',
        value: 'x3',
      },
    ];

    const checkOptionHomeDetail = async () => {
      await expectElementVisible(`${OLD_HOME_DETAIL.options[0].title} ${OLD_HOME_DETAIL.options[0].value}`, 'text');
      await expectElementVisible(
        `${OLD_HOME_DETAIL.options[1].title} ${OLD_HOME_DETAIL.options[1].value} (2m ngang)`,
        'text',
      );
      await expectElementVisible(OLD_HOME_DETAIL.options[2].title, 'text');
      await expectElementVisible(`${NEW_HOME_DETAIL.options[0].title} ${NEW_HOME_DETAIL.options[0].value}`, 'text');
      await expectElementVisible(
        `${NEW_HOME_DETAIL.options[1].title} ${NEW_HOME_DETAIL.options[1].value} (3m ngang)`,
        'text',
      );
    };

    const checkFurniture = async () => {
      await expectElementVisible(`${FURNITURE[0].title} ${FURNITURE[0].value}`, 'text');
      await expectElementVisible(`${FURNITURE[1].title} ${FURNITURE[1].value}`, 'text');
      await expectElementVisible(`${FURNITURE[2].title} ${FURNITURE[2].value}`, 'text');
      await expectElementVisible('Máy lạnh x1', 'text');
      await expectElementVisible('Máy nước nóng trực tiếp x1', 'text');
      await expectElementVisible('Máy nước nóng gián tiếp x1', 'text');
    };

    //Step 1
    await postTask('postTaskServiceHOME_MOVING');
    await tapText(OLD_HOME_DETAIL.shortAddress);
    await tapText(OLD_HOME_DETAIL.acreage);
    await expectElementVisible(OLD_HOME_DETAIL.address, 'text');
    await swipe('AddressDetailHomeMovingScrollView', 'up');
    await tapText(OLD_HOME_DETAIL.options[0].title);
    await tapText(OLD_HOME_DETAIL.options[0].value);
    await tapText('Xác nhận');
    await tapText(OLD_HOME_DETAIL.options[1].title);
    await tapText(OLD_HOME_DETAIL.options[1].value);
    await tapText('Xác nhận');
    await tapText(OLD_HOME_DETAIL.options[2].title);
    await tapText('Chọn điểm đến');
    //Step 1

    //step 2
    await expectElementNotVisible(OLD_HOME_DETAIL.address, 'text');
    await tapText(TEMPT_HOME_DETAIL.address);
    await tapText(TEMPT_HOME_DETAIL.homeType);
    await tapText('Xác nhận');
    await expectElementVisible(TEMPT_HOME_DETAIL.address, 'text');
    await swipe('AddressDetailHomeMovingScrollView', 'up');
    await expectElementVisible('warningHomeTypeHouseTxt');
    await tapText('Nhà trệt');
    await expectElementNotVisible('Vận chuyển thang máy', 'text');
    await tapText(TEMPT_HOME_DETAIL.options[0].title);
    await tapText(TEMPT_HOME_DETAIL.options[0].value);
    await tapId('plusWidthByroadButton');
    await expectElementVisible('2.5 mét', 'text');
    await tapText('Xác nhận');
    await tapText('Nhà lầu');
    await tapText('1 trệt + 1 lầu');
    await tapText('Xác nhận');
    await expectElementVisible('Vận chuyển thang máy', 'text');
    await tapText('Vận chuyển thang máy');
    await tapId('changeHomeTypeMovingBtn');
    await expectElementNotVisible('warningHomeTypeHouseTxt');
    await swipe('AddressDetailHomeMovingScrollView', 'down');
    await tapId('choseOtherAddressBtnHomeMoving');

    await tapText(NEW_HOME_DETAIL.address);
    await tapText(NEW_HOME_DETAIL.homeType);
    await tapText('Xác nhận');
    await expectElementVisible(NEW_HOME_DETAIL.address, 'text');
    await tapText(NEW_HOME_DETAIL.acreage);
    await swipe('AddressDetailHomeMovingScrollView', 'up');
    await tapText(NEW_HOME_DETAIL.options[0].title);
    await tapText(NEW_HOME_DETAIL.options[0].value);
    await tapText('Xác nhận');
    await tapText(NEW_HOME_DETAIL.options[1].title);
    await tapText(NEW_HOME_DETAIL.options[1].value);
    await tapId('plusWidthByroadButton');
    await tapId('plusWidthByroadButton');
    await expectElementVisible('3 mét', 'text');
    await tapText('Xác nhận');
    await tapText('Chọn đồ cần chuyển');
    //step 2

    //step 3
    await tapText(FURNITURE[0].title);
    await tapText(FURNITURE[1].title);
    await tapId('plusBtnFurnitureItem-1');
    await swipe('ChooseFurnitureHomeMovingScrollView', 'up');
    await tapText(FURNITURE[2].title);
    await tapId('plusBtnFurnitureItem-2');
    await tapId('plusBtnFurnitureItem-2');
    await expectIdToHaveText('quantityTxtFurnitureItem-2', '3');
    await swipe('ChooseFurnitureHomeMovingScrollView', 'up');
    await tapId('optionBtnFurnitureItem-2-0');
    await tapId('plusBtnOptionFurniture-0');
    await tapId('confirmBtnOptionFurnitureModal');
    await expectIdToHaveText('quantityTxtFurnitureItem-2-0', '1 thiết bị');
    await tapId('optionBtnFurnitureItem-2-1');
    await tapId('plusBtnOptionFurniture-0');
    await tapId('plusBtnOptionFurniture-1');
    //Nhấn thêm nếu vẫn tăng số lượng là sai vì số lượng Thiết bị điện tử là 3
    await tapId('plusBtnOptionFurniture-0');
    await tapId('plusBtnOptionFurniture-1');
    await tapId('confirmBtnOptionFurnitureModal');
    await expectIdToHaveText('quantityTxtFurnitureItem-2-1', '2 thiết bị');
    await tapText('Chọn thời gian');
    //step 3

    //step 4
    await tapId('weekdays_3');
    await tapId('btnNextStep3');
    //step 4

    //step 5
    await expectIdToHaveText('shortAddressTxtOldHomeDetail', OLD_HOME_DETAIL.address);
    await checkHomeTypeHomeMoving('homeTypeTxtOldHomeDetail', OLD_HOME_DETAIL.homeType, OLD_HOME_DETAIL.acreage);
    await expectIdToHaveText('shortAddressTxtNewHomeDetail', NEW_HOME_DETAIL.address);
    await checkHomeTypeHomeMoving('homeTypeTxtNewHomeDetail', NEW_HOME_DETAIL.homeType, NEW_HOME_DETAIL.acreage);
    await checkOptionHomeDetail();
    await swipe('OverviewHomeMovingScrollView', 'up');
    await expectElementVisible(OLD_HOME_DETAIL.options[3].title, 'text');
    await expectElementVisible(NEW_HOME_DETAIL.options[2].title, 'text');
    await expectElementVisible(FURNITURE[0].title, 'text');
    await expectElementVisible(FURNITURE[0].value, 'text');
    await expectElementVisible(FURNITURE[1].title, 'text');
    await expectElementVisible(FURNITURE[1].value, 'text');
    await expectElementVisible(FURNITURE[2].title, 'text');
    await expectElementVisible(FURNITURE[2].value, 'text');
    await tapText('Tiếp theo');
    //step 5

    await expectElementVisible(OLD_HOME_DETAIL.shortAddress, 'text');
    await expectElementVisible(NEW_HOME_DETAIL.shortAddress, 'text');
    await expectElementVisible('timeMovingTxt');
    await expectElementVisible('timeCleaningOldHomeDetailTxt');
    await expectElementVisible('timeCleaningNewHomeDetailTxt');
    await scroll('scrollViewStep4', 650, 'down', 0.5, 0.5);
    await checkHomeTypeHomeMoving('homeTypeTxtOldHomeDetailStep4', OLD_HOME_DETAIL.homeType, OLD_HOME_DETAIL.acreage);
    await checkHomeTypeHomeMoving('homeTypeTxtNewHomeDetailStep4', NEW_HOME_DETAIL.homeType, NEW_HOME_DETAIL.acreage);
    // await checkOptionHomeDetail();
    await checkFurniture();
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('txtPaymentMethod', 'bPay');
    await tapId('checkboxPolicyPostTaskHomeMoving');
    await tapId('btnSubmitPostTask');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapId('task456');
    // await expectElementVisible(OLD_HOME_DETAIL.address, 'text');
    // await expectElementVisible(NEW_HOME_DETAIL.address, 'text');
    await expectElementVisible('timeMovingTaskTxt');
    await expectElementVisible('timeCleaningTaskOldHomeTxt');
    await expectElementVisible('timeCleaningTaskNewHomeTxt');
    await scroll('scrollTaskDetail', 650, 'down', 0.5, 0.5);
    await checkHomeTypeHomeMoving('homeTypeTxtOldHomeTask', OLD_HOME_DETAIL.homeType, OLD_HOME_DETAIL.acreage);
    await checkHomeTypeHomeMoving('homeTypeTxtNewHomeTask', NEW_HOME_DETAIL.homeType, NEW_HOME_DETAIL.acreage);
    await checkOptionHomeDetail();
    await checkFurniture();
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('paymentMethod', 'bPay');
  });

  it('LINE 380 - Asker post task not login', async () => {
    await E2EHelpers.onHaveLogout();
    await postTask('postTaskServiceHOME_MOVING');
    await expectElementVisible('notLoginHomeMovingTxt');
    await tapId('loginNowBtnHomeMoving');
    await loginWithModal(ASKER.phone, '123456', '+62');
    await expectElementNotVisible('notLoginHomeMovingTxt');
  });

  it('LINE 389 - Check location', async () => {
    const NEW_ADDRESS = ADDRESS_VALUE[ADDRESS_KEY.ID];
    const NEW_ADDRESS_2 = 'Jakarta Barat, Kota Jakarta Barat, Daerah Khusus Ibukota Jakarta, Indonesia';

    await postTask('postTaskServiceHOME_MOVING');
    await tapText(LOCATION_APARTMENT.shortAddress);
    await tapId('editBtnHomeDetail');
    await tapId('deleteBtnAddress');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await expectElementVisible('Chọn địa chỉ', 'text');
    await tapId('addNewAddressBtnHomeMoving');
    await typeToTextField('txtInputAddress', ADDRESS_KEY.ID);
    await waitForElement(NEW_ADDRESS, 3000, 'text');
    await tapText(NEW_ADDRESS);
    await tapId('btnSelectLocation');
    await typeToTextField('txtDescriptionMap', '123');
    await tapText('Đồng ý');
    await tapText('Lưu');
    await tapText('Đóng');
    await expectElementVisible(NEW_ADDRESS, 'text');
    await tapText(NEW_ADDRESS);
    await tapText('Nhà phố');
    await tapText('Xác nhận');
    await expectElementVisible(NEW_ADDRESS, 'text');
    await tapId('choseOtherAddressBtnHomeMoving');
    await tapText('Thêm địa chỉ mới');
    await typeToTextField('txtInputAddress', 'Jakarta Barat');
    await tapId('addressResult_0');
    await tapId('btnSelectLocation');
    await typeToTextField('txtDescriptionMap', '456');
    await tapText('Đồng ý');
    await tapText('Lưu');
    await tapText('Đóng');
    await tapText('Nhà phố');
    await tapText('Xác nhận');
    await expectElementVisible(NEW_ADDRESS_2, 'text');
    await tapId('choseOtherAddressBtnHomeMoving');
    await tapText(LOCATION_DL.address);
    await expectElementVisible('notSupportLocationHomeMoving');
  });
});
