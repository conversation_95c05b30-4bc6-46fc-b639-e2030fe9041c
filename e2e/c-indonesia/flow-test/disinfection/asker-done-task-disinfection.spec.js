const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectIdToHaveText,
  swipe,
  postTaskDisinfection,
} = require('../../../step-definition');
const { device } = require('detox');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: ********,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};

describe('FILE: e2e/c-indonesia/flow-test/disinfection/asker-done-task-disinfection.spec.js - Asker done disinfection task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 37 - Done task pay with bPay', async () => {
    await postTaskDisinfection('Jakarta Barat');

    // POST TASK STEP 2
    await tapId('area_0');
    await expectElementVisible('lbPrice');
    await tapId('area_1');
    await expectElementVisible('lbPrice');
    await tapId('area_2');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // // POST TASK STEP 4
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectIdToHaveText('areaStep4', '101m² - 200m²');
    await expectIdToHaveText('detailStep4', 'Khử khuẩn - Trong nhà');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await tapText('Lịch sử');
    await expectElementVisible('Chưa có lịch sử đăng việc', 'text');
    await initData('task/acceptedTask', [
      {
        isoCode: 'ID',
        status: 'CONFIRMED',
        taskerAccepted: [TASKER.phone],
        description: 'My Task',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: 'ID',
        dataUpdate: {
          status: 'DONE',
        },
      },
    ]);
    await device.reloadReactNative();
    await tapId('star5');
    await tapId('btnRating');
    await tapId('btnLater');
    await tapText('Hoạt động');
    await tapId('btnTaskHistory');
    await expectElementVisible('serviceNameMy Task');
  });
});
