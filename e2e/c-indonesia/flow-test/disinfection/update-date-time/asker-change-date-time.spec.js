/**
 * @description Change date time Disinfection task
 *   case 1: Asker want to change task date time - POSTED
 *   case 2: Asker want to change task date time - WAITING
 *   case 3: Asker want to change task date time - CONFIRMED
 *   case 4: Asker want to change deep cleaning task time include promotion
 *   case 5: Asker want to change deep cleaning task time include promotion 100m2 720k
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  swipe,
  waitForElement,
  typePromotionCode,
  selectTime,
  expectIdToHaveTextAtIndex,
  postTaskDisinfection,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: ********,
};
const TASKER1 = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
};
const TASKER2 = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
};
const TASKER3 = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker 03',
  type: 'TASKER',
  status: 'ACTIVE',
};

const TASK_POSTED = {
  isoCode: 'ID',
  serviceName: 'DISINFECTION_SERVICE',
  askerPhone: ASKER.phone,
  description: 'My Task',
  status: 'POSTED',
};

describe('FILE: e2e/c-indonesia/flow-test/disinfection/update-date-time/asker-change-date-time.spec.js - Change date time Deep Cleaning task thailand', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2, TASKER3]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 81 - Asker want to change task date time - POSTED', async () => {
    await initData('task/createTask', [TASK_POSTED]);
    await tapText('Hoạt động');
    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Khử khuẩn');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveTextAtIndex('areaTask0', 'Diện tích: 1m² - 70m²', 0);
    await expectIdToHaveTextAtIndex('taskDuration0', 'Vào lúc: 14:00', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('1m² - 70m²', 'text');
    await expectElementVisible('Khử khuẩn - Trong nhà', 'text');
    await expectElementVisible('finalCost');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapId('weekdays_3');

    await selectTime(10, true, 'AM');
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
      500,
      'text',
    );
    await tapText('Đóng');
    await selectTime(8, false, 'PM');
    await tapText('Đồng ý');

    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
    await expectIdToHaveTextAtIndex('taskDuration0', 'Vào lúc: 16:00', 0);
  });

  it('LINE 120 - Asker want to change disinfection task time include promotion', async () => {
    const PROMOTION = {
      isoCode: 'ID',
      code: 'abc123',
      value: 50000,
      target: 'ASKER',
      typeOfPromotion: 'BOTH',
      typeOfValue: 'MONEY',
      limit: 100,
      maxValue: '',
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);

    await postTaskDisinfection('Jakarta Barat');
    await tapId('area_0');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('price');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectIdToHaveText('txtPromotionCode', 'abc123');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Khử khuẩn');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');

    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapId('weekdays_4');
    await expectElementVisible('lbOriginPrice');
    await expectElementVisible('lbPrice');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectElementVisible('finalCost');
  });

  it('LINE 172 - Asker want to change deep cleaning task time include promotion 70m2 550k', async () => {
    const PROMOTION = {
      isoCode: 'ID',
      code: 'abc123',
      value: 50000,
      target: 'ASKER',
      typeOfPromotion: 'BOTH',
      typeOfValue: 'MONEY',
      limit: 100,
      maxValue: '',
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);

    await postTaskDisinfection('Jakarta Barat');
    await tapId('area_0');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('price', '550,000 IDR');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION.code);
    await expectIdToHaveText('txtPromotionCode', PROMOTION.code);
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Khử khuẩn');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');

    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await tapId('weekdays_4');
    await expectElementVisible('lbOriginPrice');
    await expectElementVisible('lbPrice');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');
    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectElementVisible('finalCost');
  });
});
