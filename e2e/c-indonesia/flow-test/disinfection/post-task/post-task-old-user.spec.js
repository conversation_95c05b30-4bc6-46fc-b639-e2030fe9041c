/**
 * @description Old User post task disinfection
 *   case 1: Old User post disinfection task
 *   case 2: Old User post disinfection task
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  expectElementVisible,
  expectIdToHaveTextAtIndex,
  typeToTextFieldSubmitKeyboard,
  reloadApp,
  postTaskDisinfection,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: ********,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

describe('FILE: e2e/c-indonesia/flow-test/disinfection/post-task/post-task-old-user.spec.js - Old User post task', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 44 - Old User post disinfection task', async () => {
    await postTaskDisinfection('Jakarta Barat');

    // POST TASK STEP 2
    await tapId('area_0');
    await expectElementVisible('lbPrice');
    await tapId('area_1');
    await expectElementVisible('lbPrice');
    await tapId('area_2');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // // POST TASK STEP 4
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectIdToHaveText('areaStep4', '101m² - 200m²');
    await expectIdToHaveText('detailStep4', 'Khử khuẩn - Trong nhà');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Khử khuẩn');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveTextAtIndex('areaTask0', 'Diện tích: 101m² - 200m²', 0);
    await expectIdToHaveTextAtIndex('taskDuration0', 'Vào lúc: 14:00', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('101m² - 200m²', 'text');
    await expectElementVisible('Khử khuẩn - Trong nhà', 'text');
    await expectElementVisible('finalCost');
  });

  it('LINE 85 - Old User post disinfection task custom area', async () => {
    await postTaskDisinfection('Jakarta Barat');
    await tapId('btnDown');

    // POST TASK STEP 2
    await typeToTextFieldSubmitKeyboard('customArea', '4000');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectIdToHaveText('areaStep4', '4000m²');
    await expectIdToHaveText('detailStep4', 'Khử khuẩn - Trong nhà');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Khử khuẩn');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveTextAtIndex('areaTask0', 'Diện tích: 4000m²', 0);
    await expectIdToHaveTextAtIndex('taskDuration0', 'Vào lúc: 14:00', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('4000m²', 'text');
    await expectElementVisible('Khử khuẩn - Trong nhà', 'text');
    await expectElementVisible('finalCost');
  });
});
