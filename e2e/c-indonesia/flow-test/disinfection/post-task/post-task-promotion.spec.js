/**
 * @description See marketing campaign for Deep cleaning
 *   case 1: See campaign marketing and post task Deep cleaning
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  waitForElement,
  swipe,
  expectElementVisible,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: '<PERSON><PERSON>',
  type: 'ASKER',
  status: 'ACTIVE',
  FMainAccount: ********,
};

const PROMOTION = {
  isoCode: 'ID',
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'BOTH',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: 30000,
};

const CAMPAIGN = {
  isoCode: 'ID',
  status: 'ACTIVE',
  code: PROMOTION.code,
  serviceName: 'DISINFECTION_SERVICE',
  type: 'PROMOTION',
  primaryNavigate: 'IntroDisinfectionService',
  secondaryNavigate: 'Home',
  isSpecialCampaign: true,
};

describe('FILE: e2e/c-indonesia/flow-test/disinfection/post-task/post-task-promotion.spec.js - See marketing campaign for Disinfection cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('promotion/create-promotion-code', [PROMOTION]);
  });

  it('LINE 46 - See campaign marketing and post task Disinfection', async () => {
    await initData('campaign/createMarketingCampaign', CAMPAIGN);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');

    await tapText('Đăng việc ngay');
    await tapId('address1');
    await tapId('area_0');
    await expectElementVisible('lbOriginPrice');
    await expectElementVisible('lbPrice');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await tapId('btnSubmitPostTask');
    await tapId('btnConfirmAndBookNow');
    await tapText('Theo dõi công việc');
    await waitForElement('taskMy Task', 500);
  });
});
