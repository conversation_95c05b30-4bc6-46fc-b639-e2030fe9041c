/**
 * @description Asker cancel task (7 test cases)
 *   case 1: <PERSON><PERSON><PERSON> 33 - <PERSON><PERSON> cancel posted task
 *   case 2: <PERSON><PERSON><PERSON> 56 Ask<PERSON> cancel task - WAITING
 *   case 3: <PERSON><PERSON><PERSON> 77 - <PERSON><PERSON> cancel task - Confirmed task before working time
 *   case 4: <PERSON><PERSON><PERSON> 118 - Ask<PERSON> cancel confirmed cleaning task before working time, find same task for Tasker
 *   case 5: <PERSON><PERSON><PERSON> 147 - Ask<PERSON> cancel confirmed cleaning task after task began 15 minutes
 *   case 6: <PERSON><PERSON><PERSON> 178 - <PERSON><PERSON> cancel confirmed cleaning task with fee 20k
 *   case 7: <PERSON><PERSON><PERSON> 210 - <PERSON><PERSON> cancel waiting cleaning task with fee 0k
 *   case 8: L<PERSON><PERSON> 235 - Ask<PERSON> cancel posted task with free charge
 *   case 9: LINE 263 - <PERSON><PERSON> cancel confirmed cleaning task with reason Tasker not comming free
 *   case 10: LINE 383 - <PERSON><PERSON> cancel cleaning task before task begining 2 hours
 * */
const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  waitForElement,
  tapText,
  expectElementNotExist,
  reloadApp,
} = require('../../../step-definition');

const moment = require('moment');
const expect = require('chai').expect;

describe('FILE: e2e/c-indonesia/flow-test/washing-machine/cancel-task.spec.js - <PERSON><PERSON> cancel task', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [
      { isoCode: 'ID', phone: '**********', name: 'Asker', type: 'ASKER', status: 'ACTIVE' },
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE' },
    ]);
    await initData('task/createTask', [
      {
        isoCode: 'ID',
        serviceName: 'WASHING_MACHINE',
        askerPhone: '**********',
        description: 'My Task 01',
      },
      {
        isoCode: 'ID',
        serviceName: 'WASHING_MACHINE',
        askerPhone: '**********',
        description: 'My Task 02',
      },
      {
        isoCode: 'ID',
        serviceName: 'WASHING_MACHINE',
        askerPhone: '**********',
        description: 'My Task 03',
      },
      {
        isoCode: 'ID',
        serviceName: 'WASHING_MACHINE',
        askerPhone: '**********',
        description: 'My Task 04',
      },
    ]);
    await initData('task/acceptedTask', [
      {
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: ['**********'],
        description: 'My Task 01',
        isoCode: 'ID',
      },
      { status: 'CONFIRMED', taskerAccepted: ['**********'], description: 'My Task 02', isoCode: 'ID' },
    ]);
    await E2EHelpers.onHaveLogin('**********', '123456', '+62');
  });

  it('LINE 74 - Asker cancel posted task', async () => {
    await tapId('Tab_Activity');
    await swipe('scrollUpcoming', 'up');
    await waitForElement('taskMy Task 04', 500);
    await tapId('taskMy Task 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 04');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 04', isoCode: 'ID' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 106 Asker cancel task - WAITING', async () => {
    await tapId('Tab_Activity');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 01');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 01', isoCode: 'ID' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 135 - Asker cancel task - Confirmed task before working time', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'My Task 04',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: '**********', favouriteTasker: ['**********'], isoCode: 'ID' },
    ]);
    await tapId('Tab_Activity');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await expectElementNotExist('Tasker tự ý không đến.', 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskMy Task 02');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 02', isoCode: 'ID' });
    expect(task.status).to.equal('POSTED');

    const notify = await initData('notification/get-notification', { isoCode: 'ID', phone: '**********', type: 30 });
    expect(notify.length).to.equal(1);

    const data3 = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'ID',
      taskDescription: 'My Task 02',
    });
    expect(data3.length).to.equal(1);
    expect(data3[0].type).to.equal(30);

    const data1 = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'ID',
      taskDescription: 'My Task 04',
    });
    expect(data1.length).to.equal(0);
  });

  it('LINE 213 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: '**********', favouriteTasker: ['**********'], isoCode: 'ID' },
    ]);
    await tapId('Tab_Activity');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');

    const data = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'ID',
      taskDescription: 'My Task 03',
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: '**********',
      isoCode: 'ID',
      taskDescription: 'My Task 04',
    });
    expect(data1.length).to.equal(0);

    await expectElementNotExist('taskMy Task 02');
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task 02',
      status: 'CANCELED',
      isoCode: 'ID',
    });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 262 - Asker cancel confirmed cleaning task after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'My Task 04',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'My Task 02',
        isoCode: 'ID',
        dataUpdate: {
          date: moment().toDate(),
          createdAt: moment().subtract(15, 'minutes').toDate(),
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: '**********', favouriteTasker: ['**********'], isoCode: 'ID' },
    ]);
    await tapId('Tab_Activity');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker tự ý không đến.', 500, 'text');
    await tapText('Tasker tự ý không đến.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 02');
  });

  it('LINE 316 - Asker cancel confirmed cleaning task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 01');

    const data1 = await initData('user/findFATransaction', {
      phone: '**********',
      accountType: 'M',
      type: 'C',
      amount: 20000,
      isoCode: 'ID',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(20000);

    const data2 = await initData('user/find-faccount', { phone: '**********', isoCode: 'ID' });
    expect(data2.ID_FMainAccount).to.equal(-20000);
    expect(data2.ID_Promotion).to.equal(0);
  });

  it('LINE 371 - Asker cancel waiting cleaning task with fee 0k', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await tapId('Tab_Activity');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 01');
  });

  it('LINE 403 - Asker cancel posted task with free charge', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'My Task 04', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await tapId('Tab_Activity');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskMy Task 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Tìm Tasker mới');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 04');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 04');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(0);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 435 - Asker cancel confirmed cleaning task with reason Tasker not comming free', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await tapId('Tab_Activity');
    await tapId('taskMy Task 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskMy Task 01');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(0);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 473 - Asker cancel cleaning task before task begining 2 hours', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'My Task 02', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task 02',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 02');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(-20000);
    expect(data.ID_Promotion).to.equal(0);
  });
});
