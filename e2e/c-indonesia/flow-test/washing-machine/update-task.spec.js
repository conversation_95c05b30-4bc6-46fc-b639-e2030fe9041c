/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-06-27 10:02:31
 * @modify date 2024-06-20 16:01:41
 * @description Update note
 *   case 1: Asker update note task
 *   case 2: Asker update date time task
 */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  swipe,
  reloadApp,
  expectElementVisible,
  selectTime24h,
  clearTextInput,
  typeToTextField,
} = require('../../../step-definition');

describe('FILE: e2e/c-indonesia/flow-test/washing-machine/update-task.spec.js - Update note', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [
      { isoCode: 'ID', phone: '0834567890', name: '<PERSON><PERSON>', type: 'ASKER', status: 'ACTIVE' },
    ]);
    await initData('task/createTask', [
      {
        isoCode: 'ID',
        serviceName: 'WASHING_MACHINE',
        askerPhone: '0834567890',
        description: 'My Task',
      },
    ]);
    await E2EHelpers.onHaveLogin('0834567890');
  });

  it('LINE 43 - Asker update note task', async () => {
    await tapId('Tab_Activity');
    await expectElementVisible('txtNumberFrontLoading');
    await expectElementVisible('txtNumberTopLoading');
    await expectIdToHaveText('numberFrontLoading', 'x1');
    await expectIdToHaveText('numberTopLoading', 'x1');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await clearTextInput('taskNote');
    await typeToTextField('taskNote', 'Lau dọn phong ngu tang 1\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn phong ngu tang 1\n', 500, 'text');
  });

  it('LINE 63 - Asker update date time task', async () => {
    await tapId('Tab_Activity');
    await expectElementVisible('txtNumberFrontLoading');
    await expectElementVisible('txtNumberTopLoading');
    await expectIdToHaveText('numberFrontLoading', 'x1');
    await expectIdToHaveText('numberTopLoading', 'x1');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(17);
    await tapText('Đồng ý');
    await tapText('Cập nhật');
    await tapText('Đồng ý');
    await expectElementVisible('Cập nhật thành công', 'text');
    await tapText('Đóng');
  });
});
