const { E2E<PERSON>el<PERSON> } = require('../../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  waitForElement,
  postTask,
  clearTextInput,
  typePromotionCode,
  tapIdAtIndex,
  scrollTo,
} = require('../../../../../step-definition');

describe('FILE: e2e/c-indonesia/flow-test/washing-machine/post-task/promotion-discount/post-task-with-promotion-code.spec.js - Asker post AC task with promotion code', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        isoCode: 'ID',
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        oldUser: true,
        FMainAccount: *********,
      },
      { isoCode: 'ID', phone: '**********', name: 'Asker', type: 'ASKER', status: 'ACTIVE', oldUser: true },
      { isoCode: 'ID', phone: '**********', name: 'Asker 02', type: 'ASKER', status: 'ACTIVE', oldUser: true },
    ]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'ID',
        code: 'abc123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'CURRENT',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: '',
      },
      {
        isoCode: 'ID',
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: 30000,
      },
      {
        isoCode: 'ID',
        code: 'ghk123',
        value: 50000,
        target: 'TASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: '',
      },
      {
        isoCode: 'ID',
        code: 'lmn123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 1,
        maxValue: '',
      },
      {
        isoCode: 'ID',
        code: 'opq123',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
        maxValue: '',
      },
      {
        isoCode: 'ID',
        code: 'opq789',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
        maxValue: 30000,
      },
    ]);
    await initData('promotion/usersAppliedPromotion', { isoCode: 'ID', phone: '**********', promotionCode: 'lmn123' });
    await E2EHelpers.onHaveLogin('**********', '123456');
  });

  const postTaskPromotion = async (code) => {
    await postTask('postTaskServiceWASHING_MACHINE', 'Jakarta Barat');
    // post task step 2
    await tapId('btnConfirmCreateWM-TOP_LOADING');
    await tapIdAtIndex('switchOptionWM', 0);
    await tapId('btnConfirmCreateWM-TOP_LOADING');
    await tapIdAtIndex('btnTabBarWM', 1);
    await tapIdAtIndex('btnTabTypeWM-FRONT_LOADING', 1);
    await tapId('btnConfirmCreateWM-FRONT_LOADING');
    await tapId('btnConfirmCreateWM-FRONT_LOADING');
    await tapId('btnNextStep2WM');
    await tapId('btnNextStep3WM');
    // Post task step 4
    await scrollTo('scrollViewStep4WM', 'bottom');
    // Áp dụng mã khuyến mãi giảm 50,000
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(code);
    await expectIdToHaveText('txtPromotionCode', code?.toLocaleLowerCase());
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    //DONE step 4
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy giặt');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');
    await scrollTo('scrollTaskDetail', 'bottom');
  };

  it('LINE 118 - Asker post task with promotion code money "def123"', async () => {
    await postTaskPromotion('def123');
    await expectIdToHaveText('cost', '2,300,000 Rp');
    await expectIdToHaveText('discount', '-50,000 Rp');
    await expectIdToHaveText('finalCost', '2,250,000 Rp');
  });

  it('LINE 125 - Asker post task with promotion code percentage "opq123"', async () => {
    await postTaskPromotion('opq123');
    await expectIdToHaveText('cost', '2,300,000 Rp');
    await expectIdToHaveText('discount', '-920,000 Rp');
    await expectIdToHaveText('finalCost', '1,380,000 Rp');
  });

  it('LINE 132 - Asker post task with promotion code percentage "OPQ123"', async () => {
    await postTaskPromotion('OPQ123');
    await expectIdToHaveText('cost', '2,300,000 Rp');
    await expectIdToHaveText('discount', '-920,000 Rp');
    await expectIdToHaveText('finalCost', '1,380,000 Rp');
  });
  //
  it('LINE 139 - Asker post task with invalid promotion code', async () => {
    await postTask('postTaskServiceWASHING_MACHINE', 'Jakarta Barat');
    await tapId('btnConfirmCreateWM-TOP_LOADING');
    await tapIdAtIndex('switchOptionWM', 0);
    await tapId('btnConfirmCreateWM-TOP_LOADING');
    await tapIdAtIndex('btnTabBarWM', 1);
    await tapIdAtIndex('btnTabTypeWM-FRONT_LOADING', 1);
    await tapId('btnConfirmCreateWM-FRONT_LOADING');
    await tapId('btnConfirmCreateWM-FRONT_LOADING');
    await tapId('btnNextStep2WM');
    await tapId('btnNextStep3WM');
    // POST TASK STEP 4
    await swipe('scrollViewStep4WM', 'up');

    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await waitForElement('Mã ưu đãi này không áp dụng cho tài khoản của bạn.', 500, 'text');
    await tapText('Đóng');
    await clearTextInput('textInputPromotion');
    await typePromotionCode('ghk123');
    await waitForElement('Mã ưu đãi chỉ áp dụng cho Tasker.', 500, 'text');
    await tapText('Đóng');
    await clearTextInput('textInputPromotion');
    await typePromotionCode('lmn123');
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 168 - Asker post task with promotion code percentage "opq789" and max value is 30K', async () => {
    await postTaskPromotion('opq789');
    await expectIdToHaveText('cost', '2,300,000 Rp');
    await expectIdToHaveText('discount', '-30,000 Rp');
    await expectIdToHaveText('finalCost', '2,270,000 Rp');
  });
});
