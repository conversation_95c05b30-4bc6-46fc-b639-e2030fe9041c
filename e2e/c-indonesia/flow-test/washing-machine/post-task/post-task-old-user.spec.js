/**
 * @description Old user post WM task (1 test cases)
 *   case 1: LINE 28 - <PERSON><PERSON> post task with Old User
 * */

const {
  initData,
  postTask,
  tapId,
  tapIdAtIndex,
  expectIdToHaveText,
  tapText,
  scrollTo,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

describe('FILE: e2e/c-indonesia/flow-test/washing-machine/post-task/post-task-old-user.spec.js - Old User post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        isoCode: 'ID',
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        oldUser: true,
        FMainAccount: *********,
      },
      { isoCode: 'ID', phone: '**********', name: 'Tasker', type: 'TASKER', status: 'ACTIVE', oldUser: true },
    ]);
    await E2EHelpers.onHaveLogin('**********');
  });

  it('LINE 95 - Asker post task with Old User', async () => {
    await postTask('postTaskServiceWASHING_MACHINE');
    // post task step 2
    await tapId('btnConfirmCreateWM-TOP_LOADING');
    await tapIdAtIndex('switchOptionWM', 0);
    await tapId('btnConfirmCreateWM-TOP_LOADING');
    await tapIdAtIndex('btnTabBarWM', 1);
    await tapIdAtIndex('btnTabTypeWM-FRONT_LOADING', 1);
    await tapId('btnConfirmCreateWM-FRONT_LOADING');
    await tapId('btnConfirmCreateWM-FRONT_LOADING');
    await tapId('btnNextStep2WM');
    await tapId('btnNextStep3WM');
    // Post task step 4
    await scrollTo('scrollViewStep4WM', 'bottom');
    await tapText('Đăng việc');
    await tapId('btnConfirmAndBookNow');
    //DONE step 4
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy giặt');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  });
});
