const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  expectElementVisible,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER_01 = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 1000000,
};

describe('FILE: e2e/c-indonesia/flow-test/ironing/post-task/asker-post-task-with-marketing-campaign.spec.js - Asker post task with marketing campaign', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_01]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'ID',
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: 30000,
      },
    ]);
    await initData('campaign/createMarketingCampaign', {
      isoCode: 'ID',
      status: 'ACTIVE',
      code: 'def123',
      serviceName: 'IRONING',
      type: 'PROMOTION',
      primaryNavigate: 'PostTaskStep1',
      secondaryNavigate: 'Home',
    });
    await E2EHelpers.onHaveLogin(ASKER_01.phone, '123456', '+62');
  });

  it('LINE 49 - Asker post task with promotion code money "def123"', async () => {
    await tapId('marketingCampaign_PROMOTION');
    await tapText('Đăng việc ngay');
    await tapId('chooseDuration-3');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('cost');
    await expectElementVisible('discount', '-50,000 Rp');
    await expectElementVisible('finalCost');
  });
});
