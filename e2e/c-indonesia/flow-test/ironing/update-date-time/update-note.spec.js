/**
 * @description Update note
 *   case 1: Asker update note normal task
 *   case 2: Asker update note prepay task
 * */

const {
  initData,
  tapId,
  tapText,
  waitForElement,
  swipe,
  typeToTextField,
  tapTask,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
  FMainAccount: 1000000,
};
const TASKER = {
  isoCode: 'ID',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
describe('FILE: e2e/c-indonesia/flow-test/ironing/update-date-time/update-note.spec.js - Update note', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  it('LINE 42 - Asker update note normal task', async () => {
    const TASK = {
      isoCode: 'ID',
      serviceName: 'IRONING',
      askerPhone: ASKER.phone,
      description: 'Don dep nha 01',
    };
    await initData('task/createTask', [TASK]);
    await tapId('Tab_Activity');
    await tapTask(TASK.description);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await typeToTextField('taskNote', 'Lau dọn phong ngu tang 1\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn phong ngu tang 1\n', 500, 'text');
  });

  it('LINE 59 - Asker update note prepay task', async () => {
    const TASK = {
      isoCode: 'ID',
      serviceName: 'IRONING',
      askerPhone: ASKER.phone,
      description: 'Don dep nha 01',
    };
    await initData('task/createTask', [TASK]);
    await initData('task/updateTask', [
      {
        isoCode: TASK.isoCode,
        description: TASK.description,
        dataUpdate: {
          payment: {
            method: 'SHOPEE_PAY',
            status: 'NEW',
          },
          isPrepayTask: true,
          status: 'POSTED',
        },
      },
    ]);
    await tapId('Tab_Activity');
    await tapTask(TASK.description);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await typeToTextField('taskNote', 'Lau dọn phong ngu tang 1\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn phong ngu tang 1\n', 500, 'text');
  });
});
