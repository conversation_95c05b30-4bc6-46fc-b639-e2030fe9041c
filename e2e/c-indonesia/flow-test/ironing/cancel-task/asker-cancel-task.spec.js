const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  waitForElement,
  typeToTextField,
  tapText,
  expectElementNotExist,
  waitForLoading,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const moment = require('moment');
const { E2EHelpers } = require('../../../../e2e.helpers');

const ASKER = {
  isoCode: 'ID',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'ID',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
describe('FILE: e2e/c-indonesia/flow-test/ironing/cancel-task/asker-cancel-task.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [
      {
        isoCode: 'ID',
        serviceName: 'IRONING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
      },
      {
        isoCode: 'ID',
        serviceName: 'IRONING',
        askerPhone: '0834567890',
        description: 'Don dep nha 02',
      },
      {
        isoCode: 'ID',
        serviceName: 'IRONING',
        askerPhone: '0834567890',
        description: 'Don dep nha 03',
      },
      {
        isoCode: 'ID',
        serviceName: 'IRONING',
        askerPhone: '0834567890',
        description: 'Don dep nha 04',
      },
    ]);
    const tasker = await initData('user/getUserByPhone', { phone: TASKER.phone, countryCode: '+62' });

    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'ID',
        dataUpdate: {
          status: 'WAITING_ASKER_CONFIRMATION',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
      {
        description: 'Don dep nha 02',
        isoCode: 'ID',
        dataUpdate: {
          status: 'CONFIRMED',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+62');
  });

  it('LINE 105 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('TAB_UPCOMINGDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Ngày giờ làm việc');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await waitForLoading(1000);
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { isoCode: 'ID', description: 'Don dep nha 04' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 165 - Asker cancel confirmed cleaning task before working time', async () => {
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'Don dep nha 04',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'ID' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await expectElementNotExist('Tasker tự ý không đến.', 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskDon dep nha 02');
    const task = await initData('task/getTaskByDescription', { isoCode: 'ID', description: 'Don dep nha 02' });
    expect(task.status).to.equal('POSTED');

    const notify = await initData('notification/get-notification', { isoCode: 'ID', phone: TASKER.phone, type: 30 });
    expect(notify.length).to.equal(1);
  });

  it('LINE 248 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'ID' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    const data = await initData('notification/get-notification', {
      phone: '0834567891',
      isoCode: 'ID',
      taskDescription: 'Don dep nha 03',
      type: 0,
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: '0834567891',
      isoCode: 'ID',
      taskDescription: 'Don dep nha 04',
      type: 0,
    });
    expect(data1.length).to.equal(0);
    await expectElementNotExist('taskDon dep nha 02');
    const task = await initData('task/getTaskByDescription', { isoCode: 'ID', description: 'Don dep nha 02' });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 294 - Asker cancel confirmed cleaning task after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'Don dep nha 04',
        isoCode: 'ID',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'Don dep nha 02',
        isoCode: 'ID',
        dataUpdate: {
          progress: 'AFTER_WORKING',
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'ID' },
    ]);

    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Hủy việc');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
  });

  it('LINE 354 - Asker cancel confirmed cleaning task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'Don dep nha 01', taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 01');

    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 20000,
      isoCode: 'ID',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(20000);

    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'ID' });
    expect(data2.ID_FMainAccount).to.equal(-20000);
    expect(data2.ID_Promotion).to.equal(0);
  });

  it('LINE 411 - Asker cancel waiting cleaning task with fee 0k', async () => {
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: 'LATE',
          progress: 'WAITING',
          status: 'WAITING_ASKER_CONFIRMATION',
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 01');
  });

  it('LINE 451 - Asker cancel posted task with free charge', async () => {
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 04',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: 'LATE',
          progress: 'WAITING',
          status: 'POSTED',
        },
      },
    ]);
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Ngày giờ làm việc');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 04');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(0);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 494 - Asker cancel confirmed cleaning task with reason Tasker not comming free', async () => {
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'ID',
        dataUpdate: {
          status: 'CONFIRMED',
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskDon dep nha 01');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(0);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 540 - Asker cancel task and input the reason', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Ngày giờ làm việc');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // await tapText('Đồng ý');
    await tapId('btnOtherReason');
    await typeToTextField('inputOtherReason', 'I cancel');
    await tapId('txtOtherReason'); // click lần dầu tắt bàn phím
    await tapId('btnOKChooseReason');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { isoCode: 'ID', description: 'Don dep nha 04' });

    expect(task.cancellationText).to.equal('I cancel');
  });

  it('LINE 574 - Asker cancel cleaning task after task begining', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'Don dep nha 02', taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 02',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(-30, 'minute').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(-36000);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 614 - Asker cancel cleaning task before task begining less than 1 hour', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'Don dep nha 02', taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 02',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          progress: 'BEFORE_WORKING_50M',
          status: 'CONFIRMED',
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(-36000);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 656 - Asker cancel cleaning task before task begining less than 1 hour (Max fee)', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'Don dep nha 02', taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 02',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          progress: 'BEFORE_WORKING_50M',
          status: 'CONFIRMED',
          costDetail: {
            finalCost: 5000000,
            baseCost: 5000000,
            cost: 5000000,
          },
          cost: 5000000,
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(-100000);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 704 - Asker cancel cleaning task before task begining 2 hours', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'ID', description: 'Don dep nha 02', taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 02',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
          progress: 'BEFORE_WORKING_2H',
          status: 'CONFIRMED',
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 02');
    const data = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: 'ID' });
    expect(data.ID_FMainAccount).to.equal(-20000);
    expect(data.ID_Promotion).to.equal(0);
  });

  it('LINE 770 - Asker cancel CONFIRMED cleaning task because Tasker request - Before 8 hours when task is started', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'ID' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 02',
        isoCode: 'ID',
        dataUpdate: {
          createdAt: moment().subtract(10, 'hour').toDate(),
          date: moment().add(8, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskDon dep nha 02');
  });
});
