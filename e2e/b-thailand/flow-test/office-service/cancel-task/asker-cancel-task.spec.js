/**
 * case 1: <PERSON><PERSON><PERSON> 53 - <PERSON><PERSON> cancel posted task
 * case 2: <PERSON><PERSON><PERSON> 145 - <PERSON><PERSON> cancel confirmed cleaning task before working time, find same task for Tasker
 * case 3: LINE 153 - Ask<PERSON>  cancel posted task POSTED_WRONG_DATE
 * case 4: <PERSON><PERSON><PERSON> 180 - <PERSON><PERSON>  cancel posted task NO_TASKER_ACCEPT
 * case 5: LINE 207 - <PERSON><PERSON>  cancel posted task NO_TASKER_ACCEPT
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  typeToTextField,
  expectElementVisible,
  expectElementNotExist,
  swipe,
  waitForElement,
} = require('../../../../step-definition');
const expect = require('chai').expect;

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 1000000,
};
const TASKER1 = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASKER2 = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 9,
};

const TASK1 = {
  isoCode: 'TH',
  serviceName: 'OFFICE_CLEANING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};
const TASK2 = {
  isoCode: 'TH',
  serviceName: 'OFFICE_CLEANING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 02',
};
const TASK3 = {
  isoCode: 'TH',
  serviceName: 'OFFICE_CLEANING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 03',
};
const TASK4 = {
  isoCode: 'TH',
  serviceName: 'OFFICE_CLEANING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 04',
};

describe('FILE: e2e/b-thailand/flow-test/office-service/cancel-task/asker-cancel-task.spec.js - New User posted task Office cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);
    await initData('task/createTask', [TASK1, TASK2, TASK3, TASK4]);
    await E2EHelpers.onHaveLogin(ASKER.phone);
  });

  // Bận việc đột xuất
  it('LINE 87 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');

    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Lý do khác', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { description: TASK4.description, isoCode: 'TH' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 114 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASK1.phone], isoCode: 'TH' },
    ]);

    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    try {
      await tapId('cancelTask');
      await tapId('btnConfirmCancel');
    } catch (error) {}
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    const data = await initData('notification/get-notification', {
      phone: TASKER1.phone,
      isoCode: 'TH',
      taskDescription: TASK3.description,
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: TASKER1.phone,
      isoCode: 'TH',
      taskDescription: TASK4.description,
    });
    expect(data1.length).to.equal(0);
    await expectElementNotExist('tasktask 02');
    const task = await initData('task/getTaskByDescription', { isoCode: 'TH', description: TASK2.description });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 156 - Asker  cancel posted task POSTED_WRONG_DATE', async () => {
    await tapText('Hoạt động');

    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Lý do khác', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Đăng nhầm ngày.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { description: TASK4.description, isoCode: 'TH' });
    expect(task.cancellationReason).to.equal('POSTED_WRONG_DATE');
  });

  it('LINE 183 - Asker  cancel posted task NO_TASKER_ACCEPT', async () => {
    await tapText('Hoạt động');

    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Lý do khác', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Chưa có người nhận.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { description: TASK4.description, isoCode: 'TH' });
    expect(task.cancellationReason).to.equal('NO_TASKER_ACCEPT');
  });

  it('LINE 210 - Asker  cancel posted task NO_TASKER_ACCEPT', async () => {
    const MESSAGE_REASON = 'muon huy';
    await tapText('Hoạt động');

    await swipe('scrollUpcoming', 'up');
    await tapId('taskDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Lý do khác', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Lý do khác');
    await typeToTextField('inputOtherReason', MESSAGE_REASON);
    try {
      await tapId('txtOtherReason'); // click lần dầu tắt bàn phím
      await tapId('btnOKChooseReason');
    } catch (error) {}
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { description: TASK4.description, isoCode: 'TH' });
    expect(task.cancellationText).to.equal(MESSAGE_REASON);
  });
});
