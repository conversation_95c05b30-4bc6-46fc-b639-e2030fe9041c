const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  waitForElement,
  swipe,
  waitForLoading,
} = require('../../../step-definition');
const { E2EHelpers } = require('../../../e2e.helpers');
const { SERVICE_NAME } = require('../../../helpers/constants');
const moment = require('moment');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  FMainAccount: 20000,
  oldUser: true,
};

const TASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

const TASK = {
  isoCode: 'TH',
  serviceName: SERVICE_NAME.SOFA,
  askerPhone: ASKER.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/b-thailand/flow-test/sofa-thailand/cancel-task.spec.js - <PERSON>er cancel Sofa task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER.phone);
  });

  it('LINE 40 - Asker cancel sofa task, fee = 0', async () => {
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await expectElementVisible('cancelTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
  });

  it('LINE 80 - Asker cancel sofa task include cancel fee', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: TASK.isoCode,
        description: TASK.description,
        taskerAccepted: [TASKER.phone],
        status: 'CONFIRMED',
      },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(7, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await waitForElement('cancelTask', 500);
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
  });
});
