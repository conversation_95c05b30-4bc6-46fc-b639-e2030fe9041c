const {
  tapId,
  swipe,
  tapText,
  initData,
  waitForElement,
  expectElementNotExist,
  loginWithPhoneAndPassword,
} = require('../../../step-definition');
const expect = require('chai').expect;

//TODO: <PERSON>ần update lại flow rồi update test
describe.skip('FILE: e2e/b-thailand/flow-test/deep-cleaning-thai/cancel-task-with-true-money.spec.js - Cancel with true money deep cleaning thai', () => {
  const description1 = 'Tong ve sinh 01';
  beforeEach(async () => {
    await initData('user/createUser', [
      { Country: 'TH', Phone: '0834567890', Name: 'Asker', Type: 'ASKER', Status: 'ACTIVE' },
      {
        Phone: '0834567891',
        Name: 'Tasker 01',
        Type: 'TASKER',
        Status: 'ACTIVE',
        Score: 8,
        OldUser: true,
        Country: 'TH',
      },
      {
        Phone: '0834567892',
        Name: 'Tasker 02',
        Type: 'TASKER',
        Status: 'ACTIVE',
        Score: 9,
        OldUser: true,
        Country: 'TH',
      },
    ]);
    await initData('task/createTask', [
      {
        ServiceName: 'Tổng vệ sinh',
        AskerPhone: '0834567890',
        Description: description1,
        IsoCode: 'TH',
        Cost: 400,
      },
    ]);

    // Update fee cancel
    await initData('settings/changeTH_SettingSystem', {
      taskerNotCommingFee: {
        earlyCancelFee: 20, // money
        lateCancelFee: 50, // percent
        notCommingFee: 100, // percent
      },
    });
    await device.reloadReactNative();
  });

  const _cancelTask = async ({ reason, cancelResult, refundMoney, isPromotion }) => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId(`task${description1}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // await tapText('Đồng ý');

    if (isPromotion) {
      await tapText('Tiếp tục');
    }

    await tapText(reason);
    await tapText('Đồng ý');
    await waitForElement(cancelResult, 500, 'text');
    await tapText('Đóng');
    await expectElementNotExist(`task${description1}`);
    const refundData = await initData('refundRequest/findRefundRequest', { Description: description1 });
    expect(refundData.data.type).to.equal('PREPAY_TASK');
    expect(refundData.data.amount).to.equal(refundMoney);
    expect(refundData.data.paymentMethod).to.equal('TRUE_MONEY');
    expect(refundData.data.currency).to.equal('THB');
    expect(refundData.data.status).to.equal('NEW');
  };

  it('LINE 83 - Asker cancel task true money before working 6h', async () => {
    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '0834567891',
        Description: description1,
        Progress: 'BEFORE_WORKING_8H',
        PaymentMethod: 'TRUE_MONEY',
      },
    ]);

    await loginWithPhoneAndPassword('0834567890', '123456');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 20 ฿. Số tiền 380 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 380,
    });
  });

  it('LINE 102 - Asker cancel task true money before working 1h', async () => {
    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '0834567891',
        Description: description1,
        Progress: 'BEFORE_WORKING_50M',
        PaymentMethod: 'TRUE_MONEY',
        CreatedAt: 'LATE',
      },
    ]);

    await loginWithPhoneAndPassword('0834567890', '123456');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 120 ฿. Số tiền 280 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 280,
    });
  });

  it('LINE 122 - Asker cancel task true money before working 2h', async () => {
    // fee cancel base on field cancelTaskFee = 30
    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '0834567891',
        Description: description1,
        Progress: 'BEFORE_WORKING_2H',
        PaymentMethod: 'TRUE_MONEY',
        CreatedAt: 'LATE',
      },
    ]);

    await loginWithPhoneAndPassword('0834567890', '123456');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 30 ฿. Số tiền 370 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 370,
    });
  });

  it('LINE 143 - Asker cancel task with no tasker accepted', async () => {
    await initData('task/updateTask', [
      {
        Description: description1,
        PaymentMethod: 'TRUE_MONEY',
      },
    ]);

    await loginWithPhoneAndPassword('0834567890', '123456');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 20 ฿. Số tiền 380 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 380,
    });
  });

  it('LINE 159 - Asker cancel task true money with promotion code', async () => {
    await initData('promotion/createPromotionCode', [
      {
        Code: 'abc123',
        Value: 50,
        Target: 'ASKER',
        TypeOfPromotion: 'NEW',
        TypeOfValue: 'MONEY',
        Limit: 100,
        MaxValue: '',
      },
    ]);

    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '0834567891',
        Description: description1,
        PaymentMethod: 'TRUE_MONEY',
        PromotionCode: 'abc123',
      },
    ]);

    await loginWithPhoneAndPassword('0834567890', '123456');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 20 ฿. Số tiền 330 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 330,
      isPromotion: true,
    });
  });
});
