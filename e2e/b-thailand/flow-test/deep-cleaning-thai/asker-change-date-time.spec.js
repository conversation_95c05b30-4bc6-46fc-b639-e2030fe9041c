/**
 * @description Change datetime Deep Cleaning task thailand
 *   case 1: Asker want to change task datetime - POSTED
 *   case 2: Asker want to change task datetime - WAITING
 *   case 3: Asker want to change task datetime - CONFIRMED
 *   case 4: Asker want to change deep cleaning task time include promotion
 *   case 5: Asker want to change deep cleaning task time include promotion 100m2 720k
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  swipe,
  postTask,
  waitForElement,
  typePromotionCode,
  expectElementVisibleAtIndex,
  tapTextAtIndex,
  selectTime24h,
} = require('../../../step-definition');
const moment = require('moment');
const { E2EHelpers } = require('../../../e2e.helpers');
const { COUNTRY_CODE } = require('../../../helpers/constants');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: ********,
};
const TASKER1 = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 8,
};
const TASKER2 = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  score: 9,
};

const TASK = {
  isoCode: 'TH',
  serviceName: 'DEEP_CLEANING',
  askerPhone: ASKER.phone,
  description: 'My Task',
};

const checkTimeChangeSuccess = async () => {
  await tapText('Đồng ý');
  await waitForElement('Cập nhật thành công', 500, 'text');
  await tapText('Đóng');
  await tapId('taskMy Task');
  await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
  await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
  await expectIdToHaveText('duration', '4 giờ, 16:00 đến 20:00');
};

describe('FILE: e2e/b-thailand/flow-test/deep-cleaning-thai/asker-change-date-time.spec.js - Change datetime Deep Cleaning task thailand', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1, TASKER2]);

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', COUNTRY_CODE[ASKER.isoCode]);
  });

  it('LINE 40 - Asker want to change task datetime - POSTED', async () => {
    await initData('task/createTask', [TASK]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Mới đăng', 0, 'text');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
      500,
      'text',
    );
    await tapText('Đóng');
    await selectTime24h(16, 22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await checkTimeChangeSuccess();
  });

  it('LINE 77 - Asker want to change task datetime - WAITING', async () => {
    await initData('task/createTask', [TASK]);
    await initData('task/acceptedTask', [
      {
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: [TASKER1.phone],
        description: TASK.description,
        isoCode: 'TH',
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await selectTime24h(16);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await checkTimeChangeSuccess();
  });

  it('LINE 104 - Asker want to change task datetime - CONFIRMED', async () => {
    await initData('task/createTask', [TASK]);
    await initData('task/acceptedTask', [
      {
        status: 'CONFIRMED',
        taskerAccepted: [TASKER1.phone],
        description: TASK.description,
        isoCode: 'TH',
      },
    ]);

    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Xác nhận', 0, 'text');
    await tapId('taskMy Task');
    await expectElementVisible(TASKER1.name, 'text');
  });

  it('LINE 121 - Asker want to change deep cleaning thailand task time include promotion', async () => {
    const PROMOTION = {
      isoCode: 'TH',
      code: 'abc123',
      value: 50,
      target: 'ASKER',
      typeOfPromotion: 'BOTH',
      typeOfValue: 'MONEY',
      limit: 100,
      maxValue: '',
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);
    await postTask('postTaskServiceDEEP_CLEANING', '500 Thanon Tanao');
    await tapId('area80');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // await tapText('Đăng việc');
    // await tapText('Đồng ý');

    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');

    await waitForElement('promotionCode', 5000);
    await waitForElement('price', 5000);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectIdToHaveText('txtPromotionCode', 'abc123');
    await waitForElement('originPrice', 5000);
    await waitForElement('price', 5000);
    await tapText('Đăng việc');
    await tapText('Đồng ý');

    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');

    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');

    try {
      await expectElementVisible('Giữ Tasker hiện tại', 'text');
      await tapText('Giữ Tasker hiện tại');
    } catch (error) {}
    await selectTime24h(17);
    await tapText('Đồng ý');
    try {
      await tapId('btnUpdateDateTime');
      await expectElementVisible('Đồng ý', 'text');
    } catch (error) {
      //xảy ra khi test run vào lúc < 5h hoặc > 23h
      await expectElementVisible('contentWarningConflictTime');
      await tapId('changeTaskerBtn');
      await tapText('Đồng ý');
      await tapText('Đóng');
    }
  });

  it('LINE 172 - Asker want to change deep cleaning thailand task time include promotion 100m2 720k', async () => {
    const PROMOTION = {
      isoCode: 'TH',
      code: 'abc123',
      value: 50,
      target: 'ASKER',
      typeOfPromotion: 'BOTH',
      typeOfValue: 'MONEY',
      limit: 100,
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);
    await postTask('postTaskServiceDEEP_CLEANING', '500 Thanon Tanao');
    await tapId('area150');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');

    await expectElementVisible('promotionCode');
    await expectElementVisible('price');
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectIdToHaveText('txtPromotionCode', 'abc123');
    await expectElementVisible('originPrice');
    await expectElementVisible('price');

    await tapText('Đăng việc');
    await tapText('Đồng ý');

    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');

    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');

    const nextDay = moment().add(3, 'd');
    if (moment().add(1, 'd').date() !== 1 && moment().month() !== nextDay.month()) {
    }
    const nextDayString = nextDay.date() < 9 ? `0${String(nextDay.date())}` : String(nextDay.date());
    await tapTextAtIndex(nextDayString, 0);
    await selectTime24h(16);
    await tapText('Đồng ý');

    await expectElementVisible('lbPrice');

    await tapId('btnUpdateDateTime');
    await checkTimeChangeSuccess();
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
  });
});
