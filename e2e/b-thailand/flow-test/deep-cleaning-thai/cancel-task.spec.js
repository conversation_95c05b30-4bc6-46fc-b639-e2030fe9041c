/**
 * @description Asker cancel Deep Cleaning task thailand
 *   case 1: <PERSON><PERSON> cancel posted task
 *   case 2: Ask<PERSON> cancel posted task (Max fee
 *   case 3: Ask<PERSON> cancel confirmed deep cleaning task before working time (max fee
 *   case 4: Ask<PERSON> cancel confirmed deep cleaning task with fee 20k
 *   case 5: Ask<PERSON> cancel waiting deep cleaning task with fee 0k
 *   case 6: Asker cancel posted task with free charge
 *   case 7: Ask<PERSON> cancel posted task
 * */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  waitForElement,
  swipe,
  expectIdToHaveText,
} = require('../../../step-definition');
const expect = require('chai').expect;
const moment = require('moment');

describe('FILE: e2e/b-thailand/flow-test/deep-cleaning-thai/cancel-task.spec.js - Asker cancel Deep Cleaning task thailand', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        isoCode: 'TH',
        phone: '**********',
        name: '<PERSON><PERSON>',
        type: 'ASKER',
        status: 'ACTIVE',
        oldUser: true,
        FMainAccount: 20000,
      },
      {
        isoCode: 'TH',
        phone: '**********',
        name: 'Tasker',
        type: 'TASKER',
        status: 'ACTIVE',
        oldUser: true,
        FMainAccount: 20000,
      },
    ]);

    await initData('task/createTask', [
      {
        isoCode: 'TH',
        serviceName: 'DEEP_CLEANING',
        askerPhone: '**********',
        description: 'My Task 01',
      },
      {
        isoCode: 'TH',
        serviceName: 'DEEP_CLEANING',
        askerPhone: '**********',
        description: 'My Task 02',
      },
      {
        isoCode: 'TH',
        serviceName: 'DEEP_CLEANING',
        askerPhone: '**********',
        description: 'My Task 03',
      },
      {
        isoCode: 'TH',
        serviceName: 'DEEP_CLEANING',
        askerPhone: '**********',
        description: 'My Task 04',
      },
    ]);

    await initData('task/acceptedTask', [
      {
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: ['**********'],
        description: 'My Task 01',
        isoCode: 'TH',
      },
      { status: 'CONFIRMED', taskerAccepted: ['**********'], description: 'My Task 02', isoCode: 'TH' },
    ]);
    await E2EHelpers.onHaveLogin('**********');
  });
  const canCelTask = async ({ description, reason = 'Không cần công việc này nữa.', fee, scrollUp, rebook }) => {
    await tapText('Hoạt động');
    if (scrollUp) {
      await swipe('scrollUpcoming', 'up');
    }
    await waitForElement(description, 500);
    await tapId(description);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText(reason);
    if (rebook) {
      await tapText('Đăng lại');
    } else {
      await tapText('Đồng ý');
    }
  };

  it('LINE 73 - Asker cancel posted task', async () => {
    await canCelTask({ description: 'taskMy Task 04', scrollUp: true });
    const task = await initData('task/getTaskByDescription', { description: 'My Task 04', isoCode: 'TH' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 107 - Asker cancel posted task (Max fee)', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 02',
        isoCode: 'TH',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(1, 'hour').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 02');
    const data1 = await initData('user/findFATransaction', {
      phone: '**********',
      accountType: 'M',
      type: 'C',
      amount: 2400,
      isoCode: 'TH',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(2400);

    const data2 = await initData('user/find-faccount', { phone: '**********', isoCode: 'TH' });
    expect(data2.TH_FMainAccount).to.equal(17600);
    expect(data2.TH_Promotion).to.equal(0);
  });

  it('LINE 153 - Asker cancel confirmed deep cleaning task with fee 30 baht', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 02',
        isoCode: 'TH',
        dataUpdate: {
          createdAt: moment().subtract(8, 'hour').toDate(),
          date: moment().add(7, 'hour').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 02');
    const data1 = await initData('user/findFATransaction', {
      phone: '**********',
      accountType: 'M',
      type: 'C',
      amount: 30,
      isoCode: 'TH',
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(30);

    const data2 = await initData('user/find-faccount', { phone: '**********', isoCode: 'TH' });
    expect(data2.TH_FMainAccount).to.equal(19970);
    expect(data2.TH_Promotion).to.equal(0);
  });

  it('LINE 178 - Asker cancel waiting deep cleaning task with fee 0k', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 02',
        isoCode: 'TH',
        dataUpdate: {
          createdAt: moment().subtract(9, 'm').toDate(),
          date: moment().add(1, 'days').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
  });

  it('LINE 201 - Asker cancel posted task with free charge', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 02',
        isoCode: 'TH',
        dataUpdate: {
          createdAt: moment().subtract(1, 'months').toDate(),
          date: moment().add(1, 'months').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('TAB_UPCOMINGMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
  });

  it('LINE 222 - Asker cancel posted task', async () => {
    const PROMOTION = {
      isoCode: 'TH',
      code: 'abc123',
      value: 50,
      target: 'ASKER',
      typeOfPromotion: 'BOTH',
      typeOfValue: 'MONEY',
      limit: 100,
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);

    await initData('task/createTask', [
      {
        isoCode: 'TH',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'my task 05',
        status: 'CONFIRMED',
        acceptedTasker: ['**********'],
        date: moment().subtract(1, 'd').toDate(),
        promotion: {
          code: 'abc123',
          value: 500,
          target: 'ASKER',
          typeOfPromotion: 'CURRENT',
          typeOfValue: 'MONEY',
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('taskmy task 05');

    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('discount', '-500 ฿');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await waitForElement(
      'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
      500,
      'text',
    );
    await tapText('Tiếp tục');
  });
});
