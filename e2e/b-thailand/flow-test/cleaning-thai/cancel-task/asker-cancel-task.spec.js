const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  waitForElement,
  tapText,
  expectElementNotExist,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const moment = require('moment');

const ASKER = {
  isoCode: 'TH',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: 'TH',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASK_1 = {
  isoCode: 'TH',
  serviceName: 'CLEANING',
  askerPhone: '0834567890',
  description: 'Don dep nha 01',
};
const TASK_2 = {
  isoCode: 'TH',
  serviceName: 'CLEANING',
  askerPhone: '0834567890',
  description: 'Don dep nha 02',
};
const TASK_3 = {
  isoCode: 'TH',
  serviceName: 'CLEANING',
  askerPhone: '0834567890',
  description: 'Don dep nha 03',
};
const TASK_4 = {
  isoCode: 'TH',
  serviceName: 'CLEANING',
  askerPhone: '0834567890',
  description: 'Don dep nha 04',
};

describe('FILE: e2e/b-thailand/flow-test/cleaning-thai/cancel-task/asker-cancel-task.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK_1, TASK_2, TASK_3, TASK_4]);
    const tasker = await initData('user/get-user', { phone: TASKER.phone, isoCode: TASKER.isoCode });
    await initData('task/updateTask', [
      {
        description: TASK_1.description,
        isoCode: TASK_1.isoCode,
        dataUpdate: {
          status: 'WAITING_ASKER_CONFIRMATION',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
      {
        description: TASK_2.description,
        isoCode: TASK_2.isoCode,
        dataUpdate: {
          status: 'CONFIRMED',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 86 - Asker cancel posted task', async () => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId('TAB_UPCOMINGDon dep nha 04');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 04');
    const task = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'Don dep nha 04' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 110 - Asker cancel waiting cleaning task', async () => {
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    const task = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'Don dep nha 01' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 132 - Asker cancel confirmed cleaning task before working time', async () => {
    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: 'TH',
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: 'Don dep nha 04',
        isoCode: 'TH',
        dataUpdate: {
          visibility: 2,
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: 'TH' },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await expectElementNotExist('Tasker tự ý không đến.', 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
    await expectElementVisible('taskDon dep nha 02');
    const task = await initData('task/getTaskByDescription', {
      isoCode: TASK_2.isoCode,
      description: TASK_2.description,
    });

    expect(task.status).to.equal('POSTED');

    const notify = await initData('notification/get-notification', {
      isoCode: ASKER.isoCode,
      phone: TASKER.phone,
      type: 30,
    });
    expect(notify.length).to.equal(1);
  });

  it('LINE 178 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: TASKER.isoCode },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Bận việc đột xuất.');
    await tapText('Đồng ý');
    const data = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: TASKER.isoCode,
      taskDescription: TASK_3.description,
      type: 0,
    });
    expect(data.length).to.equal(0);

    const data1 = await initData('notification/get-notification', {
      phone: TASKER.phone,
      isoCode: TASKER.isoCode,
      taskDescription: TASK_4.description,
      type: 0,
    });
    expect(data1.length).to.equal(0);
    await expectElementNotExist('taskDon dep nha 02');
    const task = await initData('task/getTaskByDescription', {
      isoCode: TASK_2.isoCode,
      description: TASK_2.description,
    });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 205 - Asker cancel confirmed cleaning task after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: TASK_1.description,
        isoCode: TASK_1.isoCode,
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK_4.description,
        isoCode: TASK_4.isoCode,
        dataUpdate: {
          visibility: 2,
        },
      },
      {
        description: TASK_2.description,
        isoCode: TASK_2.isoCode,
        dataUpdate: {
          progress: 'AFTER_WORKING',
        },
      },
    ]);
    await initData('update-user/add-favourite-tasker', [
      { phone: ASKER.phone, favouriteTasker: [TASKER.phone], isoCode: TASKER.phone },
    ]);

    await tapText('Hoạt động');
    await tapId('taskDon dep nha 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Tasker có báo không đến được.', 500, 'text');
    await tapText('Tasker có báo không đến được.');
    await tapText('Hủy việc');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 02');
  });

  it('LINE 232 - Asker cancel confirmed cleaning task with fee 30 ฿', async () => {
    await initData('task/acceptedTask', [
      { isoCode: TASK_1.isoCode, description: TASK_1.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_1.description,
        isoCode: TASK_1.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(7, 'hour').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 01');

    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 30,
      isoCode: ASKER.isoCode,
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(30);

    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: ASKER.isoCode });
    expect(data2.TH_FMainAccount).to.equal(-30);
    expect(data2.TH_Promotion).to.equal(0);
  });

  it('LINE 373 - Asker cancel cleaning task before task before 2 - 6 hour fee 200 ฿', async () => {
    await initData('task/acceptedTask', [
      { isoCode: TASK_1.isoCode, description: TASK_1.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_1.description,
        isoCode: TASK_1.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(3, 'hour').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 01');

    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 200,
      isoCode: ASKER.isoCode,
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(200);

    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: ASKER.isoCode });
    expect(data2.TH_FMainAccount).to.equal(-200);
    expect(data2.TH_Promotion).to.equal(0);
  });

  it('LINE 402 - Asker cancel cleaning task before task begining less than 1 hour (Max fee)', async () => {
    await initData('task/acceptedTask', [
      { isoCode: TASK_1.isoCode, description: TASK_1.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK_1.description,
        isoCode: TASK_1.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(1, 'hour').toDate(),
        },
      },
    ]);

    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // await tapText('Giữ Tasker hiện tại');
    // await swipe('updatePage', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskDon dep nha 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskDon dep nha 01');

    const data1 = await initData('user/findFATransaction', {
      phone: ASKER.phone,
      accountType: 'M',
      type: 'C',
      amount: 528,
      isoCode: ASKER.isoCode,
    });
    expect(data1.length).to.equal(1);
    expect(data1[0].amount).to.equal(528);

    const data2 = await initData('user/find-faccount', { phone: ASKER.phone, isoCode: ASKER.isoCode });
    expect(data2.TH_FMainAccount).to.equal(-528);
    expect(data2.TH_Promotion).to.equal(0);
  });
});
