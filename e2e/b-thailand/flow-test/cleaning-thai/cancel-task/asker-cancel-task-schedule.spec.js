/**
 * @description Cancel task schedule ThaiLand (1 test cases)
 *   case 1: LINE 26 - Post task with cat
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  expectElementVisible,
  waitForElement,
  tapHeaderBack,
  swipe,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 1000000,
};
const TASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

describe('FILE: e2e/b-thailand/flow-test/cleaning-thai/cancel-task/asker-cancel-task-schedule.spec.js - Cancel task schedule', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('LINE 47 - Post task and cancel schedule task', async () => {
    await postTask('postTaskServiceCLEANING');
    await tapId('chooseDuration-2');
    await tapId('btnNextStep2');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek3');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Đồng ý'); //warning khi post task schedule
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
    await tapHeaderBack();
    await tapId('Tab_Schedule');
    await expectElementVisible('Đang hoạt động', 'text');
    await tapId('Tab_Upcoming');

    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementVisible('Bạn có muốn dừng lịch lặp lại ?', 'text');
    await tapText('Đồng ý');
    await tapId('Tab_Schedule');
    await expectElementVisible('Tạm dừng', 'text');
  });
});
