/**
 * @description Asker cancel task with promotion ThaiLand
 *   case 1: Asker cancel the task is not include fee
 *   case 2: Asker cancel the task include fee
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  expectIdToHaveText,
  waitForElement,
  postTask,
  tapText,
  typePromotionCode,
} = require('../../../../step-definition');
const moment = require('moment');

const F_MAIN_ACCOUNT = 1000000;

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: F_MAIN_ACCOUNT,
};
const TASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASK = {
  removeRequirements: true,
  isoCode: 'TH',
  serviceName: 'CLEANING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 01',
};

const PROMOTION_01 = {
  isoCode: 'TH',
  code: 'def123',
  value: 100,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 1,
  maxValue: '',
};

describe('FILE: e2e/b-thailand/flow-test/cleaning-thai/cancel-task/asker-cancel-task-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK]);
    await initData('promotion/create-promotion-code', PROMOTION_01);

    await E2EHelpers.onHaveLogin(ASKER.phone);
  });

  it('LINE 46 - Asker cancel the task is not include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('discount');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    // await tapText('Đồng ý'); //Confirm chọn lý do
    await tapText('Đồng ý'); // Xác nhận phí hủy
    await tapText('Tiếp tục'); //Xác nhận tiếp tục hủy khi có promotion
    //Book lại task mới
    await waitForElement('postTaskNow', 1000);
    await tapId('postTaskNow');
    await postTask('postTaskServiceCLEANING');
    await tapText('Đặt công việc mới');
    await tapId('chooseDuration-3');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION_01.code);
    await expectElementVisible('originPrice');
    await expectElementVisible('price');
    await tapText('Đăng việc');
    await tapText('Đồng ý');
    await tapText('Theo dõi công việc');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('discount');
  });

  it('LINE 115 - Asker cancel the task thai include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);
    await initData('task/acceptedTask', [
      { isoCode: TASK.isoCode, description: TASK.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          status: 'CONFIRMED',
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('discount');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await waitForElement('Không cần công việc này nữa.', 500, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý'); // Xác nhận phí hủy
    await tapText('Tiếp tục'); //Xác nhận tiếp tục hủy khi có promotion

    //Book lại task mới
    await waitForElement('postTaskNow', 1000);
    await tapId('postTaskNow');
    await postTask('postTaskServiceCLEANING');
    await tapText('Đặt công việc mới');
    await tapId('chooseDuration-3');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(PROMOTION_01.code);
    await waitForElement('Mã ưu đãi này đã hết lượt sử dụng. Vui lòng chọn mã ưu đãi khác.', 500, 'text');
    await tapText('Đóng');
  });
});
