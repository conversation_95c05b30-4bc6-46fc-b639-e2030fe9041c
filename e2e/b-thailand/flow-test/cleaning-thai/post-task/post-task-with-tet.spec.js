const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  postTask,
  expectElementVisible,
  tapHeaderBack,
  tapTextAtIndex,
  expectElementNotVisible,
  waitForLoading,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 20000,
};

describe('FILE: e2e/b-thailand/flow-test/cleaning-thai/post-task/post-task-with-tet.spec.js - Post task Tet service', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('event-config/create-data', [{ isoCode: 'TH', services: ['CLEANING'] }]);
    await initData('service/update-fields', {
      isoCode: 'TH',
      serviceName: 'CLEANING',
      dataUpdate: {
        tetBookingDates: {
          minVersion: '1.0.0',
          bookTaskTime: {
            fromDate: moment().add(2, 'days').toDate(),
            toDate: moment().add(32, 'days').toDate(),
          },
          fromDate: moment().subtract(1, 'days').toDate(),
          toDate: moment().add(60, 'days').toDate(),
        },
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+66');
  });

  it('LINE 49 - Asker change date tet booking', async () => {
    await postTask('postTaskServiceTetCLEANING');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');
    await tapId('btnNextStep2');
    await tapId('datePickerTet');

    const nextMonday = moment().add(10, 'days').date();
    await waitForLoading(500);
    try {
      await tapTextAtIndex(nextMonday.toString(), 0);
    } catch (error) {
      await tapTextAtIndex(nextMonday.toString(), 1);
    }
    await tapText('Đồng ý');

    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await expectElementNotVisible('Tiền mặt', 'text');
    await tapHeaderBack();
    await tapText('Đăng việc');
    await tapText('Đồng ý');
    await tapText('Theo dõi công việc');

    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('paymentMethod', 'bPay');
  });
});
