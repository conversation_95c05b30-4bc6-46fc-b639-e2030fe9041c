const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  postTask,
  expectElementVisible,
  tapTextAtIndex,
  waitForLoading,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 2000000,
};

describe('FILE: e2e/b-thailand/flow-test/air-conditioner-thai/post-task/post-task-with-tet.spec.js - Post task Tet service', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('event-config/create-data', [{ isoCode: 'TH', services: ['AIR_CONDITIONER_SERVICE'] }]);
    await initData('service/update-fields', {
      isoCode: 'TH',
      serviceName: 'AIR_CONDITIONER_SERVICE',
      dataUpdate: {
        tetBookingDates: {
          minVersion: '1.0.0',
          bookTaskTime: {
            fromDate: moment().add(2, 'days').toDate(),
            toDate: moment().add(32, 'days').toDate(),
          },
          fromDate: moment().subtract(1, 'days').toDate(),
          toDate: moment().add(60, 'days').toDate(),
        },
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+66');
  });

  it('LINE 47 - Asker change date tet booking', async () => {
    await postTask('postTaskServiceTetAIR_CONDITIONER_SERVICE');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');

    await tapId('address1');
    await tapText('9,000 - 18,000 BTU');

    await tapId('btnNextStep2');
    await tapId('datePickerTet');

    const nextMonday = moment().add(10, 'days').date();
    await waitForLoading(500);
    try {
      await tapTextAtIndex(nextMonday.toString(), 0);
    } catch (error) {
      await tapTextAtIndex(nextMonday.toString(), 1);
    }
    await tapText('Đồng ý');

    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');

    await expectElementVisible('Treo tường', 'text');
    await expectElementVisible('9,000 - 18,000 BTU', 'text');
    await tapText('Đăng việc');
    await tapText('Đồng ý');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Vệ sinh máy lạnh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');
    await expectElementVisible('Treo tường', 'text');
    await expectElementVisible('9,000 - 18,000 BTU', 'text');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
  });
});
