/**
 * @description Asker cancel task (7 test cases)
 *   case 1: <PERSON><PERSON><PERSON> 33 - <PERSON><PERSON> cancel posted task
 *   case 2: <PERSON><PERSON><PERSON> 56 Ask<PERSON> cancel task - WAITING
 *   case 3: <PERSON><PERSON><PERSON> 77 - <PERSON><PERSON> cancel task - Confirmed task before working time
 *   case 4: <PERSON><PERSON><PERSON> 118 - Ask<PERSON> cancel confirmed cleaning task before working time, find same task for Tasker
 *   case 5: <PERSON><PERSON><PERSON> 147 - Ask<PERSON> cancel confirmed cleaning task after task began 15 minutes
 *   case 6: <PERSON><PERSON><PERSON> 178 - Ask<PERSON> cancel confirmed cleaning task with fee 20k
 *   case 7: <PERSON><PERSON><PERSON> 210 - <PERSON><PERSON> cancel waiting cleaning task with fee 0k
 *   case 8: LIN<PERSON> 235 - Ask<PERSON> cancel posted task with free charge
 *   case 9: LINE 263 - <PERSON><PERSON> cancel confirmed cleaning task with reason Tasker not comming free
 *   case 10: LINE 383 - <PERSON><PERSON> cancel cleaning task before task begining 2 hours
 * */
const {
  initData,
  tapId,
  swipe,
  expectElementVisible,
  waitForElement,
  tapText,
  expectElementNotExist,
} = require('../../../step-definition');
const expect = require('chai').expect;
const moment = require('moment');
const { E2EHelpers } = require('../../../e2e.helpers');

describe('FILE: e2e/b-thailand/flow-test/air-conditioner-thai/cancel-task-thai.spec.js - <PERSON><PERSON> cancel task ThaiLand', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [
      {
        isoCode: 'TH',
        phone: '**********',
        name: 'Asker',
        type: 'ASKER',
        status: 'ACTIVE',
        oldUser: true,
        FMainAccount: 20000,
      },
      {
        isoCode: 'TH',
        phone: '**********',
        name: 'Tasker',
        type: 'TASKER',
        status: 'ACTIVE',
        oldUser: true,
        FMainAccount: 20000,
      },
    ]);

    await initData('task/createTask', [
      {
        isoCode: 'TH',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 01',
      },
      {
        isoCode: 'TH',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 02',
      },
      {
        isoCode: 'TH',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 03',
      },
      {
        isoCode: 'TH',
        serviceName: 'AIR_CONDITIONER_SERVICE',
        askerPhone: '**********',
        description: 'My Task 04',
      },
    ]);

    await initData('task/acceptedTask', [
      {
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: ['**********'],
        description: 'My Task 01',
        isoCode: 'TH',
      },
      { status: 'CONFIRMED', taskerAccepted: ['**********'], description: 'My Task 02', isoCode: 'TH' },
    ]);
    await E2EHelpers.onHaveLogin('**********');
  });

  const canCelTask = async ({ description, reason = 'Không cần công việc này nữa.', fee, scrollUp, rebook }) => {
    await tapText('Hoạt động');
    if (scrollUp) {
      await swipe('scrollUpcoming', 'up');
    }
    await waitForElement(description, 500);
    await tapId(description);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText(reason);
    if (rebook) {
      await tapText('Đăng lại');
    } else {
      await tapText('Đồng ý');
    }
  };

  it('LINE 109 - Asker cancel posted task ThaiLand ThaiLand', async () => {
    await canCelTask({ description: 'taskMy Task 04', scrollUp: true });
    await expectElementNotExist('taskMy Task 04');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 04', isoCode: 'TH' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 116 Asker cancel task ThaiLand - WAITING', async () => {
    await canCelTask({ description: 'taskMy Task 01' });
    await expectElementNotExist('taskMy Task 01');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 01', isoCode: 'TH' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 123 - Asker cancel task ThaiLand - Confirmed task before working time', async () => {
    await tapText('Hoạt động');
    await waitForElement('taskMy Task 02', 500);
    await tapId('taskMy Task 02');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Tasker có báo không đến được.');
    await tapText('Đăng lại');
  });

  it('LINE 153 - Asker cancel confirmed cleaning task ThaiLand before working time, find same task for Tasker', async () => {
    await canCelTask({ description: 'taskMy Task 02', reason: 'Bận việc đột xuất.' });
    await expectElementNotExist('taskMy Task 02');
    const task = await initData('task/getTaskByDescription', { description: 'My Task 02', isoCode: 'TH' });
    expect(task.cancellationReason).to.equal('ASKER_BUSY');
  });

  it('LINE 165 - Asker cancel confirmed cleaning task ThaiLand after task began 15 minutes', async () => {
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'TH',
        dataUpdate: {
          date: moment().add(15, 'm').toDate(),
        },
      },
    ]);
    await canCelTask({ description: 'taskMy Task 02', reason: 'Bận việc đột xuất.', fee: 0 });
    await expectElementNotExist('taskMy Task 02');
  });

  it('LINE 177 - Asker cancel confirmed cleaning task ThaiLand with fee 30THB', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'TH', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task 01',
        isoCode: 'TH',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(8, 'hour').toDate(),
        },
      },
    ]);

    await canCelTask({ description: 'taskMy Task 01', reason: 'Bận việc đột xuất.', fee: 30 });
    await expectElementNotExist('taskMy Task 01');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 01');
    const data = await initData('user/findFATransaction', {
      phone: '**********',
      accountType: 'M',
      type: 'C',
      amount: 30,
      isoCode: 'TH',
    });

    expect(data.length).to.equal(1);
    expect(data[0].amount).to.equal(30);

    const data2 = await initData('user/find-faccount', { phone: '**********', isoCode: 'TH' });
    expect(data2.TH_FMainAccount).to.equal(19970);
    expect(data2.TH_Promotion).to.equal(0);
  });

  it('LINE 206 - Asker cancel posted task ThaiLand with free charge', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'TH', description: 'My Task 04', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await canCelTask({ description: 'taskMy Task 04', reason: 'Không cần công việc này nữa.', fee: 0, scrollUp: true });
    await expectElementNotExist('taskMy Task 04');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'TH' });
    expect(data.TH_FMainAccount).to.equal(20000);
    expect(data.TH_Promotion).to.equal(0);
  });

  it('LINE 220 - Asker cancel confirmed cleaning task ThaiLand with reason Tasker not comming free', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'TH', description: 'My Task 01', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);

    await canCelTask({
      description: 'taskMy Task 01',
      reason: 'Tasker có báo không đến được.',
      fee: 0,
      rebook: true,
    });
    await expectElementVisible('taskMy Task 01');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'TH' });
    expect(data.TH_FMainAccount).to.equal(20000);
    expect(data.TH_Promotion).to.equal(0);
  });

  it('LINE 238 - Asker cancel cleaning task ThaiLand before task begining 2 hours', async () => {
    await initData('task/acceptedTask', [
      { isoCode: 'TH', description: 'My Task 02', taskerAccepted: ['**********'], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task 02',
        isoCode: 'TH',
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);

    await canCelTask({ description: 'taskMy Task 02', reason: 'Bận việc đột xuất.' });

    await expectElementNotExist('taskMy Task 02');
    await swipe('scrollUpcoming', 'up');
    await expectElementNotExist('taskMy Task 02');
    const data = await initData('user/find-faccount', { phone: '**********', isoCode: 'TH' });
    expect(data.TH_FMainAccount).to.equal(19400);
    expect(data.TH_Promotion).to.equal(0);
  });
});
