const {
  tapId,
  swipe,
  tapText,
  initData,
  callService,
  waitForElement,
  expectElementNotExist,
  loginWithPhoneAndPassword,
} = require('../../../step-definition');
const expect = require('chai').expect;

//TODO: <PERSON>ần update lại flow rồi update test
describe.skip('FILE: e2e/b-thailand/flow-test/air-conditioner-thai/cancel-task-with-true-money.spec.js - Cancel with true money AC task thai', () => {
  const description1 = 'task AC 01';
  beforeEach(async () => {
    await initData('user/createUser', [
      { Country: 'TH', Phone: '**********', Name: 'Asker', Type: 'ASKER', Status: 'ACTIVE', CountryCode: '+66' },
      { Country: 'TH', Phone: '**********', Name: 'Tasker', Type: 'TASKER', Status: 'ACTIVE', CountryCode: '+66' },
    ]);

    await initData('task/createACTask', [
      {
        ServiceName: 'Vệ sinh máy lạnh',
        AskerPhone: '**********',
        Description: description1,
        Detail: 'Split,0,2,1,clean_gas,200000',
        TaskNote: 'note ne',
        IsoCode: 'TH',
        CountryCode: '+66',
        Cost: 400,
      },
    ]);

    // Update fee cancel
    await initData('settings/changeTH_SettingSystem', {
      taskerNotCommingFee: {
        earlyCancelFee: 20, // money
        lateCancelFee: 50, // percent
        notCommingFee: 100, // percent
      },
    });
    await device.reloadReactNative();
  });

  const _cancelTask = async ({ reason, cancelResult, refundMoney, isPromotion }) => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId(`task${description1}`);
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // await tapText('Đồng ý');

    if (isPromotion) {
      await tapText('Tiếp tục');
    }

    await tapText(reason);
    await tapText('Đồng ý');
    await waitForElement(cancelResult, 500, 'text');
    await tapText('Đóng');
    await expectElementNotExist(`task${description1}`);
    const refundData = await initData('refundRequest/findRefundRequest', { Description: description1 });
    expect(refundData.data.type).to.equal('PREPAY_TASK');
    expect(refundData.data.amount).to.equal(refundMoney);
    expect(refundData.data.paymentMethod).to.equal('TRUE_MONEY');
    expect(refundData.data.currency).to.equal('THB');
    expect(refundData.data.status).to.equal('NEW');
  };

  it('LINE 70 - Asker cancel task true money with reason tasker not come', async () => {
    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '**********',
        Description: description1,
        Progress: 'AFTER_WORKING',
        PaymentMethod: 'TRUE_MONEY',
      },
    ]);

    await loginWithPhoneAndPassword('**********', '123456', '+66');
    await _cancelTask({
      reason: 'Tasker tự ý không đến.',
      cancelResult: 'Công việc đã được hủy. Phí: 0 ฿. Số tiền 400 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 400,
    });

    const data = await initData('faccount/findTaskerFAccount', { taskerPhone: '**********', countryCode: '+66' });
    expect(data.TH_FMainAccount).to.equal(-400);
  });

  it('LINE 92 - Asker cancel task true money before working 6h', async () => {
    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '**********',
        Description: description1,
        Progress: 'BEFORE_WORKING_8H',
        PaymentMethod: 'TRUE_MONEY',
      },
    ]);

    await loginWithPhoneAndPassword('**********', '123456', '+66');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 20 ฿. Số tiền 380 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 380,
    });
  });

  it('LINE 111 - Asker cancel task true money before working 1h', async () => {
    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '**********',
        Description: description1,
        Progress: 'BEFORE_WORKING_50M',
        PaymentMethod: 'TRUE_MONEY',
        CreatedAt: 'LATE',
      },
    ]);

    await loginWithPhoneAndPassword('**********', '123456', '+66');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 120 ฿. Số tiền 280 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 280,
    });
  });

  it('LINE 131 - Asker cancel task true money before working 2h', async () => {
    // fee cancel base on field cancelTaskFee = 30
    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '**********',
        Description: description1,
        Progress: 'BEFORE_WORKING_2H',
        PaymentMethod: 'TRUE_MONEY',
        CreatedAt: 'LATE',
      },
    ]);

    await loginWithPhoneAndPassword('**********', '123456', '+66');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 30 ฿. Số tiền 370 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 370,
    });
  });

  it('LINE 152 - Asker cancel task true money with promotion code', async () => {
    await initData('promotion/createPromotionCode', [
      {
        Code: 'abc123',
        Value: 50,
        Target: 'ASKER',
        TypeOfPromotion: 'NEW',
        TypeOfValue: 'MONEY',
        Limit: 100,
        MaxValue: '',
      },
    ]);

    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '**********',
        Description: description1,
        PaymentMethod: 'TRUE_MONEY',
        PromotionCode: 'abc123',
      },
    ]);

    await loginWithPhoneAndPassword('**********', '123456', '+66');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 20 ฿. Số tiền 330 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 330,
      isPromotion: true,
    });
  });

  it('LINE 184 - Asker cancel task with no tasker accepted', async () => {
    await initData('task/updateTask', [
      {
        Description: description1,
        PaymentMethod: 'TRUE_MONEY',
      },
    ]);

    await loginWithPhoneAndPassword('**********', '123456', '+66');
    await _cancelTask({
      reason: 'Bận việc đột xuất.',
      cancelResult: 'Công việc đã được hủy. Phí: 20 ฿. Số tiền 380 ฿ sẽ được hoàn lại trong vòng 5 ngày.',
      refundMoney: 380,
    });
  });

  it('LINE 200 - Tasker cancel booking', async () => {
    await loginWithPhoneAndPassword('**********', '123456', '+66');

    await initData('task/updateTask', [
      {
        Status: 'CONFIRMED',
        AcceptedTasker: '**********',
        Description: description1,
        Progress: 'BEFORE_WORKING_50M',
        PaymentMethod: 'TRUE_MONEY',
        CreatedAt: 'LATE',
      },
    ]);

    const task = await initData('task/getTaskByDescription', { description: description1 });
    const tasker = await initData('user/getUserByPhone', { phone: '**********', countryCode: '+66' });
    await callService('/v2/cancel-booking/tasker-cancel-task', {
      taskId: task._id,
      userId: tasker._id,
      reason: 'OTHER_REASON',
      otherReason: 'test cancel',
    });

    const refundData = await initData('refundRequest/findRefundRequest', { Description: description1 });
    expect(refundData.data.type).to.equal('PREPAY_TASK');
    expect(refundData.data.amount).to.equal(400);
    expect(refundData.data.paymentMethod).to.equal('TRUE_MONEY');
    expect(refundData.data.currency).to.equal('THB');
    expect(refundData.data.status).to.equal('NEW');
  });
});
