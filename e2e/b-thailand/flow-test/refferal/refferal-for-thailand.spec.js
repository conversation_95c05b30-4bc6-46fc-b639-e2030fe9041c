/**
 * @description Asker done task home cooking with referral promotion
 *   case 1: 1st task of <PERSON><PERSON> done, the both askers will receive 50k in credit account for cooking
 *   case 2: 2nd task of <PERSON><PERSON> done, the both askers will not receive more for cooking
 * */

const {
  initData,
  tapId,
  tapText,
  logout,
  tapHeaderBack,
  callService,
  signUpWithModal,
  expectElementNotExist,
  expectElementVisible,
} = require('../../../step-definition');
const { device } = require('detox');
const { E2EHelpers } = require('../../../e2e.helpers');
const moment = require('moment');

const ASKER_01 = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  referralCode: 'abcqwe1231',
};
const ASKER_02 = {
  isoCode: 'TH',
  phone: '**********',
  name: '<PERSON><PERSON> 02',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const createTaskDone = async (description) => {
  await initData('task/createTask', [
    {
      isoCode: 'TH',
      serviceName: 'CLEANING',
      askerPhone: ASKER_02.phone,
      description,
      status: 'CONFIRMED',
      taskerAccepted: [TASKER.phone],
    },
  ]);
  await initData('task/updateTask', [
    {
      isoCode: 'TH',
      description,
      dataUpdate: {
        date: moment().add(-2, 'day').toDate(),
        createdAt: moment().add(-3, 'day').toDate(),
      },
    },
  ]);
  const task = await initData('task/getTaskByDescription', { isoCode: 'TH', description });
  const tasker = await initData('user/get-user', { phone: TASKER.phone, isoCode: TASKER.isoCode });
  await callService('/v2/done-booking/partner-done', { taskId: task._id, userId: tasker._id });
  await device.reloadReactNative();
};
describe('FILE: e2e/b-thailand/flow-test/refferal/refferal-for-thailand.spec.js - Asker referral promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_01, TASKER]);
    await E2EHelpers.onHaveLogin(ASKER_01.phone, '123456');
  });

  it('LINE 80 - 1st task of Asker done, the both askers will receive 50k in credit account for cleaning', async () => {
    await tapText('Tài khoản');
    await tapText('Ưu đãi của tôi');
    await expectElementNotExist('newReward_0');
    await device.reloadReactNative();
    await logout();
    await tapId('homeHeaderBtnLogin');
    await signUpWithModal('Asker', ASKER_02.phone, '<EMAIL>', '+66', ASKER_01.referralCode);
    await createTaskDone('Don dep 1');
    await tapId('star5');
    await tapId('btnRating');
    await tapText('Để sau');
    await tapText('Tài khoản');
    await tapText('Ưu đãi của tôi');
    await expectElementVisible('newReward_0');
    await device.reloadReactNative();
    await logout();
    await E2EHelpers.onHaveLogin(ASKER_01.phone, '123456');
    await tapText('Tài khoản');
    await tapText('Ưu đãi của tôi');
    await expectElementVisible('newReward_0');
    await expectElementVisible('newReward_1');
  });

  it('LINE 104 - 2nd task of Asker done, the both askers will not receive more for cleaning', async () => {
    await tapText('Tài khoản');
    await tapText('Ưu đãi của tôi');
    await expectElementNotExist('newReward_0');
    await E2EHelpers.onHaveLogout();
    await tapId('homeHeaderBtnLogin');
    await signUpWithModal('Asker', ASKER_02.phone, '<EMAIL>', '+66', ASKER_01.referralCode);
    await createTaskDone('Don dep 1');
    await createTaskDone('Don dep 2');
    await tapId('star5');
    await tapId('btnRating');
    await tapText('Để sau');
    await tapText('Tài khoản');
    await tapText('Ưu đãi của tôi');
    await expectElementVisible('newReward_0');
    await expectElementNotExist('newReward_1');
    await device.reloadReactNative();
    await tapId('star5');
    await tapId('btnRating');
    await tapId('btnLater');
    await E2EHelpers.onHaveLogout();
    await E2EHelpers.onHaveLogin(ASKER_01.phone, '123456');
    await tapText('Tài khoản');
    await tapText('Ưu đãi của tôi');
    await expectElementVisible('newReward_0');
    await expectElementVisible('newReward_1');
    await expectElementNotExist('newReward_2');
  });
});
