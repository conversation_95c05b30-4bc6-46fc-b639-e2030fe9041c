const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  swipe,
  postTask,
  expectElementVisible,
  tapHeaderBack,
  tapTextAtIndex,
  expectElementNotVisible,
  waitForLoading,
} = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const moment = require('moment');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 2000000,
};

describe('FILE: e2e/b-thailand/flow-test/massage/post-task/post-task-with-tet.spec.js - Post task Tet service', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('event-config/create-data', [{ isoCode: 'TH', services: ['MASSAGE'] }]);
    await initData('service/update-fields', {
      isoCode: 'TH',
      serviceName: 'MASSAGE',
      dataUpdate: {
        tetBookingDates: {
          minVersion: '1.0.0',
          bookTaskTime: {
            fromDate: moment().add(2, 'days').toDate(),
            toDate: moment().add(32, 'days').toDate(),
          },
          fromDate: moment().subtract(1, 'days').toDate(),
          toDate: moment().add(60, 'days').toDate(),
        },
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+66');
  });

  it('LINE 49 - Asker change date tet booking', async () => {
    await postTask('postTaskServiceTetMASSAGE');
    await expectElementVisible('btnBackIntro');
    await expectElementVisible('wrapContent');
    await expectElementVisible('btnBookNow');
    await tapId('btnBookNow');

    await tapId('address1');
    await tapId('btnSingle');
    await expectElementVisible('txtNumberOfCustomer');
    await expectElementVisible('itemPackage_fullBody');
    await expectElementVisible('itemPackage_fullBodyReflexology');
    await swipe('scrollPackages', 'up');
    await expectElementVisible('itemPackage_lightMassageReflexology');
    await expectElementVisible('txtChooseFavoriteTasker');
    await tapId('itemPackage_lightMassageReflexology');
    await expectElementVisible('txt_lightMassageReflexology');
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await tapId('btn_male');
    await tapId('option_package1');

    await tapId('btnNextStep2');
    await tapId('datePickerTet');

    const nextMonday = moment().add(10, 'days').date();
    await waitForLoading(500);
    try {
      await tapTextAtIndex(nextMonday.toString(), 0);
    } catch (error) {
      await tapTextAtIndex(nextMonday.toString(), 1);
    }
    await tapText('Đồng ý');

    await tapId('btnNextStep3');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await expectElementVisible('txtTimeWorking');
    await expectElementVisible('massagePackage_1');
    await expectElementVisible('btnSubmitPostTask');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await expectElementNotVisible('Tiền mặt', 'text');
    await tapHeaderBack();
    await tapId('ckConfirmed');
    await tapId('btnSubmitPostTask');
    await tapText('Đồng ý');
    await tapText('Theo dõi công việc');

    await expectElementVisible('serviceNameMy Task');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('massage_lightMassageReflexology');
    await tapId('taskMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectElementVisible('packageMassage_lightMassageReflexology');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
  });
});
