/**
 * @description Asker book task with favorite Tasker
 *   case 1: Asker booking in chat with favorite Tasker do not have any task confirm
 *   case 2: Asker booking 1 option in chat with favorite Tasker do not have any task confirm
 *   case 3: Asker chat with favorite Tasker have conflict time
 *   case 4: Asker cancel task posted with favorite Tasker
 *   case 5: Asker cancel task confirmed with favorite Tasker
 */

const {
  initData,
  tapId,
  tapText,
  waitForLoading,
  expectElementVisible,
  callService,
  tapTextAtIndex,
  expectElementNotVisible,
  swipe,
  selectTime24h,
  waitForElement,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const expect = require('chai').expect;
const moment = require('moment');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 2000000,
};
const ASKER_02 = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASKER = {
  _id: 'tasker_**********',
  isoCode: 'TH',
  phone: '**********',
  name: 'Tasker Favorite',
  type: 'TASKER',
  status: 'ACTIVE',
  services: ['MASSAGE'],
  oldUser: true,
  FMainAccount: 2000000,
  workingPlaces: [
    {
      country: 'TH',
      city: 'Bangkok',
      district: 'Phra Nakhon',
    },
  ],
};

describe('FILE: e2e/b-thailand/flow-test/massage/post-task/book-task-with-favorite-tasker.spec.js - Asker book task with favorite Tasker', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER, ASKER_02]);
    await initData('user/updateUser', [
      {
        phone: ASKER.phone,
        dataUpdate: {
          favouriteTasker: ['tasker_**********'],
        },
        isoCode: ASKER.isoCode,
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+66');
  });

  it('LINE 81 - Asker Massage booking in chat with favorite Tasker do not have any task confirm', async () => {
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await tapId('btnBookNow');
    await tapId('btnBookBowAlert');
    await tapId('address1');
    try {
      await tapId('btnSingle');
    } catch (error) {}
    // POST TASK STEP 2
    await expectElementVisible('Tasker Favorite', 'text');
    await expectElementVisible('txtNumberOfCustomer');
    await expectElementVisible('itemPackage_fullBody');
    await tapId('itemPackage_fullBody');
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await tapId('btn_male');
    await tapId('option_package1');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Thêm khung giờ');
    await tapText('13:00');
    await tapText('10:00');
    await tapText('Xác nhận');
    await expectElementVisible('Khung giờ 1', 'text');
    await expectElementVisible('Khung giờ 2', 'text');
    await expectElementVisible('Khung giờ 3', 'text');
    await tapId('btnNextStep3');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('step4DateOptions_1');
    await expectElementVisible('step4DateOptions_2');
    await tapId('ckConfirmed');
    await tapText('Đăng việc');
    await tapText('Đồng ý');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    const task = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'My Task' });
    expect(task.forceTasker.taskerId).to.equal('tasker_**********');
    expect(task.dateOptions.length).to.equal(3);
    await expectElementVisible('Đang đợi Tasker Favorite nhận việc...', 'text');
    await tapId('TAB_UPCOMINGMy Task');
    await tapText('Gửi công việc cho Tasker khác');
    await tapText('Xác nhận');
    await tapText('Xác nhận');
    await tapText('Đóng');
    const taskAfterUpdate = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'My Task' });
    expect(taskAfterUpdate.forceTasker).to.equal(undefined);
    expect(taskAfterUpdate.dateOptions).to.equal(undefined);
  });

  it('LINE 137 - Asker booking 1 option in chat with favorite Tasker do not have any task confirm', async () => {
    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await tapId('btnBookNow');
    await tapId('btnBookBowAlert');
    await tapId('address1');
    try {
      await tapId('btnSingle');
    } catch (error) {}
    // POST TASK STEP 2
    await expectElementVisible('Tasker Favorite', 'text');
    await expectElementVisible('txtNumberOfCustomer');
    await expectElementVisible('itemPackage_fullBody');
    await tapId('itemPackage_fullBody');
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await tapId('btn_male');
    await tapId('option_package1');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Không, cảm ơn');
    await expectElementNotVisible('Khung giờ 1', 'text');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await swipe('scrollViewStep4', 'up');
    await tapId('ckConfirmed');
    await tapText('Đăng việc');
    await tapText('Đồng ý');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    const task = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'My Task' });
    expect(task.forceTasker.taskerId).to.equal('tasker_**********');
    expect(task.dateOptions).to.equal(undefined);
    await tapId('TAB_UPCOMINGMy Task');
    await tapText('Gửi công việc cho Tasker khác');
    await tapText('Xác nhận');
    await tapText('Đóng');
    const taskAfterUpdate = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'My Task' });
    expect(taskAfterUpdate.forceTasker).to.equal(undefined);
    expect(taskAfterUpdate.dateOptions).to.equal(undefined);
  });

  it('LINE 181 - Asker chat with favorite Tasker have conflict time', async () => {
    await initData('task/createTask', [
      {
        isoCode: 'TH',
        serviceName: 'MASSAGE',
        askerPhone: ASKER_02.phone,
        description: 'My Task 01',
        status: 'POSTED',
        visibility: 3,
        viewedTaskers: [TASKER.phone],
        autoChooseTasker: true,
      },
    ]);
    const task = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'My Task 01' });
    const tasker = await initData('user/getUserByPhone', { phone: TASKER.phone, countryCode: '+66' });
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
    };
    const response = await callService('/v3/accept-task-th/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await waitForLoading('Tài khoản', 500, 'text');
    await tapText('Tài khoản');
    await tapText('Tasker yêu thích');
    await expectElementVisible('btnFavoriteTasker_0');
    await tapId('btnFavoriteTasker_0');
    await tapId('btnBookNow');
    await tapId('btnBookBowAlert');
    await tapId('address1');
    try {
      await tapId('btnSingle');
    } catch (error) {}
    // POST TASK STEP 2
    await expectElementVisible('Tasker Favorite', 'text');
    await expectElementVisible('txtNumberOfCustomer');
    await expectElementVisible('itemPackage_fullBody');
    await tapId('itemPackage_fullBody');
    await expectElementVisible('btn_male');
    await expectElementVisible('btn_female');
    await tapId('btn_male');
    await tapId('option_package1');
    await tapId('btnNextStep2');
    await tapId('weekdays_2');
    await expectElementNotVisible('btnNextStep3');
    await expectElementVisible(
      'Rất tiếc! Khung giờ trên đã trùng với lịch làm việc của Tasker. Vui lòng xem lịch làm việc của Tasker bên dưới và chọn khung giờ khác.',
      'text',
    );
    await tapText('Xem lịch làm việc của Tasker');
    await tapText('08:00');
    await tapText('09:00');
    await tapText('10:00');
    await tapText('Xác nhận');
    await expectElementVisible('Khung giờ 1', 'text');
    await expectElementVisible('Khung giờ 2', 'text');
    await expectElementVisible('Khung giờ 3', 'text');
    await tapId('btnNextStep3');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await swipe('scrollViewStep4', 'up');
    await tapId('ckConfirmed');
    await tapText('Đăng việc');
    await tapText('Đồng ý');
    await tapText('Tiếp tục');
    await tapText('Theo dõi công việc');
    const task2 = await initData('task/getTaskByDescription', { isoCode: 'TH', description: 'My Task' });
    expect(task2.forceTasker.taskerId).to.equal('tasker_**********');
    expect(task2.dateOptions.length).to.equal(3);
  });

  it('LINE 251 - Asker booking in chat with favorite Tasker 6h', async () => {
    const dateUpdate = moment().add(2, 'hour').toDate();
    const now = moment().toDate();

    // Test chọn cận giờ nên sẽ bị fail vào buổi tối hoặc trước 14h
    if (now.getHours() > 14 && now.getHours() < 17) {
      await waitForLoading('Tài khoản', 500, 'text');
      await tapText('Tài khoản');
      await tapText('Tasker yêu thích');
      await expectElementVisible('btnFavoriteTasker_0');
      await tapId('btnFavoriteTasker_0');
      await tapId('btnBookNow');
      await tapId('btnBookBowAlert');
      await tapId('address1');
      try {
        await tapId('btnSingle');
      } catch (error) {}
      // POST TASK STEP 2
      await expectElementVisible('Tasker Favorite', 'text');
      await expectElementVisible('txtNumberOfCustomer');
      await expectElementVisible('itemPackage_fullBody');
      await tapId('itemPackage_fullBody');
      await expectElementVisible('btn_male');
      await expectElementVisible('btn_female');
      await tapId('btn_male');
      await tapId('option_package1');

      await tapId('btnNextStep2');
      await tapId('weekdays_0');

      await selectTime24h(dateUpdate.getHours(), now.getHours());
      await tapText('Đồng ý');
      await tapId('btnNextStep3');
      await expectElementVisible(
        'Bạn cần đặt trước giờ làm tối thiểu 6 tiếng để Tasker Yêu Thích có đủ thời gian nhận việc. Vui lòng chọn các khung giờ khác.',
        'text',
      );
    }
  });
});
