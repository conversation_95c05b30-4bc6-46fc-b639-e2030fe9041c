const { expectElementVisible } = require('../../../../step-definition');
const { E2EHel<PERSON> } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  waitForElement,
  postTask,
  typePromotionCode,
} = require('../../../../step-definition');

const ASKER = {
  isoCode: 'TH',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  FMainAccount: 5000,
};
const ASKER1 = {
  isoCode: 'TH',
  phone: '**********',
  name: '<PERSON>er 01',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
describe('FILE: e2e/b-thailand/flow-test/massage/post-task/post-task-promotion.spec.js - Asker post task with promotion code', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, ASKER1]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: 'TH',
        code: 'abc123',
        value: 500,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: '',
      },
      {
        isoCode: 'TH',
        code: 'def123',
        value: 500,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: 300,
      },
      {
        isoCode: 'TH',
        code: 'ghk123',
        value: 500,
        target: 'TASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: '',
      },
      {
        isoCode: 'TH',
        code: 'lmn123',
        value: 500,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'MONEY',
        limit: 1,
        maxValue: '',
      },
      {
        isoCode: 'TH',
        code: 'opq123',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
        maxValue: '',
      },
      {
        isoCode: 'TH',
        code: 'opq789',
        value: 0.4,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
        maxValue: 200,
      },
      {
        isoCode: 'TH',
        code: 'opq799',
        value: 1,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'PERCENTAGE',
        limit: 100,
        maxValue: 3000,
      },
    ]);
    await initData('promotion/usersAppliedPromotion', { isoCode: 'TH', phone: ASKER1.phone, promotionCode: 'lmn123' });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+66');
  });

  const _goPostTaskWithPromotion = async (promotionCode, originPrice, price, discount = '500') => {
    await postTask('postTaskServiceMASSAGE', '500 Thanon Tanao', 'My Task');
    try {
      await tapId('btnSingle');
    } catch (error) {}
    // POST TASK STEP 2
    await waitForElement('itemPackage_fullBody', 1000);
    await tapId('itemPackage_fullBody');
    await tapId('option_package1');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnChooseAskerGenderPackage1_male');
    await tapId('btnConfirmChooseAskerGender');
    await swipe('scrollViewStep4', 'up');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(promotionCode);
    await expectIdToHaveText('originPrice', `${originPrice} THB`);
    await expectIdToHaveText('price', `${price} THB`);

    await swipe('scrollViewStep4', 'up');
    await tapId('ckConfirmed');
    await tapText('Đăng việc');
    await tapText('Đồng ý');
    await tapText('Theo dõi công việc');

    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('cost', `${originPrice} ฿`);
    await expectIdToHaveText('discount', `-${discount} ฿`);
    await expectIdToHaveText('finalCost', `${price} ฿`);
  };

  it('LINE 148 - Asker post task Massage with promotion code money "def123"', async () => {
    await _goPostTaskWithPromotion('def123', '640', '140');
  });

  it('LINE 152 - Asker post task Massage with promotion code percentage "opq123"', async () => {
    await _goPostTaskWithPromotion('opq123', '640', '384', '256');
  });

  it('LINE 156 - Asker post task Massage with promotion code percentage "opq789" and max value is 300฿', async () => {
    await _goPostTaskWithPromotion('opq789', '640', '440', '200');
  });

  it('LINE 342 - Asker post task with promotion code percentage "opq799" and max value 100%', async () => {
    await _goPostTaskWithPromotion('opq799', '640', '0', '640');
  });
});
