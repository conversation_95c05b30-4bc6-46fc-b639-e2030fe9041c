const ISO_CODE = {
  VN: 'VN',
  TH: 'TH',
  ID: 'ID',
  MY: 'MY',
};

const COUNTRY_CODE = {
  VN: '+84',
  TH: '+66',
  ID: '+62',
  MY: '+60',
};

const ASKER_INDO = {
  isoCode: ISO_CODE.ID,
  phone: '**********',
  name: '<PERSON>er',
  type: 'ASKER',
  status: 'ACTIVE',
  email: '<EMAIL>',
  FMainAccount: 1000000,
  oldUser: true,
};

const ASKER_MALAYSIA = {
  isoCode: ISO_CODE.MY,
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  FMainAccount: 1000000,
  email: '<EMAIL>',
  oldUser: true,
  countryCode: COUNTRY_CODE.MY,
};

const ASKER_MALAYSIA_02 = {
  isoCode: ISO_CODE.MY,
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  FMainAccount: 1000000,
  email: '<EMAIL>',
  oldUser: true,
  countryCode: COUNTRY_CODE.MY,
};

const TASKER_MALAYSIA = {
  isoCode: ISO_CODE.MY,
  phone: '**********',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  FMainAccount: 1000000,
  oldUser: true,
  countryCode: COUNTRY_CODE.MY,
};

const TASKER_MALAYSIA_02 = {
  isoCode: ISO_CODE.MY,
  phone: '**********',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  FMainAccount: 1000000,
  oldUser: true,
  countryCode: COUNTRY_CODE.MY,
};

const DEFAULT_DESCRIPTION = 'My Task';

const SERVICE_NAME = {
  CLEANING: 'CLEANING',
  CLEANING_SUBSCRIPTION: 'CLEANING_SUBSCRIPTION',
  AIR_CONDITIONER: 'AIR_CONDITIONER_SERVICE',
  LAUNDRY: 'LAUNDRY',
  DEEP_CLEANING: 'DEEP_CLEANING',
  HOME_COOKING: 'HOME_COOKING',
  HOUSE_KEEPING: 'HOUSE_KEEPING',
  GROCERY_ASSISTANT: 'GO_MARKET',
  UPHOLSTERY: 'UPHOLSTERY_SERVICE',
  DISINFECTION_SERVICE: 'DISINFECTION_SERVICE',
  SOFA: 'SofaCleaning',
  ELDERLY_CARE: 'ELDERLY_CARE',
  PATIENT_CARE: 'PATIENT_CARE',
  GROCERY_ASSISTANT_OLD: 'GO_MARKET_OLD',
  ELDERLY_CARE_SUBSCRIPTION: 'ELDERLY_CARE_SUBSCRIPTION',
  PATIENT_CARE_SUBSCRIPTION: 'PATIENT_CARE_SUBSCRIPTION',
  CHILD_CARE: 'CHILD_CARE',
  CHILD_CARE_SUBSCRIPTION: 'CHILD_CARE_SUBSCRIPTION',
  OFFICE_CLEANING: 'OFFICE_CLEANING',
  OFFICE_CLEANING_SUBSCRIPTION: 'OFFICE_CLEANING_SUBSCRIPTION',
  WASHING_MACHINE: 'WASHING_MACHINE',
  OFFICE_CARPET_CLEANING: 'OFFICE_CARPET_CLEANING',
  HOME_MOVING: 'HOME_MOVING',
  MASSAGE: 'MASSAGE',
  WATER_HEATER: 'WATER_HEATER',
  INDUSTRIAL_CLEANING: 'INDUSTRIAL_CLEANING',
  MAKEUP: 'MAKEUP',
  HAIR_STYLING: 'HAIR_STYLING',
  NAIL: 'NAIL',
};

const STATUS_TASK = {
  POSTED: 'POSTED',
  CANCELED: 'CANCELED',
  DONE: 'DONE',
  EXPIRED: 'EXPIRED',
  WAITING_ASKER_CONFIRMATION: 'WAITING_ASKER_CONFIRMATION',
  CONFIRMED: 'CONFIRMED',
};

const CURRENCY = {
  VN: {
    sign: '₫',
    code: 'VND',
  },
  TH: {
    sign: '฿',
    code: 'THB',
  },
  ID: {
    sign: 'Rp',
    code: 'IDR',
  },
  MY: {
    sign: 'RM',
    code: 'MYR',
  },
};

module.exports = {
  ISO_CODE,
  SERVICE_NAME,
  STATUS_TASK,
  DEFAULT_DESCRIPTION,
  CURRENCY,
  COUNTRY_CODE,
  ASKER_INDO,
  ASKER_MALAYSIA,
  ASKER_MALAYSIA_02,
  TASKER_MALAYSIA,
  TASKER_MALAYSIA_02,
};
