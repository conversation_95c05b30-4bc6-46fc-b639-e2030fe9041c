const {
  initData,
  waitForElement,
  tapId,
  expectIdToHaveText,
  swipe,
  expectElementVisible,
  tapText,
  expectElementNotVisible,
  selectTime24h,
  callService,
} = require('../../step-definition');
const { device } = require('detox');
const moment = require('moment');
const _ = require('lodash');
const { E2EHelpers } = require('../../e2e.helpers');
const {
  CURRENCY,
  COUNTRY_CODE,
  SERVICE_NAME,
  STATUS_TASK: { WAITING_ASKER_CONFIRMATION },
} = require('../constants');

const ASKER = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER_01 = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  taskDone: 100,
  avgRating: 5.0,
  FMainAccount: ********,
  oldUser: true,
  gender: 'FEMALE',
};

const TASKER_02 = {
  isoCode: 'VN',
  phone: '**********',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  taskDone: 100,
  avgRating: 4.8,
  FMainAccount: ********,
  oldUser: true,
  gender: 'MALE',
};

const TASK = {
  _id: 'task01',
  isoCode: 'VN',
  serviceName: 'CLEANING',
  askerPhone: ASKER.phone,
  description: 'My Task',
  date: moment().add(3, 'days').set('hour', 14).startOf('hour').toDate(),
  viewedTaskers: [TASKER_01.phone, TASKER_02.phone],
};

const TASK_02 = {
  _id: 'task02',
  isoCode: 'VN',
  serviceName: 'CLEANING',
  askerPhone: ASKER.phone,
  description: 'Don dep nha 02',
  date: moment().add(2, 'days').set('hour', 14).startOf('hour').toDate(),
  viewedTaskers: [TASKER_01.phone, TASKER_02.phone],
};

class UpdateDateTimeHelpers {
  static acceptedTask = async ({ dataTaskAccepted, taskerPhone, packageId, isCompany }) => {
    const { isoCode, serviceName, taskId } = dataTaskAccepted;
    await initData('service/updateServiceChannel', [{ isoCode, serviceName, taskerPhone: taskerPhone }]);
    const tasker = await initData('user/getUserByPhone', { phone: taskerPhone, countryCode: COUNTRY_CODE[isoCode] });
    const paramAccept = {
      taskId: taskId,
      taskerId: tasker._id,
      appVersion: '4.0.0',
    };
    if (isCompany) {
      paramAccept.companyId = tasker._id;
    }
    let urlUpdateTask = '/v3/accept-task-vn/accept';
    if (isoCode === 'TH') {
      urlUpdateTask = '/v3/accept-task-th/accept';
      paramAccept.companyId = tasker._id;
    }
    if (isoCode === 'ID') {
      urlUpdateTask = '/v3/accept-task-indo/accept';
      paramAccept.companyId = tasker._id;
    }
    if (serviceName === SERVICE_NAME.CHILD_CARE) {
      paramAccept.appVersion = '4.0.0';
    }
    if (serviceName === SERVICE_NAME.OFFICE_CARPET_CLEANING) {
      urlUpdateTask = '/v3/accept-task-vn/accept';
      paramAccept.companyId = tasker._id;
    }
    if (serviceName === SERVICE_NAME.MASSAGE) {
      paramAccept.packageId = packageId;
      paramAccept.appVersion = '4.0.0';
    }
    await callService(urlUpdateTask, paramAccept);
  };

  static initDataTest = async ({ isoCode, serviceName, isTaskLeader, isCompany, FMainAccount }) => {
    TASK.serviceName = serviceName;
    TASK.isoCode = isoCode;
    TASK_02.serviceName = serviceName;
    TASK_02.isoCode = isoCode;

    TASKER_01.isoCode = isoCode;
    TASKER_02.isoCode = isoCode;
    ASKER.isoCode = isoCode;
    ASKER.FMainAccount = FMainAccount;

    // Nếu là dịch vụ tổng vệ sinh thì phải có ít nhất 1 tasker Nam
    if (serviceName === SERVICE_NAME.DEEP_CLEANING) {
      TASKER_02.gender = 'MALE';
    }

    await initData('user/createUser', [ASKER, TASKER_01, TASKER_02]);
    await initData('hostel/initHostel', { isoCode, phone: ASKER.phone });
    await initData('task/createTask', [TASK, TASK_02]);

    const dataTaskAccepted = {
      serviceName,
      isoCode,
    };

    if (isTaskLeader) {
      const params1 = {
        dataTaskAccepted: { ...dataTaskAccepted, taskId: TASK._id },
        taskerPhone: TASKER_02.phone,
        isCompany,
      };
      if (serviceName === SERVICE_NAME.MASSAGE) {
        params1.packageId = 'x65f409f33e3f4e66dc949937';
      }
      await this.acceptedTask(params1);
      const params2 = {
        dataTaskAccepted: { ...dataTaskAccepted, taskId: TASK_02._id },
        taskerPhone: TASKER_02.phone,
        isCompany,
      };
      if (serviceName === SERVICE_NAME.MASSAGE) {
        params2.packageId = 'x65f409f33e3f4e66dc949937';
      }
      await this.acceptedTask(params2);
    }

    const params1 = {
      dataTaskAccepted: { ...dataTaskAccepted, taskId: TASK._id },
      taskerPhone: TASKER_01.phone,
      isCompany,
    };
    if (serviceName === SERVICE_NAME.MASSAGE) {
      params1.packageId = 'x65f409f33e3f4e66dc949936';
    }
    await this.acceptedTask(params1);
    const params2 = {
      dataTaskAccepted: { ...dataTaskAccepted, taskId: TASK_02._id },
      taskerPhone: TASKER_01.phone,
      isCompany,
    };
    if (serviceName === SERVICE_NAME.MASSAGE) {
      params2.packageId = 'x65f409f33e3f4e66dc949936';
    }
    await this.acceptedTask(params2);
  };

  static changeTaskWaitingAndKeepTasker = async ({ serviceName, isoCode, isTaskLeader = false }) => {
    TASK.serviceName = serviceName;
    TASK.isoCode = isoCode;
    TASK_02.serviceName = serviceName;
    TASK_02.isoCode = isoCode;

    TASKER_01.isoCode = isoCode;
    TASKER_02.isoCode = isoCode;
    ASKER.isoCode = isoCode;

    await initData('user/createUser', [ASKER, TASKER_01, TASKER_02]);
    await initData('hostel/initHostel', { isoCode, phone: ASKER.phone });
    await initData('task/createTask', [TASK]);

    const acceptedTask = {
      isoCode,
      description: TASK.description,
      taskerAccepted: [TASKER_01.phone],
      status: WAITING_ASKER_CONFIRMATION,
    };

    if (isTaskLeader) {
      acceptedTask.taskerAccepted = [TASKER_01.phone];
      acceptedTask.leaderPhone = TASKER_01.phone;
    }

    await initData('task/acceptedTask', [acceptedTask]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', COUNTRY_CODE[isoCode]);

    await tapText('Hoạt động');
    try {
      await expectIdToHaveText('taskStatusMy Task', 'Chờ xác nhận');
    } catch (error) {
      await expectIdToHaveText('taskStatusMy Task', 'Chờ người nhận');
    }
    try {
      await tapId('taskMy Task');
    } catch (error) {
      await tapId('TAB_UPCOMING_My Task');
    }
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    // Công việc WAITING không hỗ trợ giữ lại Tasker
    await expectElementNotVisible('Giữ Tasker hiện tại', 'text');
  };

  static changeTaskConfirmedAndKeepTasker = async ({
    serviceName,
    isoCode,
    hours = 17,
    isTaskLeader = false,
    isCompany = false,
    FMainAccount,
  }) => {
    await this.initDataTest({ serviceName, isoCode, isTaskLeader, isCompany, FMainAccount });

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', COUNTRY_CODE[isoCode]);

    await tapText('Hoạt động');
    await expectIdToHaveText('taskStatusMy Task', 'Xác nhận');
    try {
      await tapId('taskMy Task');
    } catch (error) {
      await tapId('TAB_UPCOMING_My Task');
    }
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    try {
      await waitForElement('Giữ Tasker hiện tại', 500, 'text');
      await tapText('Giữ Tasker hiện tại');
    } catch (error) {}
    await selectTime24h(hours);
    await tapText('Đồng ý');
    try {
      await tapId('btnUpdateDateTime');
      await expectElementVisible('Đồng ý', 'text');
    } catch (error) {
      //xảy ra khi test run vào lúc < 5h hoặc > 23h
      await expectElementVisible('contentWarningConflictTime');
      await tapId('changeTaskerBtn');
      await tapText('Đồng ý');
      await tapText('Đóng');
    }
  };

  static changeTaskConfirmedAndKeepTaskerConflictTime = async ({
    serviceName,
    isoCode,
    isTaskLeader = false,
    isCompany = false,
  }) => {
    await this.initDataTest({ serviceName, isoCode, isTaskLeader, isCompany });

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', COUNTRY_CODE[isoCode]);

    await tapText('Hoạt động');
    await expectIdToHaveText('taskStatusMy Task', 'Xác nhận');
    try {
      await tapId('taskMy Task');
    } catch (error) {
      await tapId('TAB_UPCOMING_My Task');
    }
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    try {
      await expectElementVisible('Giữ Tasker hiện tại', 'text');
      await tapText('Giữ Tasker hiện tại');
    } catch (error) {}
    await tapId('weekdays_2');
    await expectElementVisible(
      'Thời gian làm việc bạn chọn trùng với lịch làm việc của Tasker hiện tại, vui lòng chọn lại thời gian hoặc thay đổi Tasker mới.',
      'text',
    );
    await tapId('changeTaskerBtn');
    await expectElementVisible('btnActionAlert1');
    await tapId('btnActionAlert1');
    try {
      await expectElementVisible('Giá dịch vụ sau thay đổi', 'text');
      await expectElementVisible(
        'Yêu cầu thay đổi giờ làm việc sẽ cần thời gian để Tasker xác nhận. Xin vui lòng đợi sau khi gửi yêu cầu.',
        'text',
      );
      await tapText('Đồng ý');
    } catch (error) {}
    try {
      await waitForElement('Cập nhật thành công', 500, 'text');
    } catch (error) {
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
    }

    await tapText('Đóng');
    try {
      await tapId('taskMy Task');
    } catch (error) {
      await tapId('TAB_UPCOMING_My Task');
    }
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
  };
  static changeTaskConfirmedAndKeepTaskerNotEnoughMoney = async ({
    serviceName,
    isoCode,
    isTaskLeader = false,
    isCompany = false,
  }) => {
    const COST_IN_COUNTRY = {
      VN: 100000,
      ID: 100000,
      TH: 100,
    };

    const taskCost = COST_IN_COUNTRY[isoCode];

    await this.initDataTest({ serviceName, isoCode, isTaskLeader, isCompany });

    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: isoCode,
        dataUpdate: {
          payment: {
            method: 'CREDIT',
          },
          cost: taskCost,
          costDetail: {
            baseCost: taskCost,
            cost: taskCost,
            finalCost: taskCost,
            duration: 4,
            currency: CURRENCY[isoCode],
            transportFee: 0,
            depositMoney: 0,
            newFinalCost: 0,
          },
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', COUNTRY_CODE[isoCode]);

    await tapText('Hoạt động');
    await expectIdToHaveText('taskStatusMy Task', 'Xác nhận');
    try {
      await tapId('taskMy Task');
    } catch (error) {
      await tapId('TAB_UPCOMING_My Task');
    }
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    try {
      await waitForElement('Giữ Tasker hiện tại', 500, 'text');
      await tapText('Giữ Tasker hiện tại');
    } catch (error) {}
    await tapId('weekdays_2');
    await expectElementVisible(
      'Thời gian làm việc bạn chọn trùng với lịch làm việc của Tasker hiện tại, vui lòng chọn lại thời gian hoặc thay đổi Tasker mới.',
      'text',
    );
    await expectElementVisible('Đổi Tasker', 'text');
    await tapText('Đổi Tasker');
    await tapText('Đồng ý');
    await waitForElement(
      'Số dư trong tài khoản bPay của bạn không đủ. Vui lòng thanh toán phần chênh lệch trên cho Tasker bằng tiền mặt.',
      500,
      'text',
    );

    await tapText('Đồng ý');

    // Navigate Chat
    await tapText('Đóng');

    try {
      await tapId('taskMy Task');
    } catch (error) {
      await tapId('TAB_UPCOMING_My Task');
    }

    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('txtCostChange', 'Trả thêm cho Tasker');
  };

  static changeTaskConfirmedAndKeepTaskerReject = async ({
    serviceName,
    isoCode,
    isTaskLeader = false,
    isCompany = false,
    FMainAccount,
  }) => {
    await this.initDataTest({ serviceName, isoCode, isTaskLeader, isCompany, FMainAccount });

    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', COUNTRY_CODE[isoCode]);

    await tapText('Hoạt động');
    await expectIdToHaveText('taskStatusMy Task', 'Xác nhận');
    try {
      await tapId('taskMy Task');
    } catch (error) {
      await tapId('TAB_UPCOMING_My Task');
    }
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('btnGoToUpdateDateTime');
    await expectElementVisible('Giữ Tasker hiện tại', 'text');
    await tapText('Giữ Tasker hiện tại');
    if (serviceName === SERVICE_NAME.HOME_COOKING) {
      await selectTime24h(17);
    } else {
      await selectTime24h(15);
    }
    await tapText('Đồng ý');
    try {
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');

      // Navigate Chat
      await waitForElement('Thông tin thay đổi giờ làm', 1000, 'text');
      await waitForElement('15:00', 1000, 'text');
      await waitForElement('Đợi xác nhận từ Tasker', 500, 'text');
      await device.reloadReactNative();
      const chatMessage = await initData('chat/get-chat-conversation', {
        taskId: TASK._id,
        isoCode: isoCode,
      });
      const request = {
        chatId: chatMessage?._id,
        messageId: chatMessage?.lastMessage?._id,
      };

      const urlByIsoCode = {
        VN: '/v3/update-task-vn/tasker-reject-update-date-time-request',
        TH: '/v3/update-task-th/tasker-reject-update-date-time-request',
        ID: '/v3/update-task-indo/tasker-reject-update-date-time-request',
      };
      await callService(urlByIsoCode[isoCode], request);
      await tapId('Tab_Activity');
      try {
        await tapId('taskMy Task');
      } catch (error) {
        await tapId('TAB_UPCOMING_My Task');
      }
      await tapText('Nhắn tin');
      await tapText('Chọn giờ khác');
      await tapId('weekdays_5');

      await waitForElement('btnUpdateDateTime', 1000);
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
    } catch (error) {
      //xảy ra khi test run vào lúc < 5h hoặc > 23h
      await expectElementVisible('contentWarningConflictTime');
      await tapId('changeTaskerBtn');
      await tapText('Đồng ý');
      await tapText('Đóng');
    }
  };
}

module.exports = { UpdateDateTimeHelpers };
