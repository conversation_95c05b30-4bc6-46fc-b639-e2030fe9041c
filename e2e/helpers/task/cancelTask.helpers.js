const {
  initData,
  waitForElement,
  tapId,
  swipe,
  expectElementVisible,
  tapText,
  expectElementNotExist,
  formatMoney,
  typeToTextField,
  scroll,
} = require('../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../e2e.helpers');
const { CURRENCY, COUNTRY_CODE } = require('../constants');

class CancelTaskHelpers {
  static checkCancelFee = async ({ description, cancelFee = 0, isoCode, askerPhone, isPrepayTask }) => {
    await expectElementNotExist(`task${description}`);
    try {
      await swipe('scrollUpcoming', 'up');
      await expectElementNotExist(`task${description}`);
    } catch (error) {}

    if (cancelFee && !isPrepayTask) {
      const dataTransaction = await initData('user/findFATransaction', {
        phone: askerPhone,
        accountType: 'M',
        type: 'C',
        amount: cancelFee,
        isoCode,
      });
      await expect(dataTransaction.length).to.equal(1);
      await expect(dataTransaction[0].amount).to.equal(cancelFee);

      const financialAccount = await initData('user/find-faccount', { phone: askerPhone, isoCode });
      await E2EHelpers.checkFinancialAccount({ financialAccount, mainAccount: -cancelFee, promotion: 0, isoCode });
    }
  };

  static clickCancelTask = async ({ description, reasonCancel, otherReason, taskUsedPromotion }) => {
    await tapText('Hoạt động');
    try {
      await tapId(`task${description}`);
    } catch (error) {
      try {
        await scroll('scrollUpcoming', 500, 'down', 0.5, 0.5);
        await tapId(`task${description}`);
      } catch (err) {
        await swipe('scrollUpcoming', 'up');
        await tapId(`task${description}`);
      }
    }
    try {
      await tapId('btnUpdatePrepaymentTask');
    } catch (error) {
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
    }
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    // Trường hợp Asker đăng lại công việc sẽ không kiểm tra cancel fee,
    // Do task không cancel mà chuyển thành Posted
    if (reasonCancel === 'Tasker có báo không đến được.') {
      await tapText(reasonCancel);
      // Lúc expect đc lúc không
      try {
        await expectElementVisible(
          'Rất tiếc vì tasker đã không đến làm việc. Chúng tôi có thể hỗ trợ đặt lại dịch vụ nếu bạn vẫn còn nhu cầu sử dụng dịch vụ này, công việc sẽ được gửi đến cho 1 tasker khác.',
          'text',
        );
      } catch (error) {}
      await tapText('Đăng lại');
      if (taskUsedPromotion) {
        await expectElementVisible(
          'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
          'text',
        );
        await tapText('Tiếp tục');
      }
    } else {
      if (otherReason) {
        await tapId('btnOtherReason');
        await typeToTextField('inputOtherReason', otherReason);
        try {
          await tapId('txtOtherReason'); // click lần dầu tắt bàn phím
          await tapId('btnOKChooseReason');
        } catch (error) {}
      } else {
        try {
          await expectElementVisible(
            'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
            'text',
          );
          await tapText('Tiếp tục');
        } catch (error) {}
        await tapText(reasonCancel);
        // await tapText('Đồng ý');
      }
      await tapText('Đồng ý');
      if (taskUsedPromotion) {
        //Nhấn tiếp tục popup hiển thị thông báo "Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại."
        await tapText('Tiếp tục');
      } else {
      }
    }
  };

  static cancelTask = async ({
    task,
    reasonCancel,
    cancelFee,
    isoCode,
    asker,
    otherReason,
    taskUsedPromotion,
    isPrepayTask,
  }) => {
    // Các bước cancel task
    await this.clickCancelTask({ description: task.description, reasonCancel, otherReason, taskUsedPromotion });
    // Sau khi cancel task sẽ tiến hành kiểm tra tài khoản
    await this.checkCancelFee({
      description: task.description,
      cancelFee,
      isoCode,
      askerPhone: asker?.phone,
      isPrepayTask,
    });
  };

  static cancelTaskAndTaskerViewConflictTask = async ({ tasker, isoCode, task, asker, isBlacklist }) => {
    await this.cancelTask({ task, asker, isoCode, reasonCancel: 'Không cần công việc này nữa.' });

    const taskerProfile = await initData('user/getUserByPhone', {
      phone: tasker.phone,
      countryCode: COUNTRY_CODE[isoCode],
    });
    if (!isBlacklist) {
      const task1 = await initData('task/getTaskByDescription', { isoCode, description: 'Task 01' });
      await expect(task1.viewedTaskers).to.include(taskerProfile._id);
      const task2 = await initData('task/getTaskByDescription', { isoCode, description: 'Task 02' });
      await expect(task2.viewedTaskers).to.include(taskerProfile._id);
    } else {
      const task1 = await initData('task/getTaskByDescription', { description: 'Task 01', isoCode });
      expect(task1.viewedTaskers).to.include(tasker._id);
      const task2 = await initData('task/getTaskByDescription', { description: 'Task 02', isoCode });
      expect(task2.viewedTaskers).not.to.include(tasker._id);
      const task03 = await initData('task/getTaskByDescription', { description: 'Task 03', isoCode });
      expect(task03.status).equal('CANCELED');
    }
  };
  static cancelTaskDone = async ({ tasker, isoCode, task }) => {
    await tapText('Hoạt động');
    await swipe('scrollUpcoming', 'up');
    await tapId(`task${task.description}`);

    await initData('task/updateTask', [
      {
        description: task.description,
        isoCode,
        dataUpdate: {
          status: 'DONE',
        },
      },
    ]);
    await initData('task/acceptedTask', [
      { isoCode, description: task.description, taskerAccepted: [tasker.phone], status: 'DONE' },
    ]);
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    try {
      // await tapText('Ngày giờ làm việc');
    } catch (error) {}
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    // Task is done can not cancel task
    await expectElementVisible('Bạn không thể hủy công việc đã hoàn thành.', 'text');
  };
  static cancelTaskPosted = async ({ isoCode, task, asker, paymentStatus }) => {
    const isPaymentNew = paymentStatus === 'NEW';
    await this.cancelTask({
      task,
      isoCode,
      reasonCancel: isPaymentNew ? 'Thanh toán thất bại' : 'Không cần công việc này nữa.',
      asker,
    });
    const dataTask = await initData('task/getTaskByDescription', { isoCode, description: task?.description });
    if (isPaymentNew) {
      try {
        expect(dataTask.cancellationText).to.equal('PAYMENT_FAILED');
      } catch (error) {
        expect(dataTask.cancellationReason).to.equal('PAYMENT_FAILED');
      }
    } else {
      expect(dataTask.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
    }
  };

  static cancelTaskConfirmedAndRebook = async ({ isoCode, task }) => {
    await this.clickCancelTask({ description: task.description, reasonCancel: 'Tasker có báo không đến được.' });

    await expectElementVisible(`task${task.description}`);
    // Khi chọn lý do "Tasker có báo không đến được" và chọn đang lại
    // Tasker sẽ được chuyển sang Posted
    const dataTask = await initData('task/getTaskByDescription', { isoCode, description: task?.description });
    expect(dataTask.status).to.equal('POSTED');
  };

  static cancelTaskConfirmed = async ({ isoCode, task }) => {
    await this.cancelTask({ task, reasonCancel: 'Bận việc đột xuất.', cancelFee: 0, isoCode });
    const dataTask = await initData('task/getTaskByDescription', { isoCode, description: task.description });
    expect(dataTask.cancellationReason).to.equal('ASKER_BUSY');
  };

  static cancelTaskWithFeeCancel = async ({ isoCode, task, cancelFee, asker, taskUsedPromotion, isPrepayTask }) => {
    await this.cancelTask({
      isoCode,
      task,
      cancelFee,
      asker,
      reasonCancel: 'Không cần công việc này nữa.',
      taskUsedPromotion,
      isPrepayTask,
    });
  };

  static cancelTaskWithOtherReason = async ({ isoCode, task, cancelFee, asker }) => {
    const otherReason = 'I cancel';
    await this.cancelTask({ isoCode, task, cancelFee, asker, otherReason });

    const dataTask = await initData('task/getTaskByDescription', { isoCode, description: task.description });
    expect(dataTask.cancellationText).to.equal(otherReason);
  };
}

module.exports = { CancelTaskHelpers };
