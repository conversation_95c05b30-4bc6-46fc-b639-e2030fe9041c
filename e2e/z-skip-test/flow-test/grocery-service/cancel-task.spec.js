/**
 * @description Cancel task grocery service
 *   case 1: Cancel task Grocery Assistant success
 *   case 2: Cancel task Grocery Assistant -20k
 *   case 3: Cancel task Grocery Assistant -30%
 *   case 4: Done task Grocery Assistant success with last done task
 *   case 5: Done task Grocery Assistant success with last done task -20k
 *   case 6: Done task Grocery Assistant success with last done task -30%
 * */

const {
  initData,
  loginWithPhoneAndPassword,
  tapId,
  tapText,
  swipe,
  typeToTextField,
  expectIdToHaveText,
  waitForElement,
  postTask,
  expectElementNotExist,
  chooseMarketAddress,
  expectElementVisible,
  postTaskGroceryAssistant,
} = require('../../../step-definition');
const expect = require('chai').expect;

describe('FILE: e2e/z-skip-test/flow-test/grocery-service/cancel-task.spec.js - Cancel task grocery service', () => {
  beforeEach(async () => {
    await initData('service/initGroceryAssistantService', {});
    await initData('user/createUser', [
      { Phone: '**********', Name: 'Asker', Type: 'ASKER', Status: 'ACTIVE' },
      { Phone: '**********', Name: 'Tsker', Type: 'TASKER', Status: 'ACTIVE' },
    ]);
    await device.reloadReactNative();
  });

  it('LINE 38 - Cancel task Grocery Assistant success', async () => {
    await initData('fatransaction/taskerDepositFromBackend', {
      phoneNumber: '**********',
      mainAccount: 1000000,
      promotionAccount: 0,
    });
    await loginWithPhoneAndPassword('**********', '123456');
    await postTaskGroceryAssistant({});
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bạn được hủy miễn phí trong 3 trường hợp sau:', 'text');
    await expectElementVisible('1. Hủy trong vòng 10 phút sau khi đăng việc.', 'text');
    await expectElementVisible('2. Hủy khi chưa có ai nhận việc.', 'text');
    await expectElementVisible('3. Hủy trước giờ làm việc ít nhất 6 tiếng.', 'text');
    await expectElementVisible('Ngoài 3 trường hợp trên chúng tôi sẽ tính phí:', 'text');
    await tapText('Đồng ý');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Chưa có người nhận.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    const task = await initData('task/getTaskByDescription', { description: 'My Task', status: 'CANCELED' });
    expect(task.cancellationReason).to.equal('ASKER_DONT_NEED_ANYMORE');
  });

  it('LINE 68 - Cancel task Grocery Assistant -20k', async () => {
    await initData('fatransaction/taskerDepositFromBackend', {
      phoneNumber: '**********',
      mainAccount: 1000000,
      promotionAccount: 0,
    });
    await loginWithPhoneAndPassword('**********', '123456');
    await postTaskGroceryAssistant({});
    await waitForElement('taskMy Task', 1000);
    await initData('task/updateTask', [
      {
        Description: 'My Task',
        AcceptedTasker: '**********',
        Progress: 'WAITING',
        CreatedAt: 'LATE',
        Status: 'CONFIRMED',
      },
    ]);
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await expectElementVisible('Bạn được hủy miễn phí trong 3 trường hợp sau:', 'text');
    await expectElementVisible('1. Hủy trong vòng 10 phút sau khi đăng việc.', 'text');
    await expectElementVisible('2. Hủy khi chưa có ai nhận việc.', 'text');
    await expectElementVisible('3. Hủy trước giờ làm việc ít nhất 6 tiếng.', 'text');
    await expectElementVisible('Ngoài 3 trường hợp trên chúng tôi sẽ tính phí:', 'text');
    await tapText('Đồng ý');
    await expectElementVisible('Bận việc đột xuất.', 'text');
    await expectElementVisible('Đăng nhầm ngày.', 'text');
    await expectElementVisible('Không cần công việc này nữa.', 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    const data = await initData('faccount/findTaskerFAccount', { taskerPhone: '**********' });
    expect(data.FMainAccount).to.equal(980000);
    expect(data.Promotion).to.equal(0);
  });

  // In progress no show button cancel task
  it.skip('LINE 121 - Cancel task Grocery Assistant -30%', async () => {
    await initData('fatransaction/taskerDepositFromBackend', {
      phoneNumber: '**********',
      mainAccount: 1000000,
      promotionAccount: 0,
    });
    await loginWithPhoneAndPassword('**********', '123456');
    await postTaskGroceryAssistant({});
    await waitForElement('taskMy Task', 1000);
    await initData('task/updateTask', [
      {
        Progress: 'IN_PROGRESS',
        AcceptedTasker: '**********',
        Description: 'My Task',
        Status: 'CONFIRMED',
        CreatedAt: 'LATE',
      },
    ]);
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    const data = await initData('faccount/findTaskerFAccount', { taskerPhone: '**********' });
    expect(data.FMainAccount).to.equal(980500);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 141 - Done task Grocery Assistant success with last done task', async () => {
    await initData('user/updateUser', [{ Phone: '**********', LastDoneTask: 'today' }]);
    await loginWithPhoneAndPassword('**********', '123456');
    await postTaskGroceryAssistant({ isDepositMoney: false });
    await waitForElement('taskMy Task', 1000);
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
  });

  it('LINE 157 - Done task Grocery Assistant success with last done task -20k', async () => {
    await initData('user/updateUser', [{ Phone: '**********', LastDoneTask: 'today' }]);
    await loginWithPhoneAndPassword('**********', '123456');
    await postTaskGroceryAssistant({ isDepositMoney: false });
    await waitForElement('taskMy Task', 1000);
    await initData('task/updateTask', [
      {
        Description: 'My Task',
        AcceptedTasker: '**********',
        Progress: 'WAITING',
        CreatedAt: 'LATE',
        Status: 'CONFIRMED',
      },
    ]);
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    const data = await initData('faccount/findTaskerFAccount', { taskerPhone: '**********' });
    expect(data.FMainAccount).to.equal(-20000);
    expect(data.Promotion).to.equal(0);
  });

  // In progress no show button cancel task
  it.skip('LINE 222 - Done task Grocery Assistant success with last done task -30%', async () => {
    await initData('user/updateUser', [{ Phone: '**********', LastDoneTask: 'today' }]);
    await loginWithPhoneAndPassword('**********', '123456');
    await postTaskGroceryAssistant({ isDepositMoney: false });
    await waitForElement('taskMy Task', 1000);
    await initData('task/updateTask', [
      {
        Progress: 'IN_PROGRESS',
        AcceptedTasker: '**********',
        Description: 'My Task',
        Status: 'CONFIRMED',
        CreatedAt: 'LATE',
      },
    ]);
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('cancelTask');
    await tapId('btnConfirmCancel');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await expectElementNotExist('taskMy Task');
    const data = await initData('faccount/findTaskerFAccount', { taskerPhone: '**********' });
    expect(data.FMainAccount).to.equal(-19500);
    expect(data.Promotion).to.equal(0);
  });

  it('LINE 214 - Task in progress hide button cancel task', async () => {
    const address = '12 Đường Phạm Văn Nghị, Tân Phong, Quận 7, Thành phố Hồ Chí Minh, Việt Nam';
    const goMarketDetail = {
      placeSuperMarket: {
        storeName: 'Lotter quan 7',
        short_address: '469 Đường Nguyễn Hữu Thọ',
        lat: 10.741134,
        lng: 106.7020968,
        city: 'Hồ Chí Minh',
        district: 'Quận 7',
        country: 'VN',
        address: 'LOTTE Mart quận 7, Đường Nguyễn Hữu Thọ, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam',
      },
      distance: 2.6,
      listBuy: 'thit ga, ca, thit bo,...',
      estimatedAmount: 200000,
      depositMoney: 0,
    };
    await initData('task/createTask', [
      {
        ServiceName: 'Đi Chợ',
        AskerPhone: '**********',
        Description: 'Di cho1',
        TaskPlace: 'VN;Hồ Chí Minh;Quận 1',
        GoMarketDetail: goMarketDetail,
        Address: address,
        Rated: true,
      },
    ]);
    await loginWithPhoneAndPassword('**********', '123456');
    await initData('task/updateTask', [
      {
        Progress: 'IN_PROGRESS',
        AcceptedTasker: '**********',
        Description: 'Di cho1',
        Status: 'CONFIRMED',
        CreatedAt: 'LATE',
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDi cho1');
    await waitForElement(
      'Tasker đang thực hiện việc đi chợ. Bấm gọi ngay/chat để liên hệ với công tác viên',
      500,
      'text',
    );
    await swipe('scrollTaskDetail', 'up');
    await expectElementNotExist('cancelTask');
  });
});
