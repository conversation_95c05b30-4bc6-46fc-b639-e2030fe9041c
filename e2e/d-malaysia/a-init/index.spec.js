const { TEST_ID } = require('../../test-id');
const { tapId, initData, chooseIsoCodeFromSetting } = require('../../step-definition');
const { device } = require('detox');

describe('FILE: e2e/d-malaysia/a-init/index.spec.js', () => {
  beforeEach(async () => {
    await initData('resetData');
    await device.reloadReactNative();
  });

  it('LINE 11 - Chọn ngôn ngữ và quốc gia malaysia', async () => {
    await tapId(TEST_ID.CHOOSE_LANGUAGE_HEADER.BUTTON);
    await tapId(`${TEST_ID.MODAL_CHOOSE_THE_LANGUAGE.ITEM_BTN}-vi`);
    await chooseIsoCodeFromSetting('MY');
  });
});
