/**
 * @description Asker rating
 *   case 1: LINE 166 - Task have been done by Task<PERSON>. Asker rating information
 *   case 2: LINE 187 - Task have been done by Task<PERSON>. Asker rating for the Tasker
 *   case 3: LINE 211 - Task have been done by Task<PERSON> and <PERSON><PERSON> rating
 *   case 4: <PERSON><PERSON><PERSON> 219 - Ask<PERSON> rate 5 stars with satisfied feedbacks
 *   case 5: <PERSON>INE 237 - Ask<PERSON> rating the air-conditioner task
 *   case 6: <PERSON>INE 248 - Ask<PERSON> rating the task have bring cleaning tools requirement
 *   case 7: LINE 260 - The tasks have been done. Ask<PERSON> rate many of tasks
 *   case 8: LINE 294 - Rating with Tip not enough money
 *   case 9: LINE 311 - Rating with Tip 20k
 *   case 10: LINE 328 - Rating with Tip 30k
 *   case 11: LINE 345 - Rating with Tip 40k
 *   case 12: LINE 362 - Rating with input tip 20k
 *   case 13: LINE 379 - Rating with input tip 100k
 *   case 14: LINE 396 - Rating with input tip 200k
 *   case 15: LINE 414 - Asker see not uniforms when rating < 5
 *   case 16: LINE 431 - Ask<PERSON> rate 4 stars with prolong time reason
 *   case 17: LINE 451 - Ask<PERSON> can not rate for old task over 7 days
 *   case 18: LINE 469 - Ask<PERSON> done task and finish rating with 5 stars, then edit rating
 *   case 19: <PERSON>INE 499 - Ask<PERSON> done task, finish rating with 3 stars and bad review, then edit rating
 *   case 20: <PERSON><PERSON>E 529 - Asker done task, finish rating with 5 stars, only edit rating within 14 days
 *   case 21: LINE 548 - Check requirement in quick post task detail
 *   case 22: LINE 564 - Asker check add FAV Tasker
 *   case 23: LINE 586 - Asker check show "What is FAV Tasker"
 *   case 24: LINE 594 - Asker done task, and rating in history
 *   case 25: LINE 609 - asker rate 5 start and feed back with badge
 *   case 26: LINE 652 - asker rate 5 start and feed back with badge on time
 *   case 27: LINE 660 - asker rate 5 start and no choose feedback badge
 *   case 28: LINE 680 - Asker update rate task
 * */

const {
  initData,
  tapId,
  tapText,
  waitForElement,
  expectElementVisible,
  expectElementNotExist,
  expectIdToHaveText,
  typeToTextField,
  swipe,
  scrollTo,
  tapHeaderBack,
} = require('../../../step-definition');
const moment = require('moment');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: 'CLEANING',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
  requirements: [
    {
      type: 3.0,
      cost: 30000.0,
      text: {
        vi: 'Mang theo dụng cụ',
        en: 'Bring cleaning supplies',
        ko: '청소도구 준비',
        th: 'นำอุปกรณ์ทำความสะอาดมา',
      },
    },
  ],
};

const TASK_02 = {
  isoCode: MY,
  serviceName: 'CLEANING',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task 02',
  rate: false,
  date: moment().add(5, 'days').toDate(),
};

const TASK_03 = {
  isoCode: MY,
  serviceName: 'CLEANING',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task 03',
  rate: false,
  date: moment().add(3, 'days').toDate(),
};

describe('FILE: e2e/d-malaysia/flow-test/rating/asker-rating.spec.js - Asker rating', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        description: TASK.description,
        taskerAccepted: [TASKER_MALAYSIA.phone],
        status: 'DONE',
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone);
  });

  afterEach(async () => {
    //close dialog
    if (device.getPlatform() === 'android') {
      try {
        await tapText('Đóng');
        await tapText('Đồng ý');
        await tapText('OK');
      } catch (err) {}
    }
  });

  const checkTip = async (tip) => {
    const transaction = await initData('user/findFATransaction', {
      phone: ASKER_MALAYSIA.phone,
      isoCode: ASKER_MALAYSIA.isoCode,
      accountType: 'M',
      type: 'C',
      amount: tip,
    });
    expect(transaction.length).to.equal(1);
    expect(transaction[0].amount).to.equal(tip);

    const transaction2 = await initData('user/findFATransaction', {
      phone: TASKER_MALAYSIA.phone,
      isoCode: TASKER_MALAYSIA.isoCode,
      accountType: 'P',
      type: 'D',
      amount: tip,
    });
    expect(transaction2.length).to.equal(1);
    expect(transaction2[0].amount).to.equal(tip);

    const data1 = await initData('user/find-faccount', {
      phone: TASKER_MALAYSIA.phone,
      isoCode: TASKER_MALAYSIA.isoCode,
    });
    expect(data1.FMainAccount).to.equal(0);
    expect(data1.Promotion).to.equal(tip);

    const data2 = await initData('user/find-faccount', {
      phone: ASKER_MALAYSIA.phone,
      isoCode: ASKER_MALAYSIA.isoCode,
    });
    expect(data2.FMainAccount).to.equal(100000 - tip);
    expect(data2.Promotion).to.equal(0);
  };

  it('LINE 165 - Task have been done by Tasker. Asker rating information', async () => {
    await expectElementVisible('Đánh giá', 'text');
    await tapId('star1');
    await expectIdToHaveText('lbStarStatus', 'QUÁ TỆ');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì bạn chưa hài lòng ?');
    await tapId('star2');
    await expectIdToHaveText('lbStarStatus', 'TỆ');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì bạn chưa hài lòng ?');
    await tapId('star3');
    await expectIdToHaveText('lbStarStatus', 'TRUNG BÌNH');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì bạn chưa hài lòng ?');
    await tapId('star4');
    await expectIdToHaveText('lbStarStatus', 'KHÁ');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì cần làm tốt hơn ?');
    await tapId('star5');
    await expectIdToHaveText('lbStarStatus', 'TỐT');
    await expectIdToHaveText('lbQuestionTitle', 'Lời khen của bạn ?');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
  });

  it('LINE 186 - Task have been done by Tasker. Asker rating for the Tasker', async () => {
    await initData('task/createTask', [TASK_02]);
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        description: TASK_02.description,
        taskerAccepted: [TASKER_MALAYSIA.phone],
        status: 'DONE',
      },
    ]);
    await device.reloadReactNative();
    await tapId('star3');
    await tapId('btnReasonBAD_ON_TIME');
    await tapId('btnReasonBAD_CARE');
    await tapId('btnRating');
    await device.reloadReactNative();
    await waitForElement('star4', 1000);
    await tapId('star4');
    await swipe('scrollRating', 'up');
    await tapId('btnReasonIMPROVE_CARE');
    await tapId('btnReasonIMPROVE_CLEAN');
    await tapId('btnRating');
  });

  it('LINE 210 - Task have been done by Tasker and Asker rating', async () => {
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await typeToTextField('txtReview', 'Good job');
    await tapText('Nhận xét của bạn');
    await tapId('btnRating');
  });

  it('LINE 218 - Asker rate 5 stars with satisfied feedbacks', async () => {
    await initData('update-user/updateAskerBlackList', {
      askerPhone: ASKER_MALAYSIA.phone,
      taskerPhone: [TASKER_MALAYSIA.phone],
      isoCode: ASKER_MALAYSIA.isoCode,
    });
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
    await tapText('Để sau');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    const array = asker.blackList || [];
    expect(array).to.not.include(tasker._id);
    expect(asker.favouriteTasker).to.include(tasker._id);
  });

  it('LINE 236 - Asker rating the air-conditioner task', async () => {
    await tapId('star1');
    await expectElementNotExist('btnReasonWORKING_OUTSIDE');
    await expectElementNotExist('btnReasonBRING_CLEANING_TOOLS');
    await tapId('btnReasonBAD_ON_TIME');
    await tapId('btnRating');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await expectElementVisible('taskMy Task');
  });

  it('LINE 247 - Asker rating the task have bring cleaning tools requirement', async () => {
    await tapId('star1');
    await tapId('btnReasonBAD_WORKING_OUTSIDE');
    await tapId('btnRating');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(asker.blackList).to.include(tasker._id);
  });

  it('LINE 259 - The tasks have been done. Asker rate many of tasks', async () => {
    await initData('task/createTask', [TASK_02]);
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        description: TASK_02.description,
        taskerAccepted: [TASKER_MALAYSIA.phone],
        status: 'DONE',
      },
    ]);
    await initData('task/createTask', [TASK_03]);
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        description: TASK_03.description,
        taskerAccepted: [TASKER_MALAYSIA.phone],
        status: 'DONE',
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
    await device.reloadReactNative();

    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
    await device.reloadReactNative();

    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
  });

  it('LINE 293 - Rating with Tip not enough money', async () => {
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('tip0');
    await tapId('btnRating');
    await expectElementVisible('Tài khoản bPay của bạn không đủ để Tip cho Tasker.', 'text');
    await tapText('Đóng');
    await tapId('tip1');
    await tapId('btnRating');
    await expectElementVisible('Tài khoản bPay của bạn không đủ để Tip cho Tasker.', 'text');
    await tapText('Đóng');
    await tapId('tip2');
    await tapId('btnRating');
    await expectElementVisible('Tài khoản bPay của bạn không đủ để Tip cho Tasker.', 'text');
    await tapText('Đóng');
  });

  it('LINE 310 - Rating with Tip 5k', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('tip0');
    await tapId('btnRating');
    await checkTip(5000);
  });

  it('LINE 327 - Rating with Tip 10k', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('tip1');
    await tapId('btnRating');
    await checkTip(10000);
  });

  it('LINE 344 - Rating with Tip 20k', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await tapId('tip2');
    await tapId('btnRating');
    await checkTip(20000);
  });

  it('LINE 361 - Rating with input tip 20k', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await typeToTextField('inputTip', '20');
    await tapId('btnRating');
    await checkTip(20000);
  });

  it('LINE 378 - Rating with input tip 100k', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await typeToTextField('inputTip', '100');
    await tapId('btnRating');
    await checkTip(100000);
  });

  it('LINE 395 - Rating with input tip 200k', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await typeToTextField('inputTip', '200000');
    await tapId('btnRating');
    await expectElementVisible('Tài khoản bPay của bạn không đủ để Tip cho Tasker.', 'text');
    await tapText('Đóng');
  });

  it('LINE 413 - Asker see not uniforms when rating < 5', async () => {
    await tapId('star1');
    await expectElementVisible('Không mặc đồng phục', 'text');
    await tapId('star2');
    await expectElementVisible('Không mặc đồng phục', 'text');
    await tapId('star3');
    await expectElementVisible('Không mặc đồng phục', 'text');
    await tapId('star4');
    if (device.getPlatform() === 'android') {
      await scrollTo('svQuestionRating', 'bottom');
    }
    await expectElementVisible('Nên mặc đồng phục', 'text');
    await tapId('star5');
    await expectElementNotExist('Không mặc đồng phục', 'text');
    await tapId('btnRating');
  });

  it('LINE 430 - Asker rate 4 stars with prolong time reason', async () => {
    await tapId('star1');
    await expectElementNotExist('btnReasonPROLONG_TIME');
    await tapId('star2');
    await expectElementNotExist('btnReasonPROLONG_TIME');
    await tapId('star3');
    await expectElementNotExist('btnReasonPROLONG_TIME');
    await tapId('star5');
    await expectElementNotExist('btnReasonPROLONG_TIME');
    await tapId('star4');
    if (device.getPlatform() === 'android') {
      await scrollTo('svQuestionRating', 'bottom');
    }
    await tapId('btnReasonPROLONG_TIME');
    await tapId('btnRating');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
  });

  it('LINE 450 - Asker can not rate for old task over 7 days', async () => {
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          date: moment().subtract(8, 'day').toDate(),
        },
      },
    ]);
    await device.reloadReactNative();
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await expectElementVisible('Chi tiết công việc', 'text');
    await expectElementNotExist('ĐÁNH GIÁ ', 'text');
  });

  it('LINE 469 - Asker done task and finish rating with 5 stars, then edit rating', async () => {
    await tapId('star5');
    await tapId('btnRating');
    await tapText('Để sau');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await expectElementVisible('btnUpdatedRating');
    await expectElementNotExist('lbUpdatedAt');
    await tapId('btnUpdatedRating');
    await expectElementVisible('Lưu ý: Bạn chỉ được cập nhật đánh giá 1 lần.', 'text');
    await tapText('Cập nhật');
    await waitForElement('starUpdate4', 1000);
    await tapId('starUpdate4');
    await typeToTextField('txtReview', 'Review 1');
    await tapText('Nhận xét của bạn');
    await tapId('updateRating');
    await tapText('Đồng ý');
    await expectIdToHaveText('lbReview', '- Review 1');
    await expectElementNotExist('btnUpdatedRating');
    await tapHeaderBack();
    await tapId('taskMy Task');
    await expectElementNotExist('btnUpdatedRating');
    await expectIdToHaveText('lbReview', '- Review 1');
    const rating = await initData('rating/getRating', { description: TASK.description, isoCode: TASK.isoCode });
    expect(rating.rate).to.equal(4);
    expect(rating.review).to.equal('Review 1');
    expect(rating.feedBack).to.be.undefined;
    expect(rating.updateRatingAt).not.to.be.null;
  });

  it('LINE 499 - Asker done task, finish rating with 3 stars and bad review, then edit rating', async () => {
    await tapId('star3');
    await tapId('btnReasonOTHER');
    await typeToTextField('txtReview', 'Bad review');
    await tapId('btnRating');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await expectIdToHaveText('lbReview', '- Bad review');
    await expectElementVisible('btnUpdatedRating');
    await tapId('btnUpdatedRating');
    await expectElementVisible('Lưu ý: Bạn chỉ được cập nhật đánh giá 1 lần.', 'text');
    await tapText('Cập nhật');
    await tapId('starUpdate5');
    await tapId('updateRating');
    await tapText('Đồng ý');
    await expectElementNotExist('btnUpdatedRating');
    await expectElementNotExist('lbReview');
    await expectElementNotExist('lbFeedBack');
    await tapHeaderBack();
    await tapId('taskMy Task');
    await expectElementNotExist('lbReview');
    await expectElementNotExist('lbFeedBack');
    const rating = await initData('rating/getRating', { description: TASK.description, isoCode: TASK.isoCode });
    expect(rating.rate).to.equal(5);
    expect(rating.review).to.equal('');
    expect(rating.feedBack).to.be.undefined;
    expect(rating.updateRatingAt).not.to.be.null;
  });

  it('LINE 530 - Asker done task, finish rating with 5 stars, only edit rating within 14 days', async () => {
    await tapId('star5');
    await tapId('btnRating');
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          date: moment().subtract(16, 'day').toDate(),
        },
      },
    ]);
    await tapText('Để sau');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await expectElementNotExist('btnUpdatedRating');
  });

  it('LINE 548 - Check requirement in quick post task detail', async () => {
    await tapId('star1');
    await tapId('btnReasonBAD_BRING_CLEANING_TOOLS');
    await tapId('btnRating');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await expectElementVisible('- Không mang theo dụng cụ làm việc', 'text');
    await tapHeaderBack();
    await expectElementVisible('Đăng lại', 'text');
    await tapText('Đăng lại');
    await swipe('scrollStep4', 'up');
    await expectElementVisible('Dịch vụ thêm', 'text');
    await expectElementVisible('Mang theo dụng cụ +30,000 RM', 'text');
  });

  it('LINE 564 - Asker check add FAV Tasker', async () => {
    await expectElementVisible('Thêm vào yêu thích', 'text');
    await expectElementVisible('notAdd');
    await expectElementNotExist('didAdd');
    await tapId('notAdd');
    await expectElementVisible('didAdd');
    await expectElementNotExist('notAdd');
    await expectElementVisible('star5');
    await tapId('star5');
    await expectElementVisible('didAdd');
    await expectElementNotExist('notAdd');
    await tapId('star4');
    await expectElementVisible('notAdd');
    await expectElementNotExist('didAdd');
    await tapId('notAdd');
    await expectElementVisible('didAdd');
    await expectElementNotExist('notAdd');
    await tapHeaderBack();
    await expectElementNotExist('didAdd');
    await expectElementNotExist('notAdd');
  });

  it('LINE 586 - Asker check show "What is FAV Tasker"', async () => {
    await expectElementVisible('Thêm vào yêu thích', 'text');
    await expectElementVisible('notAdd');
    await expectElementNotExist('didAdd');
    await tapHeaderBack();
    await expectElementNotExist('didAdd');
    await expectElementNotExist('notAdd');
  });
  it('LINE 594 - Asker done task, and rating in history', async () => {
    await tapHeaderBack();
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await typeToTextField('txtReview', 'Good job');
    await tapId('btnRating');
    await tapText('Để sau');
    try {
      await tapText('Để sau');
    } catch (error) {}
    await expectElementVisible('- Good job', 'text');
    await tapHeaderBack();
    await tapId('taskMy Task');
    await expectElementVisible('- Good job', 'text');
  });

  it('LINE 609 - asker rate 5 start and feed back with badge', async () => {
    await expectElementVisible('Đánh giá', 'text');

    // Star 1 no show badge
    await tapId('star1');
    await expectElementNotExist('ON_TIME');
    await expectElementNotExist('FRIENDLY');
    await expectElementNotExist('CLEAN');
    await expectElementNotExist('CHEERFUL');

    // Star 2 no show badge
    await tapId('star2');
    await expectElementNotExist('ON_TIME');
    await expectElementNotExist('FRIENDLY');
    await expectElementNotExist('CLEAN');
    await expectElementNotExist('CHEERFUL');

    // Star 3 no show badge
    await tapId('star3');
    await expectElementNotExist('ON_TIME');
    await expectElementNotExist('FRIENDLY');
    await expectElementNotExist('CLEAN');
    await expectElementNotExist('CHEERFUL');

    // Star 4 no show badge
    await tapId('star4');
    await expectElementNotExist('ON_TIME');
    await expectElementNotExist('FRIENDLY');
    await expectElementNotExist('CLEAN');
    await expectElementNotExist('CHEERFUL');

    await tapId('star5');
    // See badge icon
    await expectElementVisible('ON_TIME');
    await expectElementVisible('FRIENDLY');
    await expectElementVisible('CLEAN');
    await expectElementVisible('CHEERFUL');
    await expectElementVisible('Đúng giờ', 'text');
    await expectElementVisible('Thân thiện', 'text');
    await expectElementVisible('Sạch sẽ', 'text');
    await expectElementVisible('Vui vẻ', 'text');
  });

  it('LINE 652 - asker rate 5 start and feed back with badge on time', async () => {
    await expectElementVisible('Đánh giá', 'text');

    await tapId('star5');
    // See badge icon
    await tapId('btnRating');
  });

  it('LINE 660 - asker rate 5 start and no choose feedback badge', async () => {
    await expectElementVisible('Đánh giá', 'text');

    await tapId('star5');

    // Select badge
    await tapId('ON_TIME');
    await tapId('CHEERFUL');

    // Select again to Unselect badge
    await tapId('ON_TIME');
    await tapId('CHEERFUL');

    // Tap submit
    await tapId('btnRating');
    const rating = await initData('rating/getRating', { description: TASK.description, isoCode: TASK.isoCode });
    expect(rating.rate).to.equal(5);
    expect((rating.feedBack || []).length).to.equal(0);
  });

  it('LINE 680 - Asker update rate task', async () => {
    await expectElementVisible('Đánh giá', 'text');

    await tapId('star3');
    await tapId('btnReasonBAD_ON_TIME');
    await tapId('btnReasonBAD_CARE');

    // Tap submit
    await tapId('btnRating');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await tapId('btnUpdatedRating');
    await tapText('Cập nhật');
    await tapId('starUpdate5');
    await tapText('Cập nhật');
    await tapText('Đồng ý');
    await expectElementVisible('star5');
  });
});
