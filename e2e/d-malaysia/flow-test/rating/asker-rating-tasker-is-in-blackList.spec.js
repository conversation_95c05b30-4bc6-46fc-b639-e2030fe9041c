/**
 * @description Asker rating Tasker is in black list
 *   case 1: <PERSON><PERSON><PERSON> 77 - Ask<PERSON> done task and finish rating with 5 stars and the task move to task history
 *   case 2: LINE 87 - Asker rate 1 star and feedback Tasker not comming
 *   case 3: LINE 97 - Asker rate 4 star and Tasker is in blackList
 *   case 4: <PERSON>IN<PERSON> 117 - Asker rate cleaning task 3 star and at least 1 feedback
 * */

const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  typeToTextField,
  swipe,
} = require('../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: 'CLEANING',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/d-malaysia/flow-test/rating/asker-rating-tasker-is-in-blackList.spec.js - Asker done task and rating', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        description: TASK.description,
        taskerAccepted: [TASKER_MALAYSIA.phone],
        status: 'DONE',
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 65 - Asker done task and finish rating with 5 stars and the task move to task history', async () => {
    await expectElementVisible('Đánh giá', 'text');
    await tapId('star5');
    await tapId('btnRating');
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    const array = asker.blackList || [];
    expect(array).to.not.include(tasker._id);
  });

  it('LINE 75 - Asker rate 1 star and feedback Tasker not comming', async () => {
    await tapId('star2');
    await tapId('btnReasonBAD_NOT_COMING');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(asker.blackList).to.include(tasker._id);
  });

  it('LINE 85 - Asker rate 4 star and Tasker is in blackList', async () => {
    await initData('update-user/updateAskerBlackList', {
      askerPhone: ASKER_MALAYSIA.phone,
      taskerPhone: [TASKER_MALAYSIA.phone],
      isoCode: ASKER_MALAYSIA.isoCode,
    });
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(asker.blackList).to.include(tasker._id);
    await device.reloadReactNative();
    await tapId('star4');
    await tapId('btnReasonIMPROVE_ON_TIME');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
    const tasker1 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const asker1 = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    const array = asker1.blackList || [];
    expect(array).to.not.include(tasker1._id);
  });

  it('LINE 105 - Asker rate cleaning task 3 star and at least 1 feedback', async () => {
    await tapId('star3');
    await expectElementNotExist('btnReasonBRING_CLEANING_TOOLS');
    await tapId('btnReasonOTHER');
    await typeToTextField('txtReview', ' ');
    await tapText('Nhận xét của bạn');
    await tapId('btnRating');
    await expectElementVisible('Đóng', 'text');
    await tapText('Đóng');
    await typeToTextField('txtReview', 'Good job');
    await tapText('Nhận xét của bạn');
    await tapId('btnRating');
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(asker.blackList).to.include(tasker._id);
  });
});
