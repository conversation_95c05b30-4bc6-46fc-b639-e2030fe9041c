/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-21 13:57:07
 * @modify date 2024-06-18 13:58:21
 * @desc [Asker see request increase duration from <PERSON><PERSON>]
 *   case 1:  Asker see request increase duration from <PERSON><PERSON> and enough bPay to accept
 *   case 2:  Asker see request increase duration from <PERSON><PERSON> and not enough bPay to accept
 *   case 3:  Asker see request increase duration from Task<PERSON> and reject it
 */

const {
  initData,
  tapId,
  waitForElement,
  callService,
  expectElementVisible,
  tapHeaderBack,
  sleep,
  ADDRESS_KEY,
} = require('../../../step-definition');
const expect = require('chai').expect;
const moment = require('moment');
const { E2EHelpers } = require('../../../e2e.helpers');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const REQUEST_INCREASE_DURATION = {
  from: 'TASKER',
  _id: 'x6555f243e4a486b20eedb03d',
  createdAt: moment().toDate(),
  type: 'INCREASE_DURATION',
  requestData: {
    duration: {
      old: 3,
      new: 4,
    },
    costDetail: {
      old: {
        cost: 320000,
        baseCost: 320000,
        finalCost: 320000,
        totalCost: 320000,
        currency: {
          code: 'MYR',
          sign: 'RM',
        },
      },
      new: {
        cost: 500000,
        baseCost: 500000,
        finalCost: 500000,
        totalCost: 500000,
        currency: {
          code: 'MYR',
          sign: 'RM',
        },
      },
    },
    status: 'WAITING',
  },
};

describe('FILE: e2e/d-malaysia/flow-test/chat/asker-see-request-increase-duration-from-tasker.spec.js - Asker see request increase duration from Tasker', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('user/updateUser', [
      {
        phone: '0834567891',
        dataUpdate: {
          workingPlaces: [
            {
              country: MY,
              city: 'FT Kuala Lumpur',
              district: 'FT Kuala Lumpur Pusat',
            },
            {
              country: MY,
              city: 'FT Kuala Lumpur',
              district: 'FT Kuala Lumpur Timur',
            },
            {
              country: MY,
              city: 'FT Kuala Lumpur',
              district: ADDRESS_KEY.MY,
            },
          ],
        },
        isoCode: MY,
      },
    ]);
    await initData('task/createTask', [
      {
        isoCode: MY,
        serviceName: 'CLEANING',
        askerPhone: '**********',
        description: 'My Task',
        visibility: 3,
        viewedTaskers: [TASKER_MALAYSIA.phone],
        autoChooseTasker: true,
        duration: 3,
        paymentMethod: 'CREDIT',
      },
    ]);

    await initData('service/updateServiceChannel', [
      { isoCode: MY, serviceName: 'CLEANING', taskerPhone: TASKER_MALAYSIA.phone, isRemove: false },
    ]);
    await initData('update-user/financialAccount', {
      phone: TASKER_MALAYSIA.phone,
      isoCode: MY,
      financialAccountData: { Promotion: 100000 },
    });

    const task = await initData('task/getTaskByDescription', { isoCode: MY, description: 'My Task' });
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
    };
    const response = await callService('/v3/accept-task-my/accept', request);
    expect(response.status).to.equal('CONFIRMED');
  });

  // TODO: Open when update API
  it.skip('LINE 144 - Asker see request increase duration from Tasker and enough bPay to accept', async () => {
    const asker = await initData('user/getUserByPhone', {
      phone: '**********',
      countryCode: '+60',
    });
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task',
      isoCode: MY,
    });
    await initData('chat/insert-chat-message', {
      askerId: asker?._id,
      taskId: task?._id,
      messages: REQUEST_INCREASE_DURATION,
      isoCode: MY,
    });
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: MY,
      financialAccountData: { FMainAccount: 1000000 },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+60');

    await tapId('Tab_Notification');
    await sleep(300);
    await tapId('boxChat_0');
    await waitForElement('txtTitleIncreaseDuration', 1000);
    await expectElementVisible('txtTitleIncreaseDuration');
    await expectElementVisible('txtOldDuration');
    await expectElementVisible('txtOldCost');
    await expectElementVisible('txtNewDuration');
    await expectElementVisible('txtNewCost');
    await expectElementVisible('txtPayMore');
    await expectElementVisible('btnReject');
    await expectElementVisible('btnApprove');
    await tapId('btnApprove');
    await waitForElement('btnActionAlert1', 500);
    await tapId('btnActionAlert1');
    await expectElementVisible('Cảm ơn sự xác nhận của bạn', 'text');
  });

  // TODO: Open when update API
  it.skip('LINE 184 - Asker see request increase duration from Tasker and not enough bPay to accept', async () => {
    const asker = await initData('user/getUserByPhone', {
      phone: '**********',
      countryCode: '+60',
    });
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task',
      isoCode: MY,
    });
    await initData('chat/insert-chat-message', {
      askerId: asker?._id,
      taskId: task?._id,
      messages: REQUEST_INCREASE_DURATION,
      isoCode: MY,
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+60');

    await tapId('Tab_Notification');
    await waitForElement('boxChat_0', 3000);
    await tapId('boxChat_0');
    await waitForElement('txtTitleIncreaseDuration', 1000);
    await expectElementVisible('txtTitleIncreaseDuration');
    await expectElementVisible('txtOldDuration');
    await expectElementVisible('txtOldCost');
    await expectElementVisible('txtNewDuration');
    await expectElementVisible('txtNewCost');
    await expectElementVisible('txtPayMore');
    await expectElementVisible('btnReject');
    await expectElementVisible('btnApprove');
    await tapId('btnApprove');
    await tapId('btnActionAlert1');
    await waitForElement('txtExtraMoney', 500);
    await expectElementVisible('txtExtraMoney');
    await expectElementVisible('btnActionAlert0');
    await expectElementVisible('btnActionAlert1');
    await tapId('btnActionAlert0');
    await waitForElement('lbMainAccount', 1000);
    await tapHeaderBack();
    await tapId('btnApprove');
    await waitForElement('btnActionAlert1', 500);
    await tapId('btnActionAlert1');
    await waitForElement('txtExtraMoney', 500);
    await tapId('btnActionAlert1');
    await expectElementVisible('Cảm ơn sự xác nhận của bạn', 'text');
  });

  // TODO: Open when update API
  it.skip('LINE 237 - Asker see request increase duration from Tasker and reject it', async () => {
    const asker = await initData('user/getUserByPhone', {
      phone: '**********',
      countryCode: '+60',
    });
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task',
      isoCode: MY,
    });
    await initData('chat/insert-chat-message', {
      askerId: asker?._id,
      taskId: task?._id,
      messages: REQUEST_INCREASE_DURATION,
      isoCode: MY,
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+60');

    await tapId('Tab_Notification');
    await waitForElement('boxChat_0', 3000);
    await tapId('boxChat_0');
    await waitForElement('txtTitleIncreaseDuration', 1000);
    await expectElementVisible('txtTitleIncreaseDuration');
    await expectElementVisible('txtOldDuration');
    await expectElementVisible('txtOldCost');
    await expectElementVisible('txtNewDuration');
    await expectElementVisible('txtNewCost');
    await expectElementVisible('txtPayMore');
    await expectElementVisible('btnReject');
    await expectElementVisible('btnApprove');
    await tapId('btnReject');
    await tapId('btnActionAlert1');
    await expectElementVisible('Rất tiếc vì bạn đã từ chối', 'text');
  });
});
