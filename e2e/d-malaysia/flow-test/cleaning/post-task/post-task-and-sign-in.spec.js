const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  loginWithModal,
  signUpWithModal,
  forgotPasswordWithModal,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { device } = require('detox');
const { ASKER_MALAYSIA, TASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/post-task/post-task-and-sign-in.spec.js - New User post cleaning task before sign in', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, randomId: true }, TASKER_MALAYSIA]);

    await device.reloadReactNative();
    try {
      await tapId('cancelVerify');
    } catch (error) {}
  });

  const checkDataAfterPostTask = async () => {
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('valueOptionals', 'Nấu ăn');
  };

  it('LINE 45 - New customer post cleaning task and sign in', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseServies-0');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');

    await loginWithModal('0123456799', '123456', '+60');
    await tapText('Theo dõi công việc');
    await checkDataAfterPostTask();
  });

  it('LINE 58 - New customer post cleaning task and sign up', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseServies-0');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await signUpWithModal('Bill Gate', '0123456781', '<EMAIL>', '+60');
    await tapText('Theo dõi công việc');
    await checkDataAfterPostTask();
  });

  it('LINE 71 - New customer post cleaning task and for got password', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseServies-0');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');

    await forgotPasswordWithModal('0123456799', '+60');

    await tapText('Theo dõi công việc');
    await checkDataAfterPostTask();
  });
});
