/**
 * @description Post task with pet (3 test cases)
 *   case 1: LIN<PERSON> 35 - Post task with cat
 *   case 2: <PERSON>IN<PERSON> 56 - Post task with dog
 *   case 3: <PERSON>IN<PERSON> 77 - Post task with cat and dog
 * */

const {
  initData,
  tapId,
  tapText,
  postTask,
  scroll,
  expectIdToHaveText,
  expectElementVisible,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { ASKER_MALAYSIA, TASKER_MALAYSIA } = require('../../../../helpers/constants');

const { E2EHelpers } = require('../../../../e2e.helpers');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/post-task/post-task-with-pet.spec.js - Post task with pet', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_<PERSON>LAYSIA, TASKER_MALAYSIA]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 38 - Post task with cat', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await scroll('scrollStep2Cleaning', 300, 'down', 0.5, 0.5);
    await tapId('whatIsChoosePet');
    await expectElementVisible('txtTitleAlert_Nhà có vật nuôi');
    await tapText('Đã hiểu');
    await tapId('ChoosePet');
    await tapId('Pet1');
    await tapText('Đồng ý');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '160,000 RM');
    await expectIdToHaveText('valuePets', 'Mèo');
  });

  it('LINE 70 - Post task with dog', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await scroll('scrollStep2Cleaning', 300, 'down', 0.5, 0.5);
    await tapId('whatIsChoosePet');
    await expectElementVisible('txtTitleAlert_Nhà có vật nuôi');
    await tapText('Đã hiểu');
    await tapId('ChoosePet');
    await tapId('Pet0');
    await tapText('Đồng ý');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '160,000 RM');
    await expectIdToHaveText('valuePets', 'Chó');
  });

  it('LINE 102 - Post task with cat and dog', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await scroll('scrollStep2Cleaning', 300, 'down', 0.5, 0.5);
    await tapId('whatIsChoosePet');
    await expectElementVisible('txtTitleAlert_Nhà có vật nuôi');
    await tapText('Đã hiểu');
    await tapId('ChoosePet');
    await tapId('Pet0');
    await tapId('Pet1');
    await tapText('Đồng ý');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '160,000 RM');
    await expectIdToHaveText('valuePets', 'Chó, Mèo');
  });
});
