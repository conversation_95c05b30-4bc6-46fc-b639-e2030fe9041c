/**
 * @description New User post task
 *   case 1: <PERSON><PERSON><PERSON> 33 - <PERSON><PERSON> choose duration, but choose full service
 *   case 2: LINE 53 - <PERSON><PERSON> choose duration, but choose cooking service
 *   case 3: LINE 69 - <PERSON><PERSON> choose duration, but choose Ironing service
 *   case 4: <PERSON><PERSON><PERSON> 85 - <PERSON><PERSON> choose duration, but choose Bring cleaning supplies
 *   case 5: LINE 101 - Update contact info
 * */

const {
  initData,
  tapId,
  tapText,
  postTask,
  typeToTextField,
  expectIdToHaveText,
  waitForElement,
  clearTextInput,
  typeToTextFieldSubmitKeyboard,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { ASKER_MALAYSIA, TASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/post-task/post-task-and-choose-service.spec.js - New User post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 42 - Dont choose duration, but choose full service', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '240,000 MYR/3h');
    await tapId('chooseServies-1');
    await expectIdToHaveText('lbPrice', '320,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '320,000 RM');
    await expectIdToHaveText('valueOptionals', 'Nấu ăn, Ủi đồ');
  });

  it('LINE 62 - Dont choose duration, but choose cooking service', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '320,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '320,000 RM');
    await expectIdToHaveText('valueOptionals', 'Nấu ăn');
  });

  it('LINE 78 - Dont choose duration, but choose Ironing service', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseServies-1');
    await expectIdToHaveText('lbPrice', '320,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '320,000 RM');
    await expectIdToHaveText('valueOptionals', 'Ủi đồ');
  });

  it('LINE 94 - Dont choose duration, but choose Bring cleaning supplies', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await expectIdToHaveText('lbPrice', '240,000 MYR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '240,000 RM');
  });

  it('LINE 108 - Update contact info', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await expectIdToHaveText('lbPrice', '240,000 MYR/3h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // Press change contact
    await tapId('btnChangeContact');

    // Check items exists
    await waitForElement('contactPhoneNumber', 300);
    await waitForElement('contactName', 300);

    // Cancel change contact
    await tapId('iconCloseModal');

    // Press change contact again
    await tapId('btnChangeContact');

    // Change new contact
    await clearTextInput('contactPhoneNumber');
    await typeToTextField('contactPhoneNumber', '0123456788');
    await clearTextInput('contactName');
    await typeToTextFieldSubmitKeyboard('contactName', 'The New Name');

    // Press done change
    await tapText('Cập nhật'); // Update

    // Check contact change applied
    await expectIdToHaveText('contactPT4', '0123456788');
    await expectIdToHaveText('contactNamePT4', 'The New Name');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '3 giờ, 14:00 đến 17:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  });
});
