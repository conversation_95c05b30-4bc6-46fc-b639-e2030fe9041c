/**
 * @description Post task - check cost by duration (1 test case)
 *   case 1: LINE 22 - Discount by duration
 * */

const {
  tapId,
  tapText,
  initData,
  postTask,
  expectIdToHaveText,
  ADDRESS_KEY,
} = require('../../../../../step-definition');

const { E2EHelpers } = require('../../../../../e2e.helpers');
const {
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
} = require('../../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/post-task/campaign-promotion/asker-post-task-discount-by-duration.spec.js - Discount by duration', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);
    await initData('service/updateDiscountDurationService', { serviceName: CLEANING, isoCode: MY });

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 21 - Discount by duration', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('chooseDuration-3');
    await expectIdToHaveText('lbPrice', '216,000 MYR/3h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '272,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  });
});
