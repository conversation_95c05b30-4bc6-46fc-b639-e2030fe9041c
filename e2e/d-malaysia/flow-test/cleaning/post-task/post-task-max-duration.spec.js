/**
 * @description Post task max duration is 4 hours
 *   case 1: <PERSON><PERSON><PERSON> 51 - <PERSON><PERSON> can not post a task over 4 hours
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');

const { ASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/post-task/post-task-max-duration.spec.js - Post task max duration is 4 hours', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('<PERSON><PERSON>E 29 - Ask<PERSON> can not post a task over 4 hours', async () => {
    await tapId('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-4');
    await tapId('chooseServies-0');
    await expectElementVisible(
      'Thời gian làm tối đa là 4h. Bạn vui lòng giảm thời gian làm việc để có thể chọn thêm dịch vụ',
      'text',
    );
    await tapText('Đóng');
    await tapId('chooseDuration-3');
    await tapId('chooseServies-0');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
  });
});
