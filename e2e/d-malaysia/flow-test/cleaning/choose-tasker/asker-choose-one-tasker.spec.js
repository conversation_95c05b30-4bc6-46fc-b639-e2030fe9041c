/**
 * @description New User post task
 *   case 1: LINE 30 - Ask<PERSON> choose tasker
 *   case 2: LINE 53 - Ask<PERSON> choose 2 tasker
 * */

const { initData, tapId, swipe, tapText, waitForElement, expectIdToHaveText } = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');
const moment = require('moment');

const {
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  TASKER_MALAYSIA_02,
} = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/choose-tasker/asker-choose-one-tasker.spec.js - New User post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA, TASKER_MALAYSIA_02]);
    await initData('task/createTask', [
      {
        isoCode: MY,
        serviceName: CLEANING,
        askerPhone: ASKER_MALAYSIA.phone,
        description: 'My Task',
        autoChooseTasker: false,
        rated: true,
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 43 - Asker choose tasker', async () => {
    await tapText('Hoạt động');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '320,000 RM');
    await initData('task/acceptedTask', [
      { isoCode: MY, description: 'My Task', taskerAccepted: ['0834567891'], status: 'WAITING_ASKER_CONFIRMATION' },
    ]);

    await device.reloadReactNative();
    await waitForElement('Hoạt động', 1000, 'text');
    await tapText('Hoạt động');
    await waitForElement('taskDuration0', 1000);
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Những người đã nhận công việc của bạn');
    await tapId('tasker_0');
    await tapText('Chọn Tasker này');
    await expectIdToHaveText('taskerName0', 'Tasker');
  });

  it('LINE 65 - Asker choose 2 tasker', async () => {
    await tapText('Hoạt động');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '320,000 RM');
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        description: 'My Task',
        taskerAccepted: [TASKER_MALAYSIA.phone, TASKER_MALAYSIA_02.phone],
        status: 'WAITING_ASKER_CONFIRMATION',
      },
    ]);

    await device.reloadReactNative();
    await waitForElement('Hoạt động', 1000, 'text');
    await tapText('Hoạt động');
    await waitForElement('Có 02 người nhận công việc của bạn', 1000, 'text');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Những người đã nhận công việc của bạn');
    await waitForElement('tasker_0', 1000);
    await waitForElement('tasker_1', 1000);
    await tapId('tasker_1');
    await tapText('Chọn Tasker này');
    await expectIdToHaveText('taskerName0', 'Tasker 01');
    await tapId('taskerName0');
    await waitForElement('taskDetailTaskerName', 1000);
    await expectIdToHaveText('taskDetailTaskerName', 'Tasker 01');
  });

  it('LINE 97 - Asker see chat history', async () => {
    await initData('task/acceptedTask', [
      { isoCode: MY, description: 'My Task', taskerAccepted: [TASKER_MALAYSIA.phone], status: 'DONE' },
    ]);
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: MY,
        dataUpdate: {
          date: moment().subtract(1, 'day').toDate(),
        },
      },
    ]);

    await waitForElement('Hoạt động', 1000, 'text');
    await tapText('Hoạt động');
    await tapText('Lịch sử');
    await tapId('taskMy Task');
    await swipe('scrollHistoryDetail', 'up');
    await waitForElement('seeChatHistory', 500);
  });
});
