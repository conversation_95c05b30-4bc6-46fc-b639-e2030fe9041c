/**
 * @description Check price when change date time with promotion
 *   case 1: Change date time and duration cleaning service with promotion
 *   case 2: Change date time and duration cleaning service with promotion 0 đ
 * */

const {
  initData,
  tapId,
  waitForElement,
  expectIdToHaveText,
  tapText,
  swipe,
  expectElementNotExist,
} = require('../../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');
const {
  ASKER_MALAYSIA,
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/update-date-time/change-date-time-with-promotion.spec.js - Check price when change date time with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: MY,
        code: 'abc123',
        value: 100000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
      {
        isoCode: MY,
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 51 - Change date time and duration cleaning service with promotion', async () => {
    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: MY,
          serviceName: CLEANING,
          askerPhone: ASKER_MALAYSIA.phone,
          description: 'Don dep nha 01',
          removeRequirements: true,
        },
      ]);
      await initData('promotion/apply-promotion-code-to-task', [
        { description: 'Don dep nha 01', isoCode: MY, promotionCode: 'def123' },
      ]);

      await tapText('Hoạt động');
      await tapId('taskDuration0');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await expectElementNotExist('320,000 MYR', 'text');
      await expectElementNotExist('270,000 MYR/4h', 'text');
      await tapId('weekdays_5');

      await expectIdToHaveText('lbOriginPrice', '320,000 MYR');
      await expectIdToHaveText('lbPrice', '270,000 MYR/4h');
      await tapId('chooseDuration-3');
      await expectIdToHaveText('lbOriginPrice', '240,000 MYR');
      await expectIdToHaveText('lbPrice', '190,000 MYR/3h');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 1000, 'text');
      await tapText('Đóng');
      const data = await initData('task/getTaskByDescription', { description: 'Don dep nha 01', isoCode: MY });
      expect(data.status).to.equal('POSTED');
      expect(data.cost).to.equal(240000);
      await expectIdToHaveText('lbPriceDon dep nha 01', '190,000 RM');
    }
  });

  it('LINE 89 - Change date time and duration cleaning service with promotion 0 đ', async () => {
    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: MY,
          serviceName: CLEANING,
          askerPhone: ASKER_MALAYSIA.phone,
          description: 'Don dep nha 01',
          removeRequirements: true,
        },
      ]);
      await initData('promotion/apply-promotion-code-to-task', [
        { description: 'Don dep nha 01', isoCode: MY, promotionCode: 'abc123' },
      ]);
      await tapText('Hoạt động');
      await tapId('taskDon dep nha 01');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');

      await tapId('weekdays_5');
      await expectIdToHaveText('lbOriginPrice', '320,000 MYR');
      await expectIdToHaveText('lbPrice', '220,000 MYR/4h');
      await tapId('chooseDuration-3');
      await expectIdToHaveText('lbOriginPrice', '240,000 MYR');
      await expectIdToHaveText('lbPrice', '140,000 MYR/3h');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 1000, 'text');
      await tapText('Đóng');
      const data = await initData('task/getTaskByDescription', { description: 'Don dep nha 01', isoCode: MY });
      expect(data.status).to.equal('POSTED');
      expect(data.cost).to.equal(240000);
      await expectIdToHaveText('lbPriceDon dep nha 01', '140,000 RM');
    }
  });
});
