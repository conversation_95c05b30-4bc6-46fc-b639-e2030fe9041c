/**
 * @description Change datetime Deep Cleaning task
 *   case 1: <PERSON><PERSON><PERSON> 30 - Ask<PERSON> want to change task datetime - POSTED
 *   case 2: <PERSON><PERSON><PERSON> 64 - Asker want to change task datetime - WAITING
 *   case 3: LINE 95 - Asker want to change task datetime - CONFIRMED
 *   case 4: LINE 123 - Asker want to change deep cleaning task time include promotion
 *   case 5: LINE 176 - Asker want to change cleaning task time include promotion
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  selectTime,
  swipe,
  postTask,
  waitForElement,
  selectTime24h,
  scroll,
  expectElementNotVisible,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { device } = require('detox');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  TASKER_MALAYSIA_02,
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/update-date-time/asker-change-date-time.spec.js - Change datetime Deep Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA, TASKER_MALAYSIA_02]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 42 - Asker want to change task datetime - POSTED', async () => {
    // TODO: Run on Android

    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: MY,
          serviceName: CLEANING,
          askerPhone: ASKER_MALAYSIA.phone,
          description: 'Don dep nha 01',
        },
      ]);
      await tapText('Hoạt động');
      await expectIdToHaveText('serviceNameDon dep nha 01', 'Dọn dẹp nhà');
      await expectIdToHaveText('taskStatusDon dep nha 01', 'Mới đăng');
      await tapId('taskDon dep nha 01');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      // await selectTime(1, true, 'AM');
      await tapId('weekdays_3');
      await selectTime24h(23);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await waitForElement(
        'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
        500,
        'text',
      );
      await tapText('Đóng');
      await selectTime24h(6);
      await tapText('Đồng ý');

      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');
      await tapId('taskDon dep nha 01');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
      await expectIdToHaveText('valueDuration', '4 giờ, 15:00 đến 19:00');
    }
  });

  it('LINE 83 - Asker want to change task datetime - WAITING', async () => {
    // TODO: Run on Android

    if (device.getPlatform() === 'ios') {
      await initData('task/createTask', [
        {
          isoCode: MY,
          serviceName: CLEANING,
          askerPhone: ASKER_MALAYSIA.phone,
          description: 'Don dep nha 01',
          removeRequirements: true,
        },
      ]);
      await initData('task/acceptedTask', [
        {
          isoCode: MY,
          description: 'Don dep nha 01',
          taskerAccepted: [TASKER_MALAYSIA.phone],
          status: 'WAITING_ASKER_CONFIRMATION',
        },
      ]);
      await tapText('Hoạt động');
      await expectIdToHaveText('serviceNameDon dep nha 01', 'Dọn dẹp nhà');
      await expectIdToHaveText('taskStatusDon dep nha 01', 'Chờ xác nhận');
      await expectIdToHaveText('taskerName0', 'Tasker 01');
      await tapId('taskDon dep nha 01');
      await expectElementVisible('tasker-Tasker 01');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await tapId('weekdays_3');
      await selectTime(1);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');
      await tapId('taskDon dep nha 01');
      await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
      await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
      await expectIdToHaveText('valueDuration', '4 giờ, 15:00 đến 19:00');
    }
  });

  it('LINE 205 - Dont choose 2 duration, because choose cooking service', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await scroll('scrollStep2Cleaning', 500, 'down', 0.5, 0.5);
    await tapId('chooseServies-0');
    await expectIdToHaveText('lbPrice', '320,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '320,000 RM');
    await expectIdToHaveText('valueOptionals', 'Nấu ăn');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapText('3 giờ');
    await expectIdToHaveText('lbPrice', '240,000 MYR/3h');
    await tapText('2 giờ');
    await expectIdToHaveText('lbPrice', '240,000 MYR/3h');
    await tapText('4 giờ');
    await expectElementNotVisible('lbPrice');
  });
});
