/**
 * @description Change date time Cleaning task
 *   case 1: LINE 30 - Ask<PERSON> want to change task date time - POSTED
 *   case 2: <PERSON><PERSON><PERSON> 64 - Asker want to change task date time - WAITING
 *   case 3: LINE 95 - Asker want to change task date time - CONFIRMED
 *   case 4: LINE 123 - Asker want to change deep cleaning task time include promotion
 *   case 5: LINE 176 - Asker want to change cleaning task time include promotion
 * */

const { initData } = require('../../../../step-definition');
const { UpdateDateTimeHelpers } = require('../../../../helpers/task/updateDateTime.helpers');
const {
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const params = {
  serviceName: CLEANING,
  isoCode: MY,
};

describe('FILE: e2e/d-malaysia/flow-test/cleaning/update-date-time/asker-change-date-time-and-keep-tasker.spec.js - Change date time Deep Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
  });

  it('LINE 27 - Asker want to change task WAITING_ASKER_CONFIRMATION and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskWaitingAndKeepTasker(params);
  });

  it('LINE 31 - Asker want to change task CONFIRMED and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTasker(params);
  });

  it('LINE 35 - Asker change date time task CONFIRMED and keep tasker conflict time', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerConflictTime(params);
  });

  it('LINE 39 - Asker want to change task CONFIRMED and keep tasker and not enough money', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerNotEnoughMoney(params);
  });
});
