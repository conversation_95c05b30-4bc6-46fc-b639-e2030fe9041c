/**
 * @description Update note
 *   case 1: Asker update note normal task
 *   case 2: Asker update note prepay task
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  postTask,
  typeToTextField,
  clearTextInput,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { ASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/update-date-time/update-note.spec.js - Update note', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 31 - Asker update note normal task', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await tapId('taskMy Task');
    await tapId('btnEditTaskNote');
    await clearTextInput('taskNote');
    await typeToTextField('taskNote', 'Lau dọn phong ngu tang 1\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn phong ngu tang 1\n', 500, 'text');
  });
});
