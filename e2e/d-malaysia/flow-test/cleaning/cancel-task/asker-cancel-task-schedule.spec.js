/**
 * @description Cancel task schedule
 *   case 1: LINE 26 - Post task and cancel schedule task
 * */

const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  expectElementVisible,
  waitForElement,
  tapHeaderBack,
  expectElementNotVisible,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');

const { ASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/cancel-task/asker-cancel-task-schedule.spec.js - Cancel task schedule', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 40 - Post task and cancel schedule task', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('btnNextStep2');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek3');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '2 giờ, 14:00 đến 16:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '160,000 RM');
    await tapHeaderBack();
    await tapId('Tab_Schedule');
    await expectElementVisible('Đang hoạt động', 'text');
    await tapId('Tab_Upcoming');

    await tapId('taskMy Task');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapText('Đồng ý');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await expectElementVisible('Bạn có muốn dừng lịch lặp lại ?', 'text');
    await tapText('Đồng ý');
    await expectElementVisible('Công việc đã được hủy. Phí: 0 RM.', 'text');
    await tapText('Đóng');
    await waitForElement('postTaskNow', 1000);
    await expectElementNotVisible('TAB_UPCOMINGMy Task');
    await tapId('Tab_Schedule');
    await expectElementVisible('Tạm dừng', 'text');
    // await expectIdToHaveText('taskStatusMy Task', 'Tạm dừng');
  });
});
