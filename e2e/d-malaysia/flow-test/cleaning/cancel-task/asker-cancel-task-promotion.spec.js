/**
 * @description Asker cancel task with promotion
 *   case 1: LINE 63 - Asker cancel the task is not include fee
 *   case 2: LINE 141 - Asker cancel the task include fee
 * */

const { initData } = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');
const expect = require('chai').expect;

const {
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
} = require('../../../../helpers/constants');

const moment = require('moment');

const TASK = {
  removeRequirements: true,
  isoCode: MY,
  serviceName: CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My task',
};

const PROMOTION_01 = {
  isoCode: MY,
  code: 'def123',
  value: 50000,
  target: 'ASKER_MALAYSIA',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};

describe('FILE: e2e/d-malaysia/flow-test/cleaning/cancel-task/asker-cancel-task-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);

    await initData('promotion/create-promotion-code', PROMOTION_01);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 58 - Asker cancel the task is not include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);

    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: MY,
      tasker: TASKER_MALAYSIA,
      task: TASK,
      asker: ASKER_MALAYSIA,
      taskUsedPromotion: true,
    });

    const promotionHistory = await initData('promotion/getPromotionHistory', {
      promotionCode: PROMOTION_01.code,
      isoCode: PROMOTION_01.isoCode,
    });

    await expect(promotionHistory).to.equal(null);
  });

  it('LINE 136 - Asker cancel the task include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);

    await initData('task/acceptedTask', [
      { isoCode: MY, description: TASK.description, taskerAccepted: [TASKER_MALAYSIA.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          status: 'CONFIRMED',
        },
      },
    ]);

    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: MY,
      tasker: TASKER_MALAYSIA,
      task: TASK,
      asker: ASKER_MALAYSIA,
      taskUsedPromotion: true,
      cancelFee: 96000,
    });

    const promotionHistory = await initData('promotion/getPromotionHistory', {
      promotionCode: PROMOTION_01.code,
      isoCode: PROMOTION_01.isoCode,
    });

    await expect(promotionHistory.promotionCode).to.equal(PROMOTION_01.code);
    await expect(promotionHistory.name).to.equal(ASKER_MALAYSIA.name);
  });
});
