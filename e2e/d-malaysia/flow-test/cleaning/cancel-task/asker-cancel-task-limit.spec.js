/**
 * @description Asker cancel task limit
 *   case 1: LINE 21 - Asker cancel task with task createdAt + 10 minutes is less than or equal current date and total task canceled is less than limit
 *   case 2: LINE 42 - Ask<PERSON> cancel many tasks within 24h
 *   case 3: LINE 112 - Ask<PERSON> cancel task with task createdAt + 10 minutes is greater than current date
 * */

const {
  initData,
  tapId,
  tapText,
  expectElementNotExist,
  waitForElement,
  swipe,
} = require('../../../../step-definition');
const moment = require('moment');

const {
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
} = require('../../../../helpers/constants');

const { E2EHelpers } = require('../../../../e2e.helpers');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/cancel-task/asker-cancel-task-limit.spec.js - Asker cancel task limit', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);

    await initData('settingSystem/update-fields', {
      isoCode: ASKER_MALAYSIA.isoCode,
      dataUpdate: {
        limitNumberCancelTask: 6,
      },
    });

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 41 - Asker cancel task with task createdAt + 10 minutes is less than or equal current date and total task canceled is less than limit', async () => {
    await initData('task/createTask', [
      {
        isoCode: ASKER_MALAYSIA.isoCode,
        askerPhone: ASKER_MALAYSIA.phone,
        serviceName: CLEANING,
        description: 'Don dep nha 01',
        status: 'POSTED',
      },
      {
        isoCode: ASKER_MALAYSIA.isoCode,
        askerPhone: ASKER_MALAYSIA.phone,
        serviceName: CLEANING,
        description: 'Don dep nha 02',
        status: 'CANCELED',
      },
      {
        isoCode: ASKER_MALAYSIA.isoCode,
        askerPhone: ASKER_MALAYSIA.phone,
        serviceName: CLEANING,
        description: 'Don dep nha 03',
        status: 'CANCELED',
      },
      {
        isoCode: ASKER_MALAYSIA.isoCode,
        askerPhone: ASKER_MALAYSIA.phone,
        serviceName: CLEANING,
        description: 'Don dep nha 04',
        status: 'CANCELED',
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskDuration0');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await waitForElement('Không cần công việc này nữa.', 1000, 'text');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await expectElementNotExist('taskDuration0');
  });

  it('LINE 221 - Asker cancel task with task createdAt + 10 minutes is greater than current date', async () => {
    await initData('task/createTask', [
      {
        isoCode: MY,
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'Don dep nha 01',
        status: 'POSTED',
      },
    ]);
    await initData('task/updateTask', {
      description: 'Don dep nha 01',
      isoCode: MY,
      dataUpdate: { createdAt: moment().subtract(11, 'minute').toDate() },
    });
    await tapText('Hoạt động');
    await tapId('TAB_UPCOMINGDon dep nha 01');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('cancelTask');
    await tapText('Đồng ý');
    await tapText('Không cần công việc này nữa.');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await tapText('Đóng');
    await expectElementNotExist('TAB_UPCOMINGDon dep nha 05');
  });
});
