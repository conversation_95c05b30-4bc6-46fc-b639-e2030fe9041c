/**
 * @description Asker cancel task done
 *   case 1: LINE 28 - <PERSON><PERSON> cancel posted task
 * */

const { initData } = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');
const {
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
} = require('../../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'Don dep nha 01',
};
describe('FILE: e2e/d-malaysia/flow-test/cleaning/cancel-task/asker-cancel-task-done.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 55 - Asker cancel posted task', async () => {
    await CancelTaskHelpers.cancelTaskDone({ tasker: TASKER_MALAYSIA, isoCode: MY, task: TASK });
  });
});
