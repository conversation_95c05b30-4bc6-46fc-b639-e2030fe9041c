const { initData } = require('../../../../step-definition');
const { E2EHel<PERSON> } = require('../../../../e2e.helpers');

const {
  SERVICE_NAME: { CLEANING },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
} = require('../../../../helpers/constants');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');

const TASK_01 = {
  isoCode: MY,
  serviceName: CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'Task 01',
  visibility: 3,
};
const TASK_02 = {
  isoCode: MY,
  serviceName: CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'Task 02',
  visibility: 3,
};

const TASK_03 = {
  isoCode: MY,
  serviceName: CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'Task 03',
  visibility: 3,
  viewedTaskers: [TASKER_MALAYSIA.phone],
  acceptedTasker: [TASKER_MALAYSIA.phone],
};

describe('FILE: e2e/d-malaysia/flow-test/cleaning/cancel-task/asker-cancel-task-and-tasker-view-conflict-tasks.spec.js - Asker cancel task. Tasker will see the conflict tasks', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK_01, TASK_02, TASK_03]);
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });

    await initData('task/updateTask', [
      {
        description: 'Task 03',
        isoCode: MY,
        dataUpdate: {
          status: 'CONFIRMED',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 200 - Asker cancel posted task', async () => {
    await CancelTaskHelpers.cancelTaskAndTaskerViewConflictTask({
      tasker: TASKER_MALAYSIA,
      isoCode: MY,
      task: TASK_03,
    });
  });

  it('LINE 204 - Asker cancel posted task and other task of blacklist Asker', async () => {
    await initData('user/updateUser', [
      {
        isoCode: MY,
        phone: ASKER_MALAYSIA.phone,
        dataUpdate: {
          isBlacklist: true,
        },
      },
    ]);
    await initData('task/updateTask', [
      {
        isoCode: TASK_02.isoCode,
        description: TASK_02.description,
        dataUpdate: {
          isBlacklistAsker: true,
        },
      },
    ]);
    await CancelTaskHelpers.cancelTaskAndTaskerViewConflictTask({
      tasker: TASKER_MALAYSIA,
      isoCode: MY,
      task: TASK_03,
      isBlacklist: true,
    });
  });
});
