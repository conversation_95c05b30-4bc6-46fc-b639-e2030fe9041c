/**
 * @description Asker accept task cleaning
 *   case 1:  Asker accept-task cleaning - UI Item task
 *   case 2:  Asker accept-task cleaning - UI task detail
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  postTask,
  callService,
  expectElementNotExist,
  ADDRESS_KEY,
} = require('../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');
const {
  SERVICE_NAME: { CLEANING },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/tasker-accept-task.spec.js - Asker accept task cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 33 - Asker accept-task cleaning - UI Item task', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '320,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await initData('service/updateServiceChannel', [
      { isoCode: MY, serviceName: CLEANING, taskerPhone: TASKER_MALAYSIA.phone },
    ]);
    const task = await initData('task/getTaskByDescription', { description: 'My Task', isoCode: MY });
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: MY,
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-my/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await device.reloadReactNative();
    await tapText('Hoạt động');
    await waitForElement('taskerName0', 1000);
    await expectIdToHaveText('taskerName0', 'Tasker');
    await tapId('taskerName0');
    await tapId('seeMore');
    await expectElementNotExist('Chọn Tasker này', 'text');
  });

  it('LINE 80 -  Asker accept-task cleaning - UI task detail', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('chooseDuration-4');
    await expectIdToHaveText('lbPrice', '320,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('taskDuration0');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '320,000 RM');
    await initData('service/updateServiceChannel', [
      { isoCode: MY, serviceName: CLEANING, taskerPhone: TASKER_MALAYSIA.phone },
    ]);

    const task = await initData('task/getTaskByDescription', { description: 'My Task', isoCode: MY });
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: MY,
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-my/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await device.reloadReactNative();
    await tapText('Hoạt động');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapId('taskerName0');
    await waitForElement('seeMore', 1000);
    await tapId('seeMore');
    await expectElementNotExist('Chọn Tasker này', 'text');
  });
});
