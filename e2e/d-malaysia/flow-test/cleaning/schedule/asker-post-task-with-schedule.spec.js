/**
 * @description Post task with schedule
 *   case 1: <PERSON> <PERSON><PERSON> can post schedule task
 *   case 2: <PERSON><PERSON> pause task schedule
 *   case 3: Update task schedule
 *   case 4: Ask<PERSON> choose schedule step 2 and back remove schedule
 *   case 5: <PERSON> <PERSON><PERSON> can post schedule task and turn off
 * */

const {
  initData,
  tapId,
  tapText,
  swipe,
  expectIdToHaveText,
  waitForElement,
  postTask,
  expectElementNotExist,
  expectElementVisible,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { ASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/cleaning/schedule/asker-post-task-with-schedule.spec.js - Schedule', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 34 - New Asker can post schedule task', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('btnNextStep2');
    await tapId('whatIsWeekly');
    await waitForElement('Lịch Lặp Lại là gì?', 1000, 'text');
    await tapText('Đã hiểu');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await tapId('DayOfWeek3');
    await tapId('DayOfWeek4');
    await tapId('DayOfWeek5');
    await tapId('DayOfWeek6');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await waitForElement('Tab_Schedule', 1000);
    await tapId('Tab_Schedule');
    await waitForElement('taskStatus0', 1000);
    await expectIdToHaveText('taskStatus0', 'Đang hoạt động');
    await tapId('task_0');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await swipe('scrollScheduleUpdate', 'up');
    await tapText('Cập nhật');
    await tapId('task_0');
    await tapId('btnMore');
    await tapText('Hủy lặp lại');
    await waitForElement('Bạn chắc chắn hủy lịch làm việc này?', 1000, 'text');
    await tapText('Xác nhận');
    await expectElementNotExist('task_0');
  });

  it('LINE 78 - Asker pause task schedule', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('btnNextStep2');
    await tapId('whatIsWeekly');
    await waitForElement('Lịch Lặp Lại là gì?', 1000, 'text');
    await tapText('Đã hiểu');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await tapId('DayOfWeek3');
    await tapId('DayOfWeek4');
    await tapId('DayOfWeek5');
    await tapId('DayOfWeek6');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await waitForElement('Tab_Schedule', 1000);
    await tapId('Tab_Schedule');
    await waitForElement('taskStatus0', 1000);
    await expectIdToHaveText('taskStatus0', 'Đang hoạt động');
    await tapId('task_0');
    await tapId('btnMore');

    await tapText('Tạm dừng lặp lại');
    await tapText('Xác nhận');
    await expectIdToHaveText('taskStatus0', 'Tạm dừng');
    await tapId('task_0');
    await tapId('btnMore');
    await tapText('Tiếp tục lặp lại');
    await expectIdToHaveText('taskStatus0', 'Đang hoạt động');
  });

  it('LINE 114 - Update task schedule', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('btnNextStep2');
    await tapId('whatIsWeekly');
    await waitForElement('Lịch Lặp Lại là gì?', 1000, 'text');
    await tapText('Đã hiểu');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await tapId('DayOfWeek3');
    await tapId('DayOfWeek4');
    await tapId('DayOfWeek5');
    await tapId('DayOfWeek6');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await waitForElement('Tab_Schedule', 1000);
    await tapId('Tab_Schedule');
    await waitForElement('taskStatus0', 1000);
    await expectIdToHaveText('taskStatus0', 'Đang hoạt động');
    await tapId('task_0');

    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek2');
    await swipe('scrollScheduleUpdate', 'up');
    await tapText('Cập nhật');
    await expectElementVisible('task_0');
  });

  it('LINE 150 - Asker choose schedule step 2 and back remove schedule', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('btnNextStep2');
    await tapId('whatIsWeekly');
    await waitForElement('Lịch Lặp Lại là gì?', 1000, 'text');
    await tapText('Đã hiểu');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await waitForElement('Tab_Schedule', 1000);
    await tapId('Tab_Schedule');
    // Expect schedule not exists
    await expectElementNotExist('task_0');
    await expectElementNotExist('Đang sử dụng', 'text');
  });

  it('LINE 175 - New Asker can post schedule task and turn off', async () => {
    await postTask('postTaskServiceCLEANING', ADDRESS_KEY.MY);
    await tapId('chooseDuration-2');
    await expectIdToHaveText('lbPrice', '160,000 MYR/2h');
    await tapId('btnNextStep2');
    await tapId('whatIsWeekly');
    await waitForElement('Lịch Lặp Lại là gì?', 1000, 'text');
    await tapText('Đã hiểu');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('DayOfWeek0');
    await tapId('DayOfWeek1');
    await tapId('cbWeeklyRepeater');
    await tapId('btnNextStep3');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await waitForElement('Tab_Schedule', 1000);
    await tapId('Tab_Schedule');
    await expectElementNotExist('task_0');
    await expectElementNotExist('Đang sử dụng', 'text');
  });
});
