const { E2EHelpers } = require('../../../e2e.helpers');
const { initData, waitForElement, tapText } = require('../../../step-definition');
const { ASKER_MALAYSIA } = require('../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/a-login/login-fail.spec.js - Asker login fail', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, randomId: true }]);
  });

  it('LINE 19 - <PERSON>gin tải khoản không tồn tại', async () => {
    await E2EHelpers.onHaveLogin('23123123', '123456', '+60');
    await waitForElement('Tà<PERSON> khoản không tồn tại.', 500, 'text');
    await tapText('Đóng');
  });

  it('LINE 25 - Login sai mật khẩu', async () => {
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456789', ASKER_MALAYSIA.countryCode);
    await waitForElement('Số điện thoại hoặc mật khẩu không đúng. Vui lòng thử lại!', 500, 'text');
    await tapText('Đóng');
  });
});
