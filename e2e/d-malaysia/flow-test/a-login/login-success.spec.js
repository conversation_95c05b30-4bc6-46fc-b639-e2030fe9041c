/**
 * @description Asker login success
 * <AUTHOR>
 */

const { E2EHelpers } = require('../../../e2e.helpers');
const { initData, waitForElement } = require('../../../step-definition');
const { ASKER_MALAYSIA } = require('../../../helpers/constants');

const login = async (dataAsker) => {
  await E2EHelpers.onHaveLogin(dataAsker?.phone, '123456', '+60');
  await waitForElement(`Xin chào ${dataAsker?.name}`, 500, 'text');
};
describe('FILE: e2e/d-malaysia/flow-test/a-login/login-success.spec.js - Asker login success', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, randomId: true }]);
  });

  it('LINE 29 - Login with ASKER_VN', async () => {
    await login(ASKER_MALAYSIA);
  });
  it('LINE 32 - Login with ASKER_VN missing number 0', async () => {
    await login({ ...ASKER_MALAYSIA, phone: ASKER_MALAYSIA.phone.slice(1) });
  });
});
