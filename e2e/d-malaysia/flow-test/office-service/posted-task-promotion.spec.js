/**
 * case 1: <PERSON><PERSON><PERSON> 130 - <PERSON><PERSON> post task office cleaning with promotion code money "def123"
 * case 1: LINE 172 - <PERSON><PERSON> post task office cleaning with promotion code percentage "opq123"
 * case 1: LINE 191 - OFFICE_CLEANING promotion
 * */
const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  typeToTextField,
  expectIdToHaveText,
  waitForElement,
  swipe,
  expectElementVisible,
  typePromotionCode,
  scroll,
  ADDRESS_KEY,
} = require('../../../step-definition');

const {
  ASKER_MALAYSIA,
  ASKER_MALAYSIA_02,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const PROMO_ABC123 = {
  isoCode: MY,
  code: 'abc123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'CURRENT',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};
const PROMO_DEF123 = {
  isoCode: MY,
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};
const PROMO_GHK123 = {
  isoCode: MY,
  code: 'ghk123',
  value: 50000,
  target: 'TASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};
const PROMO_LMN123 = {
  isoCode: MY,
  code: 'lmn123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 1,
  maxValue: '',
};
const PROMO_OPQ123 = {
  isoCode: MY,
  code: 'opq123',
  value: 0.4,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'PERCENTAGE',
  limit: 100,
  maxValue: '',
};
const PROMO_OPQ789 = {
  isoCode: MY,
  code: 'opq789',
  value: 0.4,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'PERCENTAGE',
  limit: 100,
  maxValue: 30000,
};
describe('FILE: e2e/d-malaysia/flow-test/office-service/posted-task-promotion.spec.js - Post task promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, ASKER_MALAYSIA_02]);
    await initData('promotion/create-promotion-code', [
      PROMO_ABC123,
      PROMO_DEF123,
      PROMO_GHK123,
      PROMO_LMN123,
      PROMO_OPQ123,
      PROMO_OPQ789,
    ]);
    await initData('promotion/usersAppliedPromotion', {
      isoCode: MY,
      phone: ASKER_MALAYSIA_02.phone,
      promotionCode: PROMO_LMN123.code,
    });
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA_02.countryCode);
  });

  const pressCode = async (code) => {
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode(code);
  };

  // def123
  it('LINE 131 - Asker post task office cleaning with promotion code money "def123"', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.MY);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 MYR/2h');
    // step 2 & 3
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await swipe('scrollViewStep4', 'up');
    // VAT
    await expectElementVisible('txtPanel');
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await pressCode(PROMO_DEF123.code);
    await expectIdToHaveText('price', '169,400 MYR');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('Giảm giá', 'text');
    await expectElementVisible('Phương thức thanh toán', 'text');
    await expectIdToHaveText('finalCost', '169,400 RM');
  });
});
