/**
 * LINE 61 - New customer post office cleanind and sign in
 */

const {
  initData,
  tapId,
  tapText,
  typeToTextField,
  expectElementVisible,
  swipe,
  scroll,
  reloadApp,
  postTask,
  loginWithModal,
  ADDRESS_KEY,
} = require('../../../step-definition');
const { device } = require('detox');

const { ASKER_MALAYSIA, TASKER_MALAYSIA } = require('../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/office-service/posted-task-sign-in.spec.js - New User posted task Office cleaning', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, randomId: true }, TASKER_MALAYSIA]);
    await device.reloadReactNative();
  });

  it('LINE 51 - New customer post office cleanind and sign in', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.MY);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 MYR/2h');
    // step 2 & 3
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    await swipe('scrollViewStep4', 'up');
    // VAT
    await expectElementVisible('txtPanel', 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await expectElementVisible('txtWarningEmail', 'id');
    await typeToTextField('txtEmail', '<EMAIL>');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await tapText('Đăng việc');
    await loginWithModal(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
    await tapId('choosePaymentMethod');
    await tapId('paymentMethodBpay');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
  });
});
