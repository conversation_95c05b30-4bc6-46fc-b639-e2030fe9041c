/**
 * @description Old user post OFFICE_CLEANING task (1 test cases)
 *   case 2: LINE 68 - OFFICE_CLEANING Prepayment GO_PAY
 *   case 3: LINE 83 - OFFICE_CLEANING Prepayment DANA
 *   case 4: LINE 98 - OFFICE_CLEANING Prepayment QRIS
 * */
const {
  initData,
  tapId,
  tapText,
  postTask,
  scroll,
  expectElementVisible,
  ADDRESS_KEY,
} = require('../../../step-definition');
const { ASKER_MALAYSIA, TASKER_MALAYSIA } = require('../../../helpers/constants');

const { E2EHelpers } = require('../../../e2e.helpers');

const postTaskOfficeCleaning = async () => {
  await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.MY);
  await tapText('<PERSON>ị<PERSON> vụ theo Buổi/<PERSON>ày');
  // 100m2
  await tapId('area-0');
  await expectElementVisible('lbPrice', '224,400 MYR/2h');
  // step 2 & 3
  await tapId('btnNextStep2');
  await tapId('btnNextStep3');
};

describe('FILE: e2e/d-malaysia/flow-test/office-service/post-task-old-user.spec.js - Old User post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 55 - Post task OFFICE_CLEANING', async () => {
    await postTaskOfficeCleaning();
    await scroll('scrollViewStep4', 700, 'down', 0.5, 0.5);
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
  });
});
