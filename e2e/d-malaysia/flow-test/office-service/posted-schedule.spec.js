/**
 * case 1: LINE 73 - New customer post Office cleaning task and sign in and Status: DONE
 * */

const { E2EHel<PERSON> } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  typeToTextField,
  expectElementVisible,
  swipe,
  scroll,
  tapHeaderBack,
  postTask,
  ADDRESS_KEY,
} = require('../../../step-definition');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/office-service/posted-schedule.spec.js - New User posted task Office cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: MY,
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'NEW',
        typeOfValue: 'MONEY',
        limit: 100,
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  // Status: DONE
  it('LINE 63 - New customer post Office cleaning task and sign in and Status: DONE', async () => {
    await postTask('postTaskServiceOFFICE_CLEANING', ADDRESS_KEY.MY);
    await tapText('Dịch vụ theo Buổi/Ngày');
    // 100m2
    await tapId('area-0');
    await expectElementVisible('lbPrice', '204,000 MYR/2H');
    // step 2 & 3
    await tapId('btnNextStep2');
    await tapId('cbWeeklyRepeater');
    await tapText('Đồng ý');
    await tapId('btnNextStep3');
    // step 4
    await expectElementVisible('Vị trí làm việc', 'text');
    await expectElementVisible('Thông tin công việc', 'text');
    // VAT
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('txtPanel', 'id');
    await tapId('btnChangeCompanyEmailAddress');
    await typeToTextField('txtNameOfCompany', 'TNHH bTaskee');
    await typeToTextField('txtTaxCode', '0834567899');
    await scroll('scrollAddLocation2', 500, 'down', 0.5, 0.5);
    await typeToTextField('txtCompanyAddress', '123 Hung Vuong');

    await tapText('Gửi yêu cầu');
    await expectIdToHaveText('price', '224,400 MYR');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        status: 'DONE',
        taskerAccepted: [TASKER_MALAYSIA.phone],
        description: 'My Task',
      },
    ]);
    await tapHeaderBack();
    await tapText('Lịch sử');
    await expectElementVisible('taskMy Task');
  });
});
