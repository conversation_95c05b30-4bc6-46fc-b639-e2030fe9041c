/**
 * case 1: LINE 54 - Update datetime task
 */

const { E2<PERSON><PERSON>el<PERSON> } = require('../../../e2e.helpers');
const { initData, tapId, tapText, waitForElement, swipe } = require('../../../step-definition');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: 'OFFICE_CLEANING',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
};

describe('FILE: e2e/d-malaysia/flow-test/office-service/posted-task-update.spec.js -  Update task office cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 53 - Update datetime task', async () => {
    await tapText('Hoạt động');

    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('weekdays_3');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');

    await waitForElement('Không thể thay đổi công việc dọn dẹp văn phòng', 500, 'text');
  });
});
