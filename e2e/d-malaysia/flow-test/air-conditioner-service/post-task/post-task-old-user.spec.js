/**
 * @description Old user post AC task (1 test cases)
 *   case 1: <PERSON>IN<PERSON> 28 - <PERSON><PERSON> post task with Old User
 *   case 2: LIN<PERSON> 68 - AC Prepayment GO_PAY
 *   case 3: LINE 83 - AC Prepayment DANA
 *   case 4: LINE 98 - AC Prepayment QRIS
 * */
const {
  initData,
  tapId,
  tapText,
  postTask,
  expectIdToHaveText,
  tapIdAtIndex,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const {
  SERVICE_NAME: { AIR_CONDITIONER },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
} = require('../../../../helpers/constants');

const expect = require('chai').expect;
const { device } = require('detox');

const { E2EHelpers } = require('../../../../e2e.helpers');

const postTaskAC = async () => {
  await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', ADDRESS_KEY.MY, 'My Task');
  await tapText('Dưới 2HP');
  await expectIdToHaveText('lbPrice', '216,000 MYR/1h');
  await tapId('btnNextStep2');
  await tapId('btnNextStep3');
  await tapText('Tiếp tục');
};

describe('FILE: e2e/d-malaysia/flow-test/air-conditioner-service/post-task/post-task-old-user.spec.js - Old User post task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 55 - Already Loged-in and I want to post a Task for me', async () => {
    await postTaskAC();
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    const data = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(data.cities[0].country).to.equal(MY);
    expect(data.cities[0].city).to.equal('FT Kuala Lumpur');
  });

  it('LINE 116 - Post task with option refill gas', async () => {
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', ADDRESS_KEY.MY, 'My Task');
    await tapText('Dưới 2HP');
    await tapId('switchAC_Wall_Refill');
    await tapIdAtIndex('btnPlus', 0);
    await tapIdAtIndex('btnPlusGas', 0);
    await tapIdAtIndex('btnMinusGas', 0);
    await tapIdAtIndex('btnMinusGas', 0);
    await tapIdAtIndex('btnMinus', 0);
    await expectIdToHaveText('lbPrice', '216,000 MYR/1h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    const data = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(data.cities[0].country).to.equal(MY);
    expect(data.cities[0].city).to.equal('FT Kuala Lumpur');
  });

  it('LINE 142 - Book task addons Apartment', async () => {
    await initData('service/updateService', {
      isoCode: MY,
      serviceName: AIR_CONDITIONER,
      dataUpdate: {
        addons: [
          {
            text: {
              vi: 'Nhà',
              en: 'Apartment',
              ko: '아파트',
              id: 'อพาร์ทเม้นต์',
            },
            name: 'APARTMENT',
            cost: 30000,
          },
        ],
      },
    });

    await device.reloadReactNative();

    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', ADDRESS_KEY.MY, 'My Task');
    await tapId('chooseApartment');
    await tapText('Đã hiểu');

    await tapText('Dưới 2HP');
    await tapId('btnNextStep2');

    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    const data = await initData('task/getTaskByDescription', { description: 'My Task', isoCode: MY });
    expect(data.addons[0].name).to.equal('APARTMENT');
    expect(data.addons[0].cost).to.equal(30000);
  });
});
