/**
 * @description Asker accept task cleaning
 *   case 1:  Asker accept-task cleaning - UI Item task
 *   case 2:  Asker accept-task cleaning - UI task detail
 * */

const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  postTask,
  callService,
  expectElementNotExist,
  swipe,
  expectElementVisible,
  ADDRESS_KEY,
} = require('../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');

const {
  SERVICE_NAME: { AIR_CONDITIONER },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
} = require('../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/air-conditioner-service/tasker-accept-task.spec.js - Asker accept task cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 49 -  Asker accept-task air-conditioner - UI task detail', async () => {
    await postTask('postTaskServiceAIR_CONDITIONER_SERVICE', ADDRESS_KEY.MY, 'My Task');
    await tapText('Dưới 2HP');
    await expectIdToHaveText('lbPrice', '216,000 MYR/1h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapText('Tiếp tục');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('price', '216,000 MYR');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await tapId('serviceNameMy Task');
    await swipe('scrollTaskDetail', 'up');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '216,000 RM');
    await initData('service/updateServiceChannel', [
      {
        isoCode: MY,
        serviceName: AIR_CONDITIONER,
        taskerPhone: TASKER_MALAYSIA.phone,
      },
    ]);
    const task = await initData('task/getTaskByDescription', {
      description: 'My Task',
      isoCode: MY,
    });
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    await initData('task/updateTask', [
      {
        description: 'My Task',
        isoCode: MY,
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-my/accept', request);
    expect(response.status).to.equal('CONFIRMED');

    await device.reloadReactNative();
    await tapText('Hoạt động');
    await expectElementNotExist('Chưa có người nhận.', 'text');
    await tapId('taskerName0');
    await waitForElement('seeMore', 1000);
    await expectElementVisible('Nhắn tin', 'text');
  });
});
