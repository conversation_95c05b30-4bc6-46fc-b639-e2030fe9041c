/**
 * @description Change date time Air conditioner cleaning task
 *   case 1: <PERSON>IN<PERSON> 30 - Ask<PERSON> want to change task date time - POSTED
 *   case 2: <PERSON><PERSON><PERSON> 64 - Asker want to change task date time - WAITING
 *   case 3: LINE 95 - Asker want to change task date time - CONFIRMED
 *   case 4: LINE 123 - Asker want to change deep cleaning task time include promotion
 *   case 5: LINE 176 - Asker want to change cleaning task time include promotion
 * */

const { initData } = require('../../../../step-definition');
const { UpdateDateTimeHelpers } = require('../../../../helpers/task/updateDateTime.helpers');
const {
  SERVICE_NAME: { AIR_CONDITIONER },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const params = {
  serviceName: AIR_CONDITIONER,
  isoCode: MY,
  isCompany: true,
};

describe('FILE: e2e/d-malaysia/flow-test/air-conditioner-service/update-date-time/asker-change-date-time-and-keep-tasker.spec.js - Change date time  Air Conditioner task', () => {
  beforeEach(async () => {
    await initData('resetData');
  });

  it('LINE 28 - Asker want to change task WAITING_ASKER_CONFIRMATION and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskWaitingAndKeepTasker(params);
  });

  it('LINE 32 - Asker want to change task CONFIRMED and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTasker(params);
  });

  it('LINE 36 - Asker change date time task CONFIRMED and keep tasker conflict time', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerConflictTime(params);
  });

  it('LINE 40 - Asker want to change task CONFIRMED and keep tasker and not enough money', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerNotEnoughMoney(params);
  });

  // TODO: Open when update API
  it.skip('LINE 44 - Asker want to change task CONFIRMED and keep tasker reject', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerReject(params);
  });
});
