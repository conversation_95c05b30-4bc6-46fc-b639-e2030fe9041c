/**
 * @description Asker cancel task with promotion
 *   case 1: LINE 63 - Ask<PERSON> cancel the task is not include fee
 *   case 2: LINE 141 - Asker cancel the task include fee
 * */

const { initData } = require('../../../../step-definition');
const { E2EHelpers } = require('../../../../e2e.helpers');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');
const expect = require('chai').expect;

const {
  SERVICE_NAME: { AIR_CONDITIONER },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const moment = require('moment');

const ASKER = {
  isoCode: MY,
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
};
const TASKER = {
  isoCode: MY,
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASK = {
  removeRequirements: true,
  isoCode: MY,
  serviceName: AIR_CONDITIONER,
  askerPhone: ASKER.phone,
  description: 'My task',
};

const PROMOTION_01 = {
  isoCode: MY,
  code: 'def123',
  value: 50000,
  target: 'ASKER',
  typeOfPromotion: 'NEW',
  typeOfValue: 'MONEY',
  limit: 100,
  maxValue: '',
};

describe('FILE: e2e/d-malaysia/flow-test/air-conditioner-service/cancel-task/asker-cancel-task-promotion.spec.js - Asker cancel task with promotion', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);
    await initData('task/createTask', [TASK]);

    await initData('promotion/create-promotion-code', PROMOTION_01);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', '+60');
  });

  it('LINE 58 - Asker cancel the task is not include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);

    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: MY,
      tasker: TASKER,
      task: TASK,
      asker: ASKER,
      taskUsedPromotion: true,
    });

    const promotionHistory = await initData('promotion/getPromotionHistory', {
      promotionCode: PROMOTION_01.code,
      isoCode: PROMOTION_01.isoCode,
    });

    await expect(promotionHistory).to.equal(null);
  });

  it('LINE 136 - Asker cancel the task include fee', async () => {
    await initData('promotion/apply-promotion-code-to-task', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        promotionCode: PROMOTION_01.code,
      },
    ]);

    await initData('task/acceptedTask', [
      { isoCode: MY, description: TASK.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(30, 'minute').toDate(),
          status: 'CONFIRMED',
        },
      },
    ]);

    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: MY,
      tasker: TASKER,
      task: TASK,
      asker: ASKER,
      taskUsedPromotion: true,
      cancelFee: 64800,
    });

    const promotionHistory = await initData('promotion/getPromotionHistory', {
      promotionCode: PROMOTION_01.code,
      isoCode: PROMOTION_01.isoCode,
    });

    await expect(promotionHistory.promotionCode).to.equal(PROMOTION_01.code);
    await expect(promotionHistory.name).to.equal(ASKER.name);
  });
});
