/**
 * @description Asker cancel task
 * */

const { initData } = require('../../../../step-definition');
const moment = require('moment');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { CancelTaskHelpers } = require('../../../../helpers/task/cancelTask.helpers');
const expect = require('chai').expect;

const {
  SERVICE_NAME: { AIR_CONDITIONER },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const ASKER = {
  isoCode: MY,
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
  email: '<EMAIL>',
  countryCode: '+60',
};
const TASKER = {
  isoCode: MY,
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
  countryCode: '+60',
};
const TASKER_02 = {
  isoCode: MY,
  phone: '0834567892',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASK = {
  isoCode: MY,
  serviceName: AIR_CONDITIONER,
  askerPhone: '0834567890',
  description: 'Don dep nha 01',
};
const TASK_02 = {
  isoCode: MY,
  serviceName: AIR_CONDITIONER,
  askerPhone: '0834567890',
  description: 'Don dep nha 02',
};
const TASK_03 = {
  _id: 'x65e82458dab676918b287f8f',
  isoCode: MY,
  serviceName: AIR_CONDITIONER,
  askerPhone: '0834567890',
  description: 'Don dep nha 03',
};
const TASK_04 = {
  isoCode: MY,
  serviceName: AIR_CONDITIONER,
  askerPhone: '0834567890',
  description: 'Don dep nha 04',
};

describe('FILE: e2e/d-malaysia/flow-test/air-conditioner-service/cancel-task/asker-cancel-task.spec.js - Asker cancel task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER, TASKER_02]);
    await initData('task/createTask', [TASK, TASK_02, TASK_03, TASK_04]);
    const tasker = await initData('user/getUserByPhone', { phone: TASKER.phone, countryCode: TASKER.countryCode });

    await initData('task/updateTask', [
      {
        description: 'Don dep nha 01',
        isoCode: MY,
        dataUpdate: {
          status: 'WAITING_ASKER_CONFIRMATION',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
      {
        description: 'Don dep nha 02',
        isoCode: MY,
        dataUpdate: {
          status: 'CONFIRMED',
          acceptedTasker: [{ taskerId: tasker._id, name: tasker.name, avatar: tasker.avatar }],
        },
      },
    ]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456', ASKER.countryCode);
  });

  it('LINE 105 - Asker cancel posted task', async () => {
    await CancelTaskHelpers.cancelTaskPosted({ isoCode: MY, task: TASK_04, asker: ASKER });
  });

  it('LINE 165 - Asker cancel confirmed cleaning task before working time', async () => {
    await CancelTaskHelpers.cancelTaskConfirmedAndRebook({ isoCode: MY, task: TASK_02, asker: ASKER });
  });

  it('LINE 248 - Asker cancel confirmed cleaning task before working time, find same task for Tasker', async () => {
    await CancelTaskHelpers.cancelTaskConfirmed({ isoCode: MY, task: TASK_02, asker: ASKER });
  });

  it('LINE 354 - Asker cancel confirmed cleaning task with fee 20k', async () => {
    await initData('task/acceptedTask', [
      { isoCode: TASK.isoCode, description: TASK.description, taskerAccepted: [TASKER.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: TASK.isoCode,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(2, 'hour').toDate(),
        },
      },
    ]);
    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: MY,
      tasker: TASKER,
      task: TASK,
      asker: ASKER,
      cancelFee: 20000,
    });
  });

  it('LINE 540 - Asker cancel task and input the reason', async () => {
    await CancelTaskHelpers.cancelTaskWithOtherReason({ isoCode: MY, tasker: TASKER, task: TASK_04, asker: ASKER });
  });

  it('LINE 540 - Asker cancel task moving and cleaning', async () => {
    await CancelTaskHelpers.cancelTaskWithFeeCancel({
      isoCode: MY,
      tasker: TASKER,
      task: TASK_03,
      asker: ASKER,
      cancelFee: 0,
    });
    const dataTask = await initData('task/getTaskByDescription', {
      isoCode: TASK_03.isoCode,
      description: TASK_03?.description,
    });
    await expect(dataTask.status).to.equal('CANCELED');
  });

  it('LINE 540 - Asker cancel task moving and cleaning confirmed', async () => {
    await initData('task/acceptedTask', [
      {
        isoCode: TASK_03.isoCode,
        description: TASK_03.description,
        taskerAccepted: [TASKER.phone],
        status: 'CONFIRMED',
      },
    ]);
    await CancelTaskHelpers.cancelTaskConfirmedAndRebook({ isoCode: MY, task: TASK_03, asker: ASKER });
  });
});
