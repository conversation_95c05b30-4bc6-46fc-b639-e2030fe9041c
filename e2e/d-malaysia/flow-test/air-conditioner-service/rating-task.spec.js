/**
 * @description Rating task (1 test cases)
 *   case 1: LINE 34 - Task have been done by Tasker. Asker rating information
 * */
const { initData, tapId, expectElementVisible, expectIdToHaveText, swipe } = require('../../../step-definition');

const { E2EHelpers } = require('../../../e2e.helpers');

const {
  SERVICE_NAME: { AIR_CONDITIONER },
  ISO_CODE: { MY },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
} = require('../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/air-conditioner-service/rating-task.spec.js - Asker rating task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [
      {
        isoCode: MY,
        serviceName: AIR_CONDITIONER,
        askerPhone: ASKER_MALAYSIA.phone,
        description: 'Don dep nha 01',
        status: 'DONE',
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 43 - Task have been done by Tasker. Asker rating information', async () => {
    await expectElementVisible('Đánh giá', 'text');
    await tapId('star1');
    await expectIdToHaveText('lbStarStatus', 'QUÁ TỆ');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì bạn chưa hài lòng ?');
    await tapId('star2');
    await expectIdToHaveText('lbStarStatus', 'TỆ');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì bạn chưa hài lòng ?');
    await tapId('star3');
    await expectIdToHaveText('lbStarStatus', 'TRUNG BÌNH');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì bạn chưa hài lòng ?');
    await tapId('star4');
    await expectIdToHaveText('lbStarStatus', 'KHÁ');
    await expectIdToHaveText('lbQuestionTitle', 'Điều gì cần làm tốt hơn ?');
    await tapId('star5');
    await expectIdToHaveText('lbStarStatus', 'TỐT');
    await expectIdToHaveText('lbQuestionTitle', 'Lời khen của bạn ?');
    await swipe('scrollRating', 'up');
    await tapId('btnRating');
  });
});
