const {
  initData,
  tapId,
  typeToTextField,
  expectIdToHaveText,
  waitForElement,
  tapText,
  fillActiveCode,
  expectElementVisible,
  tapIdAtIndex,
  signUpWithModal,
  reloadApp,
} = require('../../../step-definition');
const { device } = require('detox');

const { ASKER_MALAYSIA } = require('../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/aa-sign-up/sign-up.spec.js - Sign up Asker', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, randomId: true }]);
    await device.reloadReactNative();
    try {
      await tapId('cancelVerify');
    } catch (error) {}
  });

  it('LINE 47 - New Asker sign up without sign up promotion code', async () => {
    // Click login button in home screen and open modal
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');
    await signUpWithModal('bill gate', '0123456789', '<EMAIL>', '+60');
    await waitForElement('Tab_Activity', 500);
  });

  it('LINE 55 - New Asker sign up with exist phone number', async () => {
    // TODO: Run on Android
    // Click login button in home screen and open modal
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');

    await typeToTextField('txtName', 'Nathan');
    await typeToTextField('txtPhone', ASKER_MALAYSIA.phone);
    await typeToTextField('txtEmail', '<EMAIL>');
    await expectElementVisible('Số điện thoại đã được sử dụng. Vui lòng chọn số khác!', 'text');
  });

  it('LINE 73 - New Asker sign up with exists email', async () => {
    // await initData('user/updateUser', [USER_ASKER_DATA_UPDATE]);

    // Click login button in home screen and open modal
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');

    await typeToTextField('txtName', 'Nathan');
    await typeToTextField('txtPhone', '0123456788');
    await typeToTextField('txtEmail', ASKER_MALAYSIA.email);
    await expectElementVisible('Email đã được đăng ký.', 'text');
  });

  it('LINE 92 - New Asker sign up with short name and check referral code', async () => {
    const PHONE = '0123456799';
    await waitForElement('homeHeaderBtnLogin', 500);
    await tapId('homeHeaderBtnLogin');

    // Modal sign up
    await waitForElement('ModalBtnSignUp', 500);
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtName', 'Linh');
    await typeToTextField('txtPhone', PHONE);
    await typeToTextField('txtEmail', '<EMAIL>');
    await tapId('checkboxPolicy');
    await tapId('btnSignup');
    await waitForElement('Xác thực tài khoản', 1000, 'text');
    await fillActiveCode(PHONE, '+60');
    await typeToTextField('txtPassword', '113114115');
    await typeToTextField('txtSecondPassword', '113114115');
    try {
      await tapIdAtIndex('btnSavePassword', 0);
    } catch (e) {
      await tapIdAtIndex('btnSavePassword', 1);
    }
    await waitForElement('Tab_Activity', 500);
    await tapText('Tài khoản');
    await tapText('Săn quà giới thiệu');
    const referralCode = await initData('user/getUserByPhone', { phone: PHONE, countryCode: '+60' });
    await expectIdToHaveText('referralCode', referralCode.referralCode?.toUpperCase());
  });
});
