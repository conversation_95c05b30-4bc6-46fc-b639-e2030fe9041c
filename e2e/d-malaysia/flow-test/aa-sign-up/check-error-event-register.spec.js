/**
 * @Filename: flow-test/sign-up/check-error-event-register.spec.js
 * @Description: Sign up check error event
 * @CreatedAt: 08/12/2020 11:06
 * @Author: <PERSON><PERSON><PERSON>oan
 * @UpdatedAt: 17/02/2021 15:30
 * @UpdatedBy: Huu<PERSON>oan
 **/

/**
 *   Case 1: LINE 31 Check phone number error
 *   Case 2: LINE 46 Check name null
 *   Case 3: LINE 66 Check email error
 **/

const {
  tapId,
  typeToTextField,
  clearTextInput,
  expectElementNotExist,
  initData,
  waitForElement,
} = require('../../../step-definition');
const { device } = require('detox');

describe('FILE: e2e/d-malaysia/flow-test/aa-sign-up/check-error-event-register.spec.js - Check error event', () => {
  beforeEach(async () => {
    await initData('resetData', {});
    await device.reloadReactNative();
  });

  it('LINE 32 - Check phone number error', async () => {
    await tapId('homeHeaderBtnLogin');
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtPhone', '00221234567');
    await tapId('txtName');
    await waitForElement('Số điện thoại không đúng', 500, 'text');
    await clearTextInput('txtPhone');
    await typeToTextField('txtPhone', '01221');
    await tapId('txtName');
    await waitForElement('Số điện thoại không đúng', 500, 'text');
    await clearTextInput('txtPhone');
    await typeToTextField('txtPhone', '0123456789');
    await expectElementNotExist('Số điện thoại không đúng', 'text');
  });

  it('LINE 48 - Check name null', async () => {
    await tapId('homeHeaderBtnLogin');
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtPhone', '00221234567');
    await waitForElement('Số điện thoại không đúng', 500, 'text');
    await tapId('txtPhone');
    await typeToTextField('txtName', 'Kaiser');
    await clearTextInput('txtName');
    await waitForElement('Thông tin bắt buộc', 500, 'text');
    await typeToTextField('txtName', 'Kaiser');
    await expectElementNotExist('Thông tin bắt buộc', 'text');
    await clearTextInput('txtPhone');
    await typeToTextField('txtPhone', '0123456789');
    await expectElementNotExist('Thông tin bắt buộc', 'text');
    await expectElementNotExist('Số điện thoại không đúng', 'text');
  });

  it('LINE 69 - Check email error', async () => {
    await tapId('homeHeaderBtnLogin');
    await tapId('ModalBtnSignUp');
    await typeToTextField('txtPhone', '00221234567');
    await typeToTextField('txtEmail', 'Kaiser');
    await waitForElement('Số điện thoại không đúng', 500, 'text');
    await tapId('txtPhone');
    await waitForElement('Email không hợp lệ', 500, 'text');
    await clearTextInput('txtEmail');
    await typeToTextField('txtEmail', '<EMAIL>');
    await clearTextInput('txtPhone');
    await typeToTextField('txtPhone', '0123456789');
    await expectElementNotExist('Email không hợp lệ', 500, 'text');
    await expectElementNotExist('Số điện thoại không đúng', 'text');
  });
});
