/**
 * @description Asker done task and rating
 *   case 1: Asker can not see feedback NOT_COMMING
 *   case 2: Asker rate 1 star, add Taskers to blacklist
 *   case 3: Asker rate 3 star, add Taskers to blacklist
 *   case 4: Asker rate 4 star, remove Taskers from blackList
 *   case 5: Asker see notify warning will rate for tasker
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  waitForElement,
  expectElementNotExist,
  typeToTextField,
  swipe,
} = require('../../../../step-definition');
const expect = require('chai').expect;
const {
  SERVICE_NAME: { DEEP_CLEANING },
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  TASKER_MALAYSIA_02,
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: DEEP_CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
};
describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/rate-tasker/rate-tasker.spec.js - Asker done task and rating', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA, TASKER_MALAYSIA_02]);
    await initData('task/createTask', [TASK]);
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        status: 'DONE',
        taskerAccepted: [TASKER_MALAYSIA.phone, TASKER_MALAYSIA_02.phone],
        description: TASK.description,
        leaderPhone: TASKER_MALAYSIA_02.phone,
      },
    ]);

    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 69 - Asker can not see feedback NOT_COMMING', async () => {
    await tapId('star1');
    await expectElementNotExist('btnReasonNOT_COMING');
  });

  it('LINE 74 - DeepCleaning Asker rate 1 star, add Taskers to blacklist', async () => {
    await tapId('star1');
    await swipe('scrollRating', 'up');
    await typeToTextField('txtReview', 'Good job');
    await tapId('btnReasonOTHER');
    await tapId('btnReasonOTHER');
    await tapId('btnRating');
    const tasker1 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const tasker2 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA_02.phone,
      countryCode: TASKER_MALAYSIA_02.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(asker.blackList).to.include(tasker1._id);
    expect(asker.blackList).to.include(tasker2._id);
  });

  it('LINE 88 - Asker rate 3 star, add Taskers to blacklist', async () => {
    await tapId('star3');
    await swipe('scrollRating', 'up');
    await typeToTextField('txtReview', 'Good job');
    await tapId('btnReasonOTHER');
    await tapId('btnReasonOTHER');
    await tapId('btnRating');
    const tasker1 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    const tasker2 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA_02.phone,
      countryCode: TASKER_MALAYSIA_02.countryCode,
    });
    const asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(asker.blackList).to.include(tasker1._id);
    expect(asker.blackList).to.include(tasker2._id);
  });

  it('LINE 102 - Asker rate 4 star, remove Taskers from blackList', async () => {
    await initData('update-user/updateAskerBlackList', {
      isoCode: ASKER_MALAYSIA.isoCode,
      askerPhone: ASKER_MALAYSIA.phone,
      taskerPhone: [TASKER_MALAYSIA.phone, TASKER_MALAYSIA_02.phone],
    });
    let tasker1 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    let tasker2 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA_02.phone,
      countryCode: TASKER_MALAYSIA_02.countryCode,
    });
    let asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    expect(asker.blackList).to.include(tasker1._id);
    expect(asker.blackList).to.include(tasker2._id);

    await tapId('star4');
    await tapId('btnReasonIMPROVE_ON_TIME');
    await tapId('btnRating');
    tasker1 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    tasker2 = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA_02.phone,
      countryCode: TASKER_MALAYSIA_02.countryCode,
    });
    asker = await initData('user/getUserByPhone', {
      phone: ASKER_MALAYSIA.phone,
      countryCode: ASKER_MALAYSIA.countryCode,
    });
    const array = asker.blackList || [];
    expect(array).to.not.include(tasker1._id);
    expect(array).to.not.include(tasker2._id);
  });

  it('LINE 127 - Asker see notify warning will rate for tasker', async () => {
    await expectElementNotExist('Điều gì bạn chưa hài lòng ?', 'text');
    await waitForElement('Đánh giá này sẽ áp dụng cho cả nhóm.', 1000, 'text');
    await tapId('star5');
    await tapId('btnRating');
  });

  // Tiền tip sẽ được chia đều cho Tasker
  // Nếu chia ra số lẻ, không chia hết cho 1000 thì leader dẽ được nhận nhiều hơn member
  it('LINE 136 - Asker Tip Tasker', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await typeToTextField('inputTip', '8');
    await tapId('btnRating');

    const transaction = await initData('user/findFATransaction', {
      phone: ASKER_MALAYSIA.phone,
      isoCode: ASKER_MALAYSIA.isoCode,
      accountType: 'M',
      type: 'C',
      amount: 8000,
    });
    expect(transaction.length).to.equal(1);
    expect(transaction[0].amount).to.equal(8000);

    const transaction2 = await initData('user/findFATransaction', {
      phone: TASKER_MALAYSIA.phone,
      isoCode: TASKER_MALAYSIA.isoCode,
      accountType: 'P',
      type: 'D',
      amount: 4000,
    });
    expect(transaction2.length).to.equal(1);
    expect(transaction2[0].amount).to.equal(4000);

    const data1 = await initData('user/find-faccount', {
      phone: TASKER_MALAYSIA.phone,
      isoCode: TASKER_MALAYSIA.isoCode,
    });
    expect(data1.FMainAccount).to.equal(0);
    expect(data1.Promotion).to.equal(4000);

    // Leader
    const transaction3 = await initData('user/findFATransaction', {
      phone: TASKER_MALAYSIA_02.phone,
      isoCode: TASKER_MALAYSIA_02.isoCode,
      accountType: 'P',
      type: 'D',
      amount: 4000,
    });
    expect(transaction3.length).to.equal(1);
    expect(transaction3[0].amount).to.equal(4000);

    const data2 = await initData('user/find-faccount', {
      phone: TASKER_MALAYSIA_02.phone,
      isoCode: TASKER_MALAYSIA_02.isoCode,
    });
    expect(data2.FMainAccount).to.equal(0);
    expect(data2.Promotion).to.equal(4000);
  });

  // Tiền tip sẽ được chia đều cho Tasker
  // Nếu chia ra số lẻ, không chia hết cho 1000 thì leader dẽ được nhận nhiều hơn member
  it('LINE 193 - Asker Tip Tasker leader', async () => {
    await initData('update-user/financialAccount', [
      {
        phone: ASKER_MALAYSIA.phone,
        isoCode: ASKER_MALAYSIA.isoCode,
        financialAccountData: {
          FMainAccount: 100000,
        },
      },
    ]);
    await tapId('star5');
    await swipe('scrollRating', 'up');
    await typeToTextField('inputTip', '7');
    await tapId('btnRating');

    const transaction = await initData('user/findFATransaction', {
      phone: ASKER_MALAYSIA.phone,
      isoCode: ASKER_MALAYSIA.isoCode,
      accountType: 'M',
      type: 'C',
      amount: 7000,
    });
    expect(transaction.length).to.equal(1);
    expect(transaction[0].amount).to.equal(7000);

    const transaction2 = await initData('user/findFATransaction', {
      phone: TASKER_MALAYSIA.phone,
      isoCode: TASKER_MALAYSIA.isoCode,
      accountType: 'P',
      type: 'D',
      amount: 3000,
    });
    expect(transaction2.length).to.equal(1);
    expect(transaction2[0].amount).to.equal(3000);

    const data1 = await initData('user/find-faccount', {
      phone: TASKER_MALAYSIA.phone,
      isoCode: TASKER_MALAYSIA.isoCode,
    });
    expect(data1.FMainAccount).to.equal(0);
    expect(data1.Promotion).to.equal(3000);

    // Leader
    const transaction3 = await initData('user/findFATransaction', {
      phone: TASKER_MALAYSIA_02.phone,
      isoCode: TASKER_MALAYSIA_02.isoCode,
      accountType: 'P',
      type: 'D',
      amount: 4000,
    });
    expect(transaction3.length).to.equal(1);
    expect(transaction3[0].amount).to.equal(4000);

    const data2 = await initData('user/find-faccount', {
      phone: TASKER_MALAYSIA_02.phone,
      isoCode: TASKER_MALAYSIA_02.isoCode,
    });
    expect(data2.FMainAccount).to.equal(0);
    expect(data2.Promotion).to.equal(4000);
  });
});
