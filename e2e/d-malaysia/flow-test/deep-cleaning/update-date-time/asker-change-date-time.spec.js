/**
 * @description Change datetime Deep Cleaning task
 *   case 1: Asker want to change task datetime - POSTED
 *   case 2: Asker want to change task datetime - WAITING
 *   case 3: Asker want to change task datetime - CONFIRMED
 *   case 4: Asker want to change deep cleaning task time include promotion
 *   case 5: Asker want to change deep cleaning task time include promotion 156m2 720k
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  expectElementVisible,
  swipe,
  postTask,
  waitForElement,
  expectElementNotVisible,
  typePromotionCode,
  selectTime,
  expectElementVisibleAtIndex,
  selectTime24h,
} = require('../../../../step-definition');
const moment = require('moment');
const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  TASKER_MALAYSIA_02,
  SERVICE_NAME: { DEEP_CLEANING },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: DEEP_CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
};

const checkTimeChangeSuccess = async () => {
  await waitForElement('Cập nhật thành công', 500, 'text');
  await tapText('Đóng');
  await tapId('taskMy Task');
  await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
  await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
  await expectIdToHaveText('duration', '4 giờ, 16:00 đến 20:00');
};

describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/update-date-time/asker-change-date-time.spec.js - Change datetime Deep Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA, TASKER_MALAYSIA_02]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 76 - Asker want to change task datetime - POSTED', async () => {
    await initData('task/createTask', [TASK]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Mới đăng', 0, 'text');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('weekdays_3');
    await selectTime24h(22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await waitForElement(
      'Vui lòng chọn giờ làm khác. bTaskee chỉ hỗ trợ công việc từ 06:00 đến 23:00 hàng ngày.',
      500,
      'text',
    );
    await tapText('Đóng');
    await selectTime24h(16, 22);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');

    await checkTimeChangeSuccess();
  });

  it('LINE 100 - Asker want to change task datetime - WAITING', async () => {
    await initData('task/createTask', [TASK]);
    await initData('task/acceptedTask', [
      {
        status: 'WAITING_ASKER_CONFIRMATION',
        taskerAccepted: [TASKER_MALAYSIA.phone],
        description: TASK.description,
        isoCode: MY,
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Chờ người nhận', 0, 'text');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapId('weekdays_3');
    await selectTime24h(16);
    await tapText('Đồng ý');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');

    await checkTimeChangeSuccess();
  });

  it('LINE 144 - Asker want to change deep cleaning task time include promotion 156m2 720k', async () => {
    const PROMOTION = {
      isoCode: MY,
      code: 'abc123',
      value: 50000,
      target: 'ASKER',
      typeOfPromotion: 'NEW',
      typeOfValue: 'MONEY',
      limit: 100,
      taskStartDate: moment().subtract(1, 'hour').toDate(),
      taskEndDate: moment().add(4, 'd').toDate(),
    };
    await initData('promotion/create-promotion-code', [PROMOTION]);
    await postTask('postTaskServiceDEEP_CLEANING');
    await tapId('area100');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('price', '720,000 MYR');
    await waitForElement('promotionCode', 500);
    await tapId('promotionCode');
    await typePromotionCode('abc123');
    await expectIdToHaveText('txtPromotionCode', PROMOTION.code);
    await expectIdToHaveText('originPrice', '720,000 MYR');
    await expectIdToHaveText('price', '670,000 MYR');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await tapId('serviceNameMy Task');

    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    const nextDay = moment().add(5, 'd');
    if (moment().add(1, 'd').date() !== 1 && moment().month() !== nextDay.month()) {
    }
    await tapId(`days_${nextDay.date()}`);
    await expectIdToHaveText('lbPrice', '720,000 MYR/3h');
    await tapId('btnUpdateDateTime');
    await tapText('Đồng ý');

    await waitForElement('Cập nhật thành công', 500, 'text');
    await tapText('Đóng');
    await tapId('taskMy Task');
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('finalCost', '720,000 RM');
  });

  it('LINE 191 - Go update task for the first time, no call the api get price', async () => {
    await initData('task/createTask', [TASK]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Mới đăng', 0, 'text');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');

    await expectElementNotVisible('btnUpdateDateTime');
    await selectTime(10, true, 'AM');
    await tapText('Đồng ý');
    await expectElementVisible('btnUpdateDateTime');
  });
});
