const { initData } = require('../../../../step-definition');
const { UpdateDateTimeHelpers } = require('../../../../helpers/task/updateDateTime.helpers');
const {
  SERVICE_NAME: { DEEP_CLEANING },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const params = {
  serviceName: DEEP_CLEANING,
  isoCode: MY,
  isTaskLeader: true,
};
describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/update-date-time/asker-change-date-time-and-keep-tasker.spec.js - Change datetime  Deep Cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
  });
  it('LINE 18 - Asker want to change task Deep Cleaning WAITING_ASKER_CONFIRMATION and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskWaitingAndKeepTasker(params);
  });

  it('LINE 22 - Asker want to change task Deep Cleaning CONFIRMED and keep tasker', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTasker(params);
  });

  it('LINE 26 - Asker change date time task Deep Cleaning CONFIRMED and keep tasker conflict time', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerConflictTime(params);
  });

  it('LINE 30 - Asker want to change task Deep Cleaning CONFIRMED and keep tasker and not enough money', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerNotEnoughMoney(params);
  });

  // TODO: Open when update API
  it.skip('LINE 34 - Asker want to change task Deep Cleaning CONFIRMED and keep tasker reject', async () => {
    await UpdateDateTimeHelpers.changeTaskConfirmedAndKeepTaskerReject(params);
  });
});
