/**
 * @description Update note
 *   case 1: Asker update note normal task
 *   case 2: Asker update note prepay task
 * */

const { E2EHel<PERSON> } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  expectIdToHaveText,
  waitForElement,
  swipe,
  typeToTextField,
  expectElementVisibleAtIndex,
} = require('../../../../step-definition');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  SERVICE_NAME: { DEEP_CLEANING },
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: DEEP_CLEANING,
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/update-date-time/update-note.spec.js - Update note', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 51 - Asker update note normal task', async () => {
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Mới đăng', 0, 'text');
    await tapId('taskMy Task');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await typeToTextField('taskNote', 'Lau dọn them phong ve sinh tang 2\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn them phong ve sinh tang 2\n', 500, 'text');
  });

  it('LINE 68 - Asker update note prepay task', async () => {
    await initData('task/updateTask', [
      {
        isoCode: MY,
        description: TASK.description,
        dataUpdate: {
          payment: {
            method: 'SHOPEE_PAY',
          },
          status: 'POSTED',
        },
      },
    ]);
    await tapText('Hoạt động');
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectElementVisibleAtIndex('Mới đăng', 0, 'text');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await typeToTextField('taskNote', 'Lau dọn them phong ve sinh tang 2\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn them phong ve sinh tang 2\n', 500, 'text');
  });
});
