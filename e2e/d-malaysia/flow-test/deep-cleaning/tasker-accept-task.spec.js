/**
 * @description Tasker accept deep cleaning task
 *   case 1: Tasker accept laundry task
 *   case 2: See List Accepted Tasker in task detail
 *   case 3: See tasker detail deep cleaning task
 * */

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  initData,
  tapId,
  expectElementNotExist,
  tapText,
  expectIdToHaveText,
  waitForElement,
  tapHeaderBack,
} = require('../../../step-definition');
const expect = require('chai').expect;
const {
  ASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASKER1 = {
  isoCode: MY,
  phone: '0834567891',
  name: 'Tasker 01',
  type: 'TASKER',
  status: 'ACTIVE',
  avgRating: 5,
  taskDone: 100,
  oldUser: true,
};
const TASKER2 = {
  isoCode: MY,
  phone: '0834567892',
  name: 'Tasker 02',
  type: 'TASKER',
  status: 'ACTIVE',
  avgRating: 5,
  taskDone: 101,
  oldUser: true,
};
const TASKER3 = {
  isoCode: MY,
  phone: '0834567893',
  name: 'Tasker 03',
  type: 'TASKER',
  status: 'ACTIVE',
  avgRating: 5,
  taskDone: 102,
  oldUser: true,
};
const TASK = {
  isoCode: MY,
  serviceName: 'DEEP_CLEANING',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  viewedTaskers: [TASKER1.phone, TASKER2.phone, TASKER3.phone],
  autoChooseTasker: true,
};

describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/tasker-accept-task.spec.js - Tasker accept deep cleaning task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER1, TASKER2, TASKER3]);
    await initData('service/updateServiceChannel', [
      { isoCode: MY, serviceName: 'DEEP_CLEANING', taskerPhone: TASKER1.phone },
      { isoCode: MY, serviceName: 'DEEP_CLEANING', taskerPhone: TASKER2.phone },
      { isoCode: MY, serviceName: 'DEEP_CLEANING', taskerPhone: TASKER3.phone },
    ]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });
  it('LINE 80 - Tasker accept deep cleaning task', async () => {
    await initData('service/updateServiceChannel', [
      { isoCode: MY, serviceName: 'DEEP_CLEANING', taskerPhone: TASKER1.phone },
      { isoCode: MY, serviceName: 'DEEP_CLEANING', taskerPhone: TASKER2.phone },
    ]);
    await initData('update-user/financialAccount', [
      {
        phone: TASKER1.phone,
        isoCode: TASKER1.isoCode,
        financialAccountData: {
          Promotion: 1000000,
        },
      },
      {
        phone: TASKER2.phone,
        isoCode: TASKER2.isoCode,
        financialAccountData: {
          Promotion: 1000000,
        },
      },
      {
        phone: TASKER3.phone,
        isoCode: TASKER3.isoCode,
        financialAccountData: {
          Promotion: 1000000,
        },
      },
    ]);
    await initData('task/acceptedTask', [
      {
        isoCode: MY,
        status: 'CONFIRMED',
        taskerAccepted: [TASKER3.phone, TASKER2.phone, TASKER1.phone],
        description: TASK.description,
        leaderPhone: TASKER3.phone,
      },
    ]);
    const task = await initData('task/getTaskByDescription', { description: TASK.description, isoCode: MY });
    await expect(task.status).to.equal('CONFIRMED');
    await tapText('Hoạt động');
    await expectElementNotExist('taskerName0');
    await tapId('Tab_Activity');
    await tapId('taskMy Task');
    await waitForElement(`tasker-${TASKER3.name}`, 500);
    await tapId(`tasker-${TASKER3.name}`);
    await expectIdToHaveText('taskerName', TASKER3.name);
    await expectIdToHaveText('taskDone', TASKER3.taskDone.toString());
    await tapHeaderBack();
    await waitForElement(`tasker-${TASKER2.name}`, 500);
    await tapId(`tasker-${TASKER2.name}`);
    await expectIdToHaveText('taskerName', TASKER2.name);
    await expectIdToHaveText('taskDone', TASKER2.taskDone.toString());
    await tapHeaderBack();
    await waitForElement(`tasker-${TASKER1.name}`, 500);
    await tapId(`tasker-${TASKER1.name}`);
    await expectIdToHaveText('taskerName', TASKER1.name);
    await expectIdToHaveText('taskDone', TASKER1.taskDone.toString());
  });
});
