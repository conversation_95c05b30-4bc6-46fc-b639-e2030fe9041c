const {
  initData,
  tapId,
  postTask,
  expectIdToHaveText,
  loginWithModal,
  signUpWithModal,
  forgotPasswordWithModal,
  swipe,
  expectElementVisible,
  tapText,
  expectIdToHaveTextAtIndex,
  reloadApp,
  tapTask,
  ADDRESS_KEY,
} = require('../../../../step-definition');

const { device } = require('detox');
const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/post-task/post-task-and-sign-in.spec.js - New User post deep-cleaning task before sign in', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('resetData');
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, randomId: true }, TASKER_MALAYSIA]);
    await device.reloadReactNative();
    try {
      await tapId('cancelVerify');
    } catch (error) {}
  });

  const checkDataAfterPostTaskDeepCleaning = async () => {
    const taskDescription = 'My Task';
    await device.reloadReactNative();
    await tapId('Tab_Activity');
    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveTextAtIndex('areaNumber', 'Tối đa 80m² - 2 người', 0);
    await tapTask(taskDescription);

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '640,000 RM');
    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Tối đa 80m²', 'text');
    await expectElementVisible('Thời lượng', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectElementVisible('2 người', 'text');
    await expectIdToHaveText('finalCost', '640,000 RM');
  };

  const postTaskDeepCleaning = async () => {
    await postTask('postTaskServiceDEEP_CLEANING', ADDRESS_KEY.MY);
    // POST TASK STEP 2
    await tapId('area80');
    await expectIdToHaveText('lbPrice', '640,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('4 giờ, 14:00 đến 18:00', 'text');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectIdToHaveText('area', 'Tối đa 80m²');
    await expectIdToHaveText('numberOfTasker', '2 người');
    await expectIdToHaveText('price', '640,000 MYR');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
  };

  it('LINE 85 - New customer post deep-cleaning task and sign in', async () => {
    await postTaskDeepCleaning();
    await loginWithModal(ASKER_MALAYSIA.phone, '123456');
    await checkDataAfterPostTaskDeepCleaning();
  });

  it('LINE 92 - New customer post deep-cleaning task and sign up', async () => {
    await postTaskDeepCleaning();
    await signUpWithModal('Bill Gate', '0123950418', '<EMAIL>', '+60');
    await checkDataAfterPostTaskDeepCleaning();
  });

  it('LINE 99 - New customer post deep-cleaning task and for got password', async () => {
    await postTaskDeepCleaning();
    await forgotPasswordWithModal(ASKER_MALAYSIA.phone, ASKER_MALAYSIA.countryCode);
    await checkDataAfterPostTaskDeepCleaning();
  });
});
