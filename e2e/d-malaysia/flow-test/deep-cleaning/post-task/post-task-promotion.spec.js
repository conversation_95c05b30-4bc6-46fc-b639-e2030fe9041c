/**
 * @description See marketing campaign for Deep cleaning
 *   case 1: See campaign marketing and post task Deep cleaning
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const { initData, tapId, expectIdToHaveText, tapText, waitForElement, swipe } = require('../../../../step-definition');
const {
  ASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../../helpers/constants');

const CAMPAIGN = {
  isoCode: MY,
  status: 'ACTIVE',
  code: 'def123',
  serviceName: 'DEEP_CLEANING',
  type: 'PROMOTION',
  primaryNavigate: 'PostTaskStep1',
  secondaryNavigate: 'Home',
};
describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/post-task/post-task-promotion.spec.js - See marketing campaign for Deep cleaning', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);
    await initData('promotion/create-promotion-code', [
      {
        isoCode: MY,
        code: 'def123',
        value: 50000,
        target: 'ASKER',
        typeOfPromotion: 'BOTH',
        typeOfValue: 'MONEY',
        limit: 100,
        maxValue: 30000,
      },
    ]);
    await initData('campaign/createMarketingCampaign', CAMPAIGN);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 46 - See campaign marketing and post task Deep cleaning', async () => {
    await tapId('marketingCampaign_PROMOTION');
    await waitForElement('def123', 500, 'text');
    await tapText('Đăng việc ngay');

    await tapId('area80');
    await expectIdToHaveText('lbOriginPrice', '640,000 MYR');
    await expectIdToHaveText('lbPrice', '590,000 MYR/4h');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await swipe('scrollViewStep4', 'up');
    await expectIdToHaveText('originPrice', '640,000 MYR');
    await expectIdToHaveText('price', '590,000 MYR');
    await tapId('btnSubmitPostTask');
    await tapText('Theo dõi công việc');
    await waitForElement('taskMy Task', 500);
  });
});
