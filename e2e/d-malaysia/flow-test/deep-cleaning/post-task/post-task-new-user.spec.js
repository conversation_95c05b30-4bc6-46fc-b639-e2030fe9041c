/**
 * @description New User post task
 *   case 1: New User post deep cleaning task
 * */

const { E2EHelpers } = require('../../../../e2e.helpers');
const {
  initData,
  tapId,
  tapText,
  postTask,
  swipe,
  expectIdToHaveText,
  expectElementVisible,
  expectIdToHaveTextAtIndex,
  reloadApp,
  ADDRESS_KEY,
} = require('../../../../step-definition');
const { ASKER_MALAYSIA, TASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/deep-cleaning/post-task/post-task-new-user.spec.js - New User post task', () => {
  beforeEach(async () => {
    await reloadApp();
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 41 - New User post deep cleaning task', async () => {
    await postTask('postTaskServiceDEEP_CLEANING', ADDRESS_KEY.MY);
    // POST TASK STEP 2
    await tapId('area80');
    await expectIdToHaveText('lbPrice', '640,000 MYR/4h');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');

    // POST TASK STEP 4
    await swipe('scrollViewStep4', 'up');
    await expectElementVisible('Ngày làm việc', 'text');
    await expectElementVisible('workingDay');
    await expectElementVisible('Làm trong', 'text');
    await expectElementVisible('4 giờ, 14:00 đến 18:00', 'text');
    await expectElementVisible('Chi tiết công việc', 'text');

    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectIdToHaveText('area', 'Tối đa 80m²');
    await expectIdToHaveText('numberOfTasker', '2 người');
    await expectIdToHaveText('price', '640,000 MYR');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');

    // TASK ITEM
    await expectIdToHaveText('serviceNameMy Task', 'Tổng vệ sinh');
    await expectIdToHaveText('notTaskerAccept0', 'Đang chờ người nhận việc...');
    await expectElementVisible('taskDate_My Task');
    await expectIdToHaveText('taskDuration0', '4 giờ, 14:00 đến 18:00 (GMT+8)');
    await expectIdToHaveTextAtIndex('areaNumber', 'Tối đa 80m² - 2 người', 0);
    await tapId('serviceNameMy Task');

    // TASK DETAIL
    await expectIdToHaveText('txtTaskDetail', 'Chi tiết công việc');
    await expectIdToHaveText('titleAccepted', 'Đang chờ người nhận việc...');
    await expectIdToHaveText('finalCost', '640,000 RM');
    await expectElementVisible('Khối lượng công việc', 'text');
    await expectElementVisible('Tối đa 80m²', 'text');
    await expectElementVisible('Thời lượng', 'text');
    await expectElementVisible('Số lượng Tasker', 'text');
    await expectElementVisible('2 người', 'text');
    await expectIdToHaveText('finalCost', '640,000 RM');
  });
});
