/**
 * @description Asker cancel Sofa task
 *  case 1: Asker cancel sofa task, fee = 0
 *  case 2: Asker cancel sofa task include cancel fee
 * <AUTHOR>
 */

const {
  initData,
  tapId,
  tapText,
  expectElementVisible,
  expectElementNotExist,
  waitForElement,
  swipe,
} = require('../../../step-definition');
const moment = require('moment');

const { E2EHelpers } = require('../../../e2e.helpers');
const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: 'SofaCleaning',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/d-malaysia/flow-test/sofa/cancel-task.spec.js - Asker cancel Sofa task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 55 - Asker cancel sofa task, fee = 0', async () => {
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await swipe('updatePage', 'up');
    await expectElementVisible('cancelTask');
    await tapId('cancelTask');

    await tapText('Không cần công việc này nữa.');
    // check button dong y is disabled
    await tapText('Đồng ý');

    // choose reason cancel task, chon ly do thi moi dong y duoc
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await waitForElement('Công việc đã được hủy. Phí: 0 RM.', 1000, 'text');
    await tapText('Đóng');
    await expectElementNotExist('taskMy Task');
    await initData('resetData');
  });

  it('LINE 93 - Asker cancel sofa task include cancel fee', async () => {
    await initData('task/acceptedTask', [
      { isoCode: MY, description: TASK.description, taskerAccepted: [TASKER_MALAYSIA.phone], status: 'CONFIRMED' },
    ]);
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: MY,
        dataUpdate: {
          createdAt: moment().subtract(5, 'hour').toDate(),
          date: moment().add(5, 'hour').toDate(),
        },
      },
    ]);
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTask');
    await tapText('Tìm Tasker mới');
    await expectElementVisible('cancelTask');
    await tapId('cancelTask');

    await tapText('Không cần công việc này nữa.');
    // check button dong y is disabled
    await tapText('Đồng ý');

    // choose reason cancel task, chon ly do thi moi dong y duoc
    await tapText('Đồng ý');
    await tapText('Đồng ý');
    await waitForElement('Công việc đã được hủy. Phí: 20,000 RM.', 1000, 'text');
    await tapText('Đóng');
    await expectElementNotExist('taskMy Task');
    await initData('resetData');
  });
});
