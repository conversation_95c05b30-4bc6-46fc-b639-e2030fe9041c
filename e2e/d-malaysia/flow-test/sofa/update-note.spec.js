/**
 * @description Update note
 *   case 1: Asker update note normal task
 *   case 2: Asker update note prepay task
 * */

const {
  initData,
  tapId,
  tapText,
  swipe,
  typeToTextField,
  waitForElement,
  clearTextInput,
} = require('../../../step-definition');

const { E2EHelpers } = require('../../../e2e.helpers');

const {
  ASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: 'SofaCleaning',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/d-malaysia/flow-test/sofa/update-note.spec.js - Update note', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 45 - Asker update note normal task', async () => {
    await tapText('Hoạt động');
    await tapId('taskMy Task');
    await swipe('scrollTaskDetail', 'up');
    await tapId('btnEditTaskNote');
    await clearTextInput('taskNote');
    await typeToTextField('taskNote', 'Lau dọn phong ngu tang 1\n');
    await tapId('updateTaskNote');
    await waitForElement('Lau dọn phong ngu tang 1\n', 500, 'text');
  });
});
