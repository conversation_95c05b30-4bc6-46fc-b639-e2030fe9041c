/**
 * @description Tasker accept Sofa task
 *  case 1 Tasker accept sofa task
 * <AUTHOR>
 */

const {
  initData,
  callService,
  tapText,
  expectIdToHaveText,
  expectElementNotExist,
} = require('../../../step-definition');
const expect = require('chai').expect;

const { E2EHelpers } = require('../../../e2e.helpers');

const {
  ASKER_MALAYSIA,
  TASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: 'SofaCleaning',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/d-malaysia/flow-test/sofa/tasker-accept-task.spec.js - Tasker accept Sofa task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA, TASKER_MALAYSIA]);
    await initData('task/createTask', [TASK]);
    await initData('service/updateServiceChannel', [
      { isoCode: MY, serviceName: 'SofaCleaning', taskerPhone: TASKER_MALAYSIA.phone },
    ]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 60 - Tasker accept sofa task', async () => {
    await expectElementNotExist('taskerName0');
    const task = await initData('task/getTaskByDescription', { description: 'My Task', isoCode: MY });
    const tasker = await initData('user/getUserByPhone', {
      phone: TASKER_MALAYSIA.phone,
      countryCode: TASKER_MALAYSIA.countryCode,
    });
    await initData('task/updateTask', [
      {
        description: TASK.description,
        isoCode: MY,
        dataUpdate: {
          viewedTaskers: [tasker?._id],
        },
      },
    ]);
    const request = {
      taskId: task._id,
      taskerId: tasker._id,
      companyId: tasker._id,
    };
    const response = await callService('/v3/accept-task-my/accept', request);
    expect(response.status).to.equal('CONFIRMED');
    await tapText('Hoạt động');
    await expectIdToHaveText('taskerName0', TASKER_MALAYSIA.name);
  });
});
