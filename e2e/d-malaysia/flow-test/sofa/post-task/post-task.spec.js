/**
 * @description Post Sofa Task
 *  case 1: Old user post Sofa task
 *  case 2: New user post Sofa task
 * <AUTHOR>
 */
const {
  tapId,
  swipe,
  tapText,
  initData,
  postTask,
  tapIdAtIndex,
  waitForElement,
  waitForLoading,
  expectIdToHaveText,
  expectElementVisible,
  expectElementNotExist,
  expectIdToHaveTextAtIndex,
  typeToTextFieldSubmitKeyboard,
  scroll,
  clearTextInput,
  ADDRESS_KEY,
  tapHeaderBack,
} = require('../../../../step-definition');

const { E2EHelpers } = require('../../../../e2e.helpers');
const { ASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/sofa/post-task/post-task.spec.js - Post Sofa Task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER_MALAYSIA]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 43 - Old user post Sofa task', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);
    await tapText('Sofa nỉ / vải');
    await tapText('Sofa 02 ghế');
    await expectIdToHaveText('lbPrice', '450,000 MYR');
    await scroll('scrollSofa', 400, 'down', 0.5, 0.5);
    await tapText('Sofa 03 ghế');
    await expectIdToHaveText('lbPrice', '1,050,000 MYR');

    await tapHeaderBack();
    await tapText('Sofa da');
    await tapText('Sofa 01 ghế');
    await expectIdToHaveText('lbPrice', '1,450,000 MYR');

    await tapHeaderBack();
    await tapId('Tab_Mattress');
    await tapId('check_box_1_mattress'); // id: check_box_[type]_[from]
    await expectIdToHaveText('lbPrice', '1,750,000 MYR');

    await tapId('Tab_Curtain');
    await tapText('Rèm đôi lớn');
    await expectIdToHaveText('lbPrice', '2,150,000 MYR');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await scroll('scrollViewStep4', 700, 'down', 0.5, 0.5);

    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
  });

  it('LINE 77 - New user post Sofa task', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);
    await tapText('Sofa nỉ / vải');
    await tapText('Sofa 02 ghế');
    await expectIdToHaveText('lbPrice', '450,000 MYR');
    await scroll('scrollSofa', 400, 'down', 0.5, 0.5);
    await tapText('Sofa 03 ghế');
    await expectIdToHaveText('lbPrice', '1,050,000 MYR');

    await tapHeaderBack();
    await tapText('Sofa da');
    await tapText('Sofa 01 ghế');
    await expectIdToHaveText('lbPrice', '1,450,000 MYR');

    await tapHeaderBack();
    await tapId('Tab_Mattress');
    await tapId('check_box_1_mattress'); // id: check_box_[type]_[from]
    await expectIdToHaveText('lbPrice', '1,750,000 MYR');

    await tapId('Tab_Curtain');
    await tapText('Rèm đôi lớn');
    await expectIdToHaveText('lbPrice', '2,150,000 MYR');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');

    // See task detail in PT4
    await swipe('scrollViewStep4', 'up');
    await waitForElement('Sofa 01 ghế', 500, 'text');
    await waitForElement('Sofa 02 ghế', 500, 'text');
    await waitForElement('Sofa 03 ghế', 500, 'text');

    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');

    // See task detail
    await tapId('taskMy Task');
    await waitForElement('Sofa 01 ghế', 500, 'text');
    await waitForElement('Sofa 02 ghế', 500, 'text');
    await waitForElement('Sofa 03 ghế', 500, 'text');
  });

  it('LINE 125 - New user post Sofa task with carpet cleaning', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);
    await tapId('Tab_Carpet');
    await tapId('check_box_1_carpet'); // id: check_box_[type]_[from]
    await typeToTextFieldSubmitKeyboard('input_note_1_carpet', 'mini ne');
    await expectIdToHaveText('lbPrice', '300,000 MYR');

    // Select type 2: tu 1.5m den 1.8m
    await tapId('check_box_2_carpet'); // id: check_box_[type]_[from]
    await expectIdToHaveText('lbPrice', '650,000 MYR');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMINGMy Task');

    // See task detail
    await tapId('taskMy Task');
    await waitForElement('mini ne', 500, 'text');
  });

  it('LINE 151 - New user post Sofa task with mattress cleaning', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);

    await tapId('Tab_Mattress');

    // Select type 1: nho hon 1.5m
    await tapId('check_box_1_mattress'); // id: check_box_[type]_[from]
    await typeToTextFieldSubmitKeyboard('input_note_1_mattress', 'nem mini ne');
    await expectElementVisible('lbPrice', '300,000 MYR');

    // Select type 2: tu 1.5m den 1.8m
    await tapId('check_box_2_mattress'); // id: check_box_[type]_[from]
    await expectElementVisible('lbPrice', '650,000 MYR');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMINGMy Task');

    // See task detail
    await tapId('taskMy Task');
    await waitForElement('nem mini ne', 500, 'text');
  });

  it('LINE 180 - Check add and remove sofa tab', async () => {
    // check data after remove and price button hide/show
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);

    await tapText('Sofa nỉ / vải');
    await tapText('Sofa 02 ghế');

    await tapHeaderBack();
    await tapText('Sofa da');
    await expectElementVisible('lbPrice', '450,000 MYR');

    // Choose sofa da
    await tapText('Sofa 01 ghế');
    await expectElementVisible('lbPrice', '850,000 MYR');

    // No choose sofa and hide price button
    await tapHeaderBack();
    await tapText('Sofa nỉ / vải');
    await tapText('Sofa 02 ghế');

    await tapHeaderBack();
    await tapText('Sofa da');
    await tapText('Sofa 01 ghế');
    await expectElementNotExist('lbPrice');
  });

  // Check remove data when hide spinner
  it('LINE 210 - Post Sofa task with mattress cleaning quantity default 1', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);

    await tapId('Tab_Mattress');

    // Select type 1: nho hon 1.5m
    await tapId('check_box_1_mattress'); // id: check_box_[type]_[from]
    await expectIdToHaveText('txtAmount', '1');
    await expectElementVisible('lbPrice', '300,000 MYR');

    await tapId('check_box_1_mattress');
    await waitForLoading(500);
    await expectElementNotExist('lbPrice');

    // choose data again
    await tapId('check_box_1_mattress');
    await expectElementVisible('lbPrice', '300,000 MYR');
    await tapId('btnPlus_mattress');
    await expectIdToHaveText('txtAmount', '2');
    await expectElementVisible('lbPrice', '600,000 MYR');

    // Remove data
    await tapId('btnMinus_mattress');
    await tapId('btnMinus_mattress');
    await waitForLoading(500);
    await expectElementNotExist('lbPrice');
  });

  it('LINE 238 - New user post Sofa task with curtain cleaning', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);
    await tapId('Tab_Curtain');
    await tapId('check_box_livingRoom_curtain'); // id: check_box_[type]_[from]
    await scroll('scrollCurtain', 400, 'down', 0.5, 0.5);
    await typeToTextFieldSubmitKeyboard('input_note_livingRoom_curtain', 'phong khach');
    await expectElementVisible('lbPrice', '400,000 MYR');
    await scroll('scrollCurtain', 400, 'down', 0.5, 0.5);
    // Select type 2: tu 1.5m den 1.8m
    await tapId('check_box_bedRoom_curtain'); // id: check_box_[type]_[from]
    // await tapIdAtIndex('btnPlus', 1);
    await expectElementVisible('lbPrice', '700,000 MYR');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMINGMy Task');

    // See task detail
    await tapId('taskMy Task');
    await waitForElement('phong khach', 500, 'text');
    await waitForElement('Rèm đôi lớn', 500, 'text');
    await waitForElement('Rèm đôi nhỏ', 500, 'text');
  });

  it('LINE 266 - New user post curtain and press edit text quantity washing', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);

    await tapId('Tab_Curtain');
    // 1kg = 38,000 vnd
    await scroll('scrollCurtain', 500, 'down', 0.5, 0.5);

    await tapText('Trên 15kg');

    await scroll('scrollCurtain', 500, 'down', 0.5, 0.5);

    await typeToTextFieldSubmitKeyboard('inputQtyWashingAmount', '16');

    await scroll('scrollCurtain', 500, 'down', 0.5, 0.5);
    await expectIdToHaveText('inputQtyWashingAmount', '16kg');
    await expectElementVisible('lbPrice', '672,000 MYR');
    await tapId('inputQtyWashingAmount');
    await clearTextInput('inputQtyWashingAmount');
    await typeToTextFieldSubmitKeyboard('inputQtyWashingAmount', '20');
    await expectElementVisible('lbPrice', '840,000 MYR');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMINGMy Task');

    // See task detail
    await tapId('taskMy Task');
    await waitForElement('Trên 15kg', 500, 'text');
    await waitForElement('x20', 500, 'text');
  });

  it('LINE 298 - user post curtain an choose type washing over 15', async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);

    await tapId('Tab_Curtain');
    await scroll('scrollCurtain', 500, 'down', 0.5, 0.5);
    // 1kg = 38,000 vnd
    await tapText('Trên 15kg');

    await scroll('scrollCurtain', 500, 'down', 0.5, 0.5);
    await typeToTextFieldSubmitKeyboard('inputQtyWashingAmount', '16');

    await scroll('scrollCurtain', 500, 'down', 0.5, 0.5);
    await expectIdToHaveText('inputQtyWashingAmount', '16kg');
    await expectElementVisible('lbPrice', '672,000 MYR');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    await swipe('scrollViewStep4', 'up');
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
    await expectElementVisible('TAB_UPCOMINGMy Task');

    // See task detail
    await tapId('taskMy Task');
    await waitForElement('Trên 15kg', 500, 'text');
    await waitForElement('x16', 500, 'text');
  });
});
