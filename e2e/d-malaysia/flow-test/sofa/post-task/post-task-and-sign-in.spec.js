const {
  initData,
  tapId,
  postTask,
  loginWithModal,
  signUpWithModal,
  forgotPasswordWithModal,
  swipe,
  waitForElement,
  typeToTextFieldSubmitKeyboard,
  tapText,
  scroll,
  ADDRESS_KEY,
  expectElementVisible,
} = require('../../../../step-definition');

const { device } = require('detox');

const { ASKER_MALAYSIA } = require('../../../../helpers/constants');

describe('FILE: e2e/d-malaysia/flow-test/sofa/post-task/post-task-and-sign-in.spec.js - New User post sofa task before sign in', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, randomId: true }]);
    await device.reloadReactNative();
  });

  const checkDataAfterPostTaskSofa = async () => {
    await tapText('<PERSON> dõi công việc');
    await expectElementVisible('TAB_UPCOMINGMy Task');
    // See task detail
    await tapId('taskMy Task');
    await waitForElement('phong khach', 500, 'text');
    await waitForElement('Rèm đôi lớn', 500, 'text');
    await waitForElement('Rèm đôi nhỏ', 500, 'text');
    await swipe('scrollTaskDetail', 'up');
    await expectElementVisible('finalCost');
  };

  const postTaskSofa = async () => {
    await postTask('postTaskServiceSofaCleaning', ADDRESS_KEY.MY);
    try {
      await tapId('address1');
    } catch (error) {}
    await tapId('Tab_Curtain');
    await tapId('check_box_livingRoom_curtain'); // id: check_box_[type]_[from]
    await expectElementVisible('lbPrice');
    await waitForElement('scrollCurtain', 500);
    await scroll('scrollCurtain', 400, 'down', 0.5, 0.5);
    await typeToTextFieldSubmitKeyboard('input_note_livingRoom_curtain', 'phong khach');
    await scroll('scrollCurtain', 400, 'down', 0.5, 0.5);
    // Select type 2: tu 1.5m den 1.8m
    await tapId('check_box_bedRoom_curtain'); // id: check_box_[type]_[from]
    await expectElementVisible('lbPrice');

    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await tapId('btnNextNoteStep3');
    // await tapId('btnSubmitPostTask');
    await scroll('scrollViewStep4', 700, 'down', 0.5, 0.5);
    await tapId('choosePaymentMethod');
    await tapText('Tiền mặt');
    await tapText('Đăng việc');
  };

  it('LINE 74 - New customer post sofa task and sign in', async () => {
    await postTaskSofa();
    await loginWithModal(ASKER_MALAYSIA.phone, '123456');
    await checkDataAfterPostTaskSofa();
  });

  it('LINE 82 - New customer post sofa task and sign up', async () => {
    await postTaskSofa();

    await signUpWithModal('Bill Gate', '0123456788', '<EMAIL>', '+60');
    await checkDataAfterPostTaskSofa();
  });

  it('LINE 90 - New customer post sofa task and for got password', async () => {
    await postTaskSofa();
    await forgotPasswordWithModal(ASKER_MALAYSIA.phone, ASKER_MALAYSIA.countryCode);
    await checkDataAfterPostTaskSofa();
  });
});
