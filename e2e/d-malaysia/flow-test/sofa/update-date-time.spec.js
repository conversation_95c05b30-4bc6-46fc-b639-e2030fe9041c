const {
  initData,
  tapId,
  waitForElement,
  tapText,
  expectElementVisible,
  expectIdToHaveTextAtIndex,
  selectTime,
  expectElementNotVisible,
  swipe,
  capitalize,
} = require('../../../step-definition');
const moment = require('moment');

moment.locale('vi');
const { E2EHelpers } = require('../../../e2e.helpers');
const { device } = require('detox');

const {
  ASKER_MALAYSIA,
  ISO_CODE: { MY },
} = require('../../../helpers/constants');

const TASK = {
  isoCode: MY,
  serviceName: 'SofaCleaning',
  askerPhone: ASKER_MALAYSIA.phone,
  description: 'My Task',
  rate: false,
};

describe('FILE: e2e/d-malaysia/flow-test/sofa/update-date-time.spec.js - Update Sofa Task', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [{ ...ASKER_MALAYSIA, FMainAccount: 5000000 }]);
    await initData('task/createTask', [TASK]);
    await E2EHelpers.onHaveLogin(ASKER_MALAYSIA.phone, '123456', ASKER_MALAYSIA.countryCode);
  });

  it('LINE 53 - Asker update time Sofa Task', async () => {
    if (device.getPlatform() === 'ios') {
      await tapText('Hoạt động');
      await tapId('taskMy Task');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await expectElementVisible('Đổi ngày giờ', 'text');
      await expectElementNotVisible('btnUpdateDateTime');
      await selectTime(1);
      await tapText('Đồng ý');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');
    }
  });

  it('LINE 72 - Asker update date Laundry Task', async () => {
    if (device.getPlatform() === 'ios') {
      await tapText('Hoạt động');
      await tapId('taskMy Task');
      await swipe('scrollTaskDetail', 'up');
      await tapId('btnEditTask');
      await expectElementVisible('Đổi ngày giờ', 'text');
      await tapId('weekdays_4');
      await tapId('btnUpdateDateTime');
      await tapText('Đồng ý');
      await waitForElement('Cập nhật thành công', 500, 'text');
      await tapText('Đóng');

      const next2Day = moment().add(4, 'd');
      const string2Day = moment(next2Day).format('dddd, DD/MM/YYYY');
      await expectElementVisible(capitalize(string2Day), 'text');
    }
  });
});
