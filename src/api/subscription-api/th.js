import { fetchAPI } from '@helper';

export const postSubscription = async (options) => {
  return await fetchAPI('v5/booking-th/subscription', options);
};

/**
 * @param subscriptionId String
 * @param paymentMethod String
 */
export const repaySubscription = async (options) => {
  return await fetchAPI('v5/payment/repay-subscription-th', options);
};

export const getPriceSubscription = async (options) => {
  return await fetchAPI('v5/pricing-th/subscription', options);
};

export const getPriceSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/pricing-th/subscription-elderly-care', options);
};

export const getPriceSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/pricing-th/subscription-patient-care', options);
};

export const getPriceSubscriptionChildCare = async (options) => {
  return await fetchAPI('v5/pricing-th/subscription-child-care', options);
};

export const getPriceSubscriptionOfficeCleaning = async (options) => {
  return await fetchAPI('v5/pricing-th/subscription-office-cleaning', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-th/new-subscription', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateExpireSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-th/update-expired-subscription', options);
};

/**
 * @param object
 */
export const cancelTaskSubscription = async (options) => {
  return await fetchAPI('v5/cancel-task-th/cancel-task-subscription', options);
};

export const getDetailSubscriptionSchedule = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-detail-subscription-schedule', options);
};

/**
 * @param options String
 */
export const cancelSubscription = async (options) => {
  return await fetchAPI('v5/api-asker-th/cancel-subscription', options);
};

export const getSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-th/get-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

export const cancelSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-th/cancel-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

export const postSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/booking-th/subscription-elderly-care', options);
};

export const postSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/booking-th/subscription-patient-care', options);
};

export const postSubscriptionOfficeCleaningAPI = async (options) => {
  return await fetchAPI('v5/booking-th/subscription-office-cleaning', options);
};
