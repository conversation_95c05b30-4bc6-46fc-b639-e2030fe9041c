import { fetchAPI } from '@helper';

export const getPriceSubscription = async (options) => {
  return await fetchAPI('v5/pricing-vn/subscription', options);
};

export const postSubscription = async (options) => {
  return await fetchAPI('v5/booking/subscription', options);
};

/**
 * @param options String
 */
export const cancelSubscription = async (options) => {
  return await fetchAPI('v5/api-asker-vn/cancel-subscription', options);
};

/**
 * @param subscriptionId String
 * @param paymentMethod String
 */
export const repaySubscription = async (options) => {
  return await fetchAPI('v5/payment/repay-subscription', options);
};

export const getSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-vn/get-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

export const cancelSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-vn/cancel-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

/**
 * @param object
 */
export const cancelTaskSubscription = async (options) => {
  return await fetchAPI('v5/cancel-task-vn/cancel-task-subscription', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-vn/new-subscription', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateExpireSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-vn/update-expired-subscription', options);
};

export const getPriceSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/pricing-vn/subscription-elderly-care', options);
};

export const getPriceSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/pricing-vn/subscription-patient-care', options);
};

export const getPriceSubscriptionChildCare = async (options) => {
  return await fetchAPI('v5/pricing-vn/subscription-child-care', options);
};

export const getPriceSubscriptionOfficeCleaning = async (options) => {
  return await fetchAPI('v5/pricing-vn/subscription-office-cleaning', options);
};
export const getDetailSubscriptionSchedule = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-detail-subscription-schedule', options);
};

export const postSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/booking/subscription-elderly-care', options);
};

export const postSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/booking/subscription-patient-care', options);
};

export const postSubscriptionOfficeCleaningAPI = async (options) => {
  return await fetchAPI('v5/booking/subscription-office-cleaning', options);
};
