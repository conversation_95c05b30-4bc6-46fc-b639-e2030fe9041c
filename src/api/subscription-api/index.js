import { isEmpty } from 'lodash';

import { SERVICES } from '@config';
import { fetchAPI, getIPAddress } from '@helper';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export const getPriceSubscription = async (options) => {
  const combine = {
    VN: VN_API.getPriceSubscription,
    TH: TH_API.getPriceSubscription,
    ID: ID_API.getPriceSubscription,
    MY: MY_API.getPriceSubscription,
  };
  return await combine[global.isoCode](options);
};

export const postSubscription = async (options) => {
  options.shopperIP = await getIPAddress();
  const combine = {
    VN: VN_API.postSubscription,
    TH: TH_API.postSubscription,
    ID: ID_API.postSubscription,
    MY: MY_API.postSubscription,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param options String
 */
export const cancelSubscription = async (options) => {
  const combine = {
    VN: VN_API.cancelSubscription,
    TH: TH_API.cancelSubscription,
    ID: ID_API.cancelSubscription,
    MY: MY_API.cancelSubscription,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param subscriptionId String
 * @param paymentMethod String
 */
export const repaySubscription = async (subscriptionId, paymentMethod, promotion) => {
  const options = {
    _id: subscriptionId,
    payment: paymentMethod,
    shopperIP: await getIPAddress(),
  };
  if (promotion) {
    options.promotion = {
      code: promotion,
    };
  }
  const combine = {
    VN: VN_API.repaySubscription,
    TH: TH_API.repaySubscription,
    ID: ID_API.repaySubscription,
    MY: MY_API.repaySubscription,
  };
  return await combine[global.isoCode](options);
};

export const getSubscriptionRequest = async (subscriptionRequestId) => {
  const combine = {
    VN: VN_API.getSubscriptionRequest,
    TH: TH_API.getSubscriptionRequest,
    ID: ID_API.getSubscriptionRequest,
    MY: MY_API.getSubscriptionRequest,
  };
  return await combine[global.isoCode](subscriptionRequestId);
};

export const cancelSubscriptionRequest = async (subscriptionRequestId) => {
  const combine = {
    VN: VN_API.cancelSubscriptionRequest,
    TH: TH_API.cancelSubscriptionRequest,
    ID: ID_API.cancelSubscriptionRequest,
    MY: MY_API.cancelSubscriptionRequest,
  };
  return await combine[global.isoCode](subscriptionRequestId);
};

/**
 * @param object
 */
export const cancelTaskSubscription = async ({ userId, taskId, date, reason }) => {
  const combine = {
    VN: VN_API.cancelTaskSubscription,
    TH: TH_API.cancelTaskSubscription,
    ID: ID_API.cancelTaskSubscription,
    MY: MY_API.cancelTaskSubscription,
  };
  const options = { userId, taskId, date, reason };
  return await combine[global.isoCode](options);
};

export const getPriceSubscriptionElderly = async (options) => {
  const combine = {
    VN: VN_API.getPriceSubscriptionElderly,
    TH: TH_API.getPriceSubscriptionElderly,
    ID: ID_API.getPriceSubscriptionElderly,
    MY: MY_API.getPriceSubscriptionElderly,
  };
  return await combine[global.isoCode](options);
};

export const postSubscriptionElderly = async (options) => {
  options.shopperIP = await getIPAddress();
  const combine = {
    VN: VN_API.postSubscriptionElderly,
    TH: TH_API.postSubscriptionElderly,
    ID: ID_API.postSubscriptionElderly,
    MY: MY_API.postSubscriptionElderly,
  };
  return await combine[global.isoCode](options);
};

export const getPriceSubscriptionPatient = async (options) => {
  const combine = {
    VN: VN_API.getPriceSubscriptionPatient,
    TH: TH_API.getPriceSubscriptionPatient,
    ID: ID_API.getPriceSubscriptionPatient,
    MY: MY_API.getPriceSubscriptionPatient,
  };
  return await combine[global.isoCode](options);
};

export const postSubscriptionPatient = async (options) => {
  options.shopperIP = await getIPAddress();
  const combine = {
    VN: VN_API.postSubscriptionPatient,
    TH: TH_API.postSubscriptionPatient,
    ID: ID_API.postSubscriptionPatient,
    MY: MY_API.postSubscriptionPatient,
  };
  return await combine[global.isoCode](options);
};

export const getPriceSubscriptionChildCare = async (options) => {
  const combine = {
    VN: VN_API.getPriceSubscriptionChildCare,
    TH: TH_API.getPriceSubscriptionChildCare,
    ID: ID_API.getPriceSubscriptionChildCare,
    MY: MY_API.getPriceSubscriptionChildCare,
  };
  return await combine[global.isoCode](options);
};

export const postSubscriptionChildCare = async (options) => {
  options.shopperIP = await getIPAddress();
  return await fetchAPI('v5/booking/subscription-child-care', options);
};

export const getPriceSubscriptionOfficeCleaning = async (options) => {
  const combine = {
    VN: VN_API.getPriceSubscriptionOfficeCleaning,
    TH: TH_API.getPriceSubscriptionOfficeCleaning,
    ID: ID_API.getPriceSubscriptionOfficeCleaning,
    MY: MY_API.getPriceSubscriptionOfficeCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postSubscriptionOfficeCleaning = async (options) => {
  options.shopperIP = await getIPAddress();
  const combine = {
    VN: VN_API.postSubscriptionOfficeCleaningAPI,
    TH: TH_API.postSubscriptionOfficeCleaningAPI,
    ID: ID_API.postSubscriptionOfficeCleaningAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateSubscriptionApi = async ({
  subscriptionId,
  newSchedule,
  newMonth,
  newDuration,
  isPremium,
  newDetailChildCare,
  newStartDate,
  newEndDate,
  isEco,
  timezone,
  pet,
  addons,
}) => {
  const options = {
    subscriptionId: subscriptionId,
  };

  if (newSchedule) {
    options.newSchedule = newSchedule;
  }

  if (newMonth) {
    options.newMonth = newMonth;
  }

  if (newDuration) {
    options.newDuration = newDuration;
  }

  if (newDetailChildCare) {
    options.newDetailChildCare = newDetailChildCare;
  }

  if (newStartDate) {
    options.newStartDate = newStartDate;
  }

  if (newEndDate) {
    options.newEndDate = newEndDate;
  }

  if (timezone) {
    options.timezone = timezone;
  }

  options.isEco = isEco;

  options.isPremium = isPremium;

  if (!isEmpty(pet)) {
    options.pet = pet;
  }

  if (!isEmpty(addons)) {
    options.addons = addons;
  }

  const combine = {
    VN: VN_API.updateSubscriptionApi,
    TH: TH_API.updateSubscriptionApi,
    ID: ID_API.updateSubscriptionApi,
    MY: MY_API.updateSubscriptionApi,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateExpireSubscriptionApi = async ({
  subscriptionId,
  newSchedule,
  newMonth,
  newDuration,
  isPremium,
  newDetailChildCare,
  newStartDate,
  newEndDate,
  isEco,
  pet,
  addons,
}) => {
  const options = {
    subscriptionId: subscriptionId,
  };

  if (newSchedule) {
    options.newSchedule = newSchedule;
  }

  if (newMonth) {
    options.newMonth = newMonth;
  }

  if (newDuration) {
    options.newDuration = newDuration;
  }

  if (newDetailChildCare) {
    options.newDetailChildCare = newDetailChildCare;
  }

  if (newStartDate) {
    options.newStartDate = newStartDate;
  }

  if (newEndDate) {
    options.newEndDate = newEndDate;
  }
  options.isEco = isEco;

  options.isPremium = isPremium;

  if (!isEmpty(pet)) {
    options.pet = pet;
  }

  if (!isEmpty(addons)) {
    options.addons = addons;
  }

  const combine = {
    VN: VN_API.updateExpireSubscriptionApi,
    TH: TH_API.updateExpireSubscriptionApi,
    ID: ID_API.updateExpireSubscriptionApi,
    MY: MY_API.updateExpireSubscriptionApi,
  };
  return await combine[global.isoCode](options);
};

export const postTaskSubscriptionProvider = async (dataTask, serviceName) => {
  switch (serviceName) {
    case SERVICES.CLEANING_SUBSCRIPTION:
      return await postSubscription(dataTask);
    case SERVICES.ELDERLY_CARE_SUBSCRIPTION:
      return await postSubscriptionElderly(dataTask);
    case SERVICES.PATIENT_CARE_SUBSCRIPTION:
      return await postSubscriptionPatient(dataTask);
    case SERVICES.CHILD_CARE_SUBSCRIPTION:
      return await postSubscriptionChildCare(dataTask);
    case SERVICES.OFFICE_CLEANING_SUBSCRIPTION:
      return await postSubscriptionOfficeCleaning(dataTask);
    default:
      break;
  }
};

export const getPriceSubscriptionProvider = async (dataTask, serviceName) => {
  switch (serviceName) {
    case SERVICES.CLEANING_SUBSCRIPTION:
      return await getPriceSubscription(dataTask);
    case SERVICES.ELDERLY_CARE_SUBSCRIPTION:
      return await getPriceSubscriptionElderly(dataTask);
    case SERVICES.PATIENT_CARE_SUBSCRIPTION:
      return await getPriceSubscriptionPatient(dataTask);
    case SERVICES.CHILD_CARE_SUBSCRIPTION:
      return await getPriceSubscriptionChildCare(dataTask);
    case SERVICES.OFFICE_CLEANING_SUBSCRIPTION:
      return await getPriceSubscriptionOfficeCleaning(dataTask);
    default:
      break;
  }
};

export const getDetailSubscriptionSchedule = async (options) => {
  const combine = {
    VN: VN_API.getDetailSubscriptionSchedule,
    TH: TH_API.getDetailSubscriptionSchedule,
    ID: ID_API.getDetailSubscriptionSchedule,
    MY: MY_API.getDetailSubscriptionSchedule,
  };
  return await combine[global.isoCode](options);
};
