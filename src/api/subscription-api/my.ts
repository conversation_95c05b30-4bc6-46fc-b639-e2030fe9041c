/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-10-25 16:18:33
 * @modify date 2024-06-20 10:46:53
 * @desc [description]
 */
import { fetchAPI } from '@helper';

export const getPriceSubscription = async (options) => {
  return await fetchAPI('v5/pricing-my/subscription', options);
};

export const postSubscription = async (options) => {
  return await fetchAPI('v5/booking-my/subscription', options);
};

/**
 * @param options String
 */
export const cancelSubscription = async (options) => {
  return await fetchAPI('v5/api-asker-my/cancel-subscription', options);
};

/**
 * @param subscriptionId String
 * @param paymentMethod String
 */
export const repaySubscription = async (options) => {
  return await fetchAPI('v5/payment-my/repay-subscription', options);
};

export const getSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-my/get-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

export const cancelSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-my/cancel-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

/**
 * @param object
 */
export const cancelTaskSubscription = async (options) => {
  return await fetchAPI('v5/cancel-task-my/cancel-task-subscription', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-my/new-subscription', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateExpireSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-my/update-expired-subscription', options);
};

export const getPriceSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/pricing-my/subscription-elderly-care', options);
};

export const getPriceSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/pricing-my/subscription-patient-care', options);
};

export const getPriceSubscriptionChildCare = async (options) => {
  return await fetchAPI('v5/pricing-my/subscription-child-care', options);
};

export const getPriceSubscriptionOfficeCleaning = async (options) => {
  return await fetchAPI('v5/pricing-my/subscription-office-cleaning', options);
};

export const getDetailSubscriptionSchedule = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-detail-subscription-schedule', options);
};

export const postSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/booking-my/subscription-elderly-care', options);
};

export const postSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/api-asker-my/subscription-patient-care', options);
};
