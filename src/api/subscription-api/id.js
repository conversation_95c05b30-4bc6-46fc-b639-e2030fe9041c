/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-10-25 16:18:33
 * @modify date 2024-06-20 10:46:53
 * @desc [description]
 */
import { fetchAPI } from '@helper';

export const getPriceSubscription = async (options) => {
  return await fetchAPI('v5/pricing-indo/subscription', options);
};

export const postSubscription = async (options) => {
  return await fetchAPI('v5/booking-indo/subscription', options);
};

/**
 * @param options String
 */
export const cancelSubscription = async (options) => {
  return await fetchAPI('v5/api-asker-indo/cancel-subscription', options);
};

/**
 * @param subscriptionId String
 * @param paymentMethod String
 */
export const repaySubscription = async (options) => {
  return await fetchAPI('v5/payment-indo/repay-subscription', options);
};

export const getSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-indo/get-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

export const cancelSubscriptionRequest = async (subscriptionRequestId) => {
  return await fetchAPI('v5/api-asker-indo/cancel-subscription-suggestion', {
    subscriptionRequestId: subscriptionRequestId,
  });
};

/**
 * @param object
 */
export const cancelTaskSubscription = async (options) => {
  return await fetchAPI('v5/cancel-task-indo/cancel-task-subscription', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-indo/new-subscription', options);
};

/**
 * @param subscriptionId
 * @param newSchedule: [xx, xx]
 * @param newMonth
 * @param newDuration
 * **/
export const updateExpireSubscriptionApi = async (options) => {
  return await fetchAPI('v5/update-task-indo/update-expired-subscription', options);
};

export const getPriceSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/pricing-indo/subscription-elderly-care', options);
};

export const getPriceSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/pricing-indo/subscription-patient-care', options);
};

export const getPriceSubscriptionChildCare = async (options) => {
  return await fetchAPI('v5/pricing-indo/subscription-child-care', options);
};

export const getPriceSubscriptionOfficeCleaning = async (options) => {
  return await fetchAPI('v5/pricing-indo/subscription-office-cleaning', options);
};

export const getDetailSubscriptionSchedule = async (options) => {
  return await fetchAPI('v5/api-asker-indo/get-detail-subscription-schedule', options);
};

export const postSubscriptionElderly = async (options) => {
  return await fetchAPI('v5/booking-indo/subscription-elderly-care', options);
};

export const postSubscriptionPatient = async (options) => {
  return await fetchAPI('v5/api-asker-indo/subscription-patient-care', options);
};

export const postSubscriptionOfficeCleaningAPI = async (options) => {
  return await fetchAPI('v5/booking-indo/subscription-office-cleaning', options);
};
