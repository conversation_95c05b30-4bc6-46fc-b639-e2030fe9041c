import { fetchAPI } from '@helper';

import {
  IAddMemberToBusiness,
  IAddMemberToLevel,
  ICreateBusinessLevel,
  ICreateBusinessParams,
  IGetTotalRevokeParams,
  IGetTotalTopUpParams,
  IListBusinessMemberTransactions,
  IListBusinessTransactions,
  IListMember,
  IParamsGetBusinessTopUpSettingInfo,
  IRemoveBusinessLevel,
  IRemoveMemberFormBusiness,
  IRemoveMemberFormGroup,
  IRevokeMemberLevelParams,
  ISendBusinessReport,
  ITopUpMemberLevelParams,
  IUpdateBusinessLevel,
  IUpdateGroupName,
  IVerifyMember,
} from './type';

export const getListMember = async (options: IListMember) => {
  return await fetchAPI('v5/api-asker-vn/list-members-by-level', options);
};
export const updateGroupName = async (options: IUpdateGroupName) => {
  return await fetchAPI('v5/api-asker-vn/update-member-level', options);
};
export const removeMemberFromBusiness = async (options: IRemoveMemberFormBusiness) => {
  return await fetchAPI('v5/api-asker-vn/remove-member-from-business', options);
};
export const removeMemberFromGroup = async (options: IRemoveMemberFormGroup) => {
  return await fetchAPI('v5/api-asker-vn/remove-members-level', options);
};
export const listBusinessMemberTransactions = async (options: IListBusinessMemberTransactions) => {
  return await fetchAPI('v5/api-asker-vn/list-business-member-transactions', options);
};
export const addMemberToLevel = async (options: IAddMemberToLevel) => {
  return await fetchAPI('v5/api-asker-vn/add-members-level', options);
};

export const createBusinessLevel = async (options: ICreateBusinessLevel) => {
  return await fetchAPI('v5/api-asker-vn/create-business-level', options);
};
export const updateBusinessLevel = async (options: IUpdateBusinessLevel) => {
  return await fetchAPI('v5/api-asker-vn/update-business-level', options);
};

export const removeBusinessLevel = async (options: IRemoveBusinessLevel) => {
  return await fetchAPI('v5/api-asker-vn/remove-business-level', options);
};

export const verifyMember = async (options: IVerifyMember) => {
  return await fetchAPI('v5/api-asker-vn/verify-members', options);
};

export const AddMemberToBusiness = async (options: IAddMemberToBusiness) => {
  return await fetchAPI('v5/api-asker-vn/add-members-to-business', options);
};

export const createBusiness = async (options: ICreateBusinessParams) => {
  return await fetchAPI('v5/api-asker-vn/create-business', options);
};
export const listBusinessTransactions = async (options: IListBusinessTransactions) => {
  return await fetchAPI('v5/api-asker-vn/list-business-transactions', options);
};

export const getTotalTopUp = async (options: IGetTotalTopUpParams) => {
  return await fetchAPI('v5/api-asker-vn/get-total-topup-bpay', options);
};

export const topUpMemberLevel = async (options: ITopUpMemberLevelParams) => {
  return await fetchAPI('v5/api-asker-vn/topup-members-level', options);
};

export const getTotalRevoke = async (options: IGetTotalRevokeParams) => {
  return await fetchAPI('v5/api-asker-vn/get-total-revoke-bpay', options);
};

export const revokeMemberLevel = async (options: IRevokeMemberLevelParams) => {
  return await fetchAPI('v5/api-asker-vn/revoke-members-level', options);
};
export const sendBusinessReport = async (options: ISendBusinessReport) => {
  return await fetchAPI('v5/api-asker-vn/send-business-report', options);
};

export const getBusinessTopUpSettingInfo = async (options: IParamsGetBusinessTopUpSettingInfo) => {
  return await fetchAPI('v5/api-asker-vn/get-business-topup-setting-info', options);
};
