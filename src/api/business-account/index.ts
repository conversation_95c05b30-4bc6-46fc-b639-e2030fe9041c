import { IRespond } from '@helper';

import * as ID_API from './id';
import * as TH_API from './th';
import {
  IAddMemberToBusiness,
  IAddMemberToLevel,
  ICreateBusinessLevel,
  ICreateBusinessParams,
  IGetTotalRevokeParams,
  IGetTotalTopUpParams,
  IListBusinessMemberTransactions,
  IListBusinessTransactions,
  IListMember,
  IParamsGetBusinessTopUpSettingInfo,
  IRemoveBusinessLevel,
  IRemoveMemberFormBusiness,
  IRemoveMemberFormGroup,
  IResponseGetBusinessTopUpSettingInfo,
  IRevokeMemberLevelParams,
  ISendBusinessReport,
  ITopUpMemberLevelParams,
  ITotalTopUpResponse,
  IUpdateBusinessLevel,
  IUpdateGroupName,
  IVerifyMember,
} from './type';
import * as VN_API from './vn';

export const getListMember = async (options: IListMember) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getListMember,
    TH: TH_API.getListMember,
    ID: ID_API.getListMember,
  };
  return await combine[global.isoCode](params);
};

export const updateGroupName = async (options: IUpdateGroupName) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.updateGroupName,
    TH: TH_API.updateGroupName,
    ID: ID_API.updateGroupName,
  };
  return await combine[global.isoCode](params);
};
export const removeMemberFromBusiness = async (options: IRemoveMemberFormBusiness) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.removeMemberFromBusiness,
    TH: TH_API.removeMemberFromBusiness,
    ID: ID_API.removeMemberFromBusiness,
  };
  return await combine[global.isoCode](params);
};

export const removeMemberFromGroup = async (options: IRemoveMemberFormGroup) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.removeMemberFromGroup,
    TH: TH_API.removeMemberFromGroup,
    ID: ID_API.removeMemberFromGroup,
  };
  return await combine[global.isoCode](params);
};

export const listBusinessMemberTransactions = async (options: IListBusinessMemberTransactions) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.listBusinessMemberTransactions,
    TH: TH_API.listBusinessMemberTransactions,
    ID: ID_API.listBusinessMemberTransactions,
  };
  return await combine[global.isoCode](params);
};

export const addMemberToLevel = async (options: IAddMemberToLevel) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.addMemberToLevel,
    TH: TH_API.addMemberToLevel,
    ID: ID_API.addMemberToLevel,
  };
  return await combine[global.isoCode](params);
};

export const updateBusinessLevel = async (options: IUpdateBusinessLevel) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.updateBusinessLevel,
    TH: TH_API.updateBusinessLevel,
    ID: ID_API.updateBusinessLevel,
  };
  return await combine[global.isoCode](params);
};

export const removeBusinessLevel = async (options: IRemoveBusinessLevel) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.removeBusinessLevel,
    TH: TH_API.removeBusinessLevel,
    ID: ID_API.removeBusinessLevel,
  };
  return await combine[global.isoCode](params);
};

export const createBusinessLevel = async (options: ICreateBusinessLevel) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.createBusinessLevel,
    TH: TH_API.createBusinessLevel,
    ID: ID_API.createBusinessLevel,
  };
  return await combine[global.isoCode](params);
};

export const verifyMember = async (options: IVerifyMember) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.verifyMember,
    TH: TH_API.verifyMember,
    ID: ID_API.verifyMember,
  };
  return await combine[global.isoCode](params);
};

export const addMemberToBusiness = async (options: IAddMemberToBusiness) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.AddMemberToBusiness,
    TH: TH_API.AddMemberToBusiness,
    ID: ID_API.AddMemberToBusiness,
  };
  return await combine[global.isoCode](params);
};

export const createBusinessAPI = async (options: ICreateBusinessParams) => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.createBusiness,
    TH: TH_API.createBusiness,
    ID: ID_API.createBusiness,
  };
  return await combine[global.isoCode](params);
};

export const getTotalTopUpAPI = async (options: IGetTotalTopUpParams): Promise<IRespond<ITotalTopUpResponse>> => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.getTotalTopUp,
    TH: TH_API.getTotalTopUp,
    ID: ID_API.getTotalTopUp,
  };
  return await combine[global.isoCode](params);
};

export const topUpMemberLevelAPI = async (options: ITopUpMemberLevelParams): Promise<IRespond<null>> => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.topUpMemberLevel,
    TH: TH_API.topUpMemberLevel,
    ID: ID_API.topUpMemberLevel,
  };
  return await combine[global.isoCode](params);
};

export const getTotalRevokeAPI = async (options: IGetTotalRevokeParams): Promise<IRespond<ITotalTopUpResponse>> => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.getTotalRevoke,
    TH: TH_API.getTotalRevoke,
    ID: ID_API.getTotalRevoke,
  };
  return await combine[global.isoCode](params);
};

export const revokeMemberLevelAPI = async (options: IRevokeMemberLevelParams): Promise<IRespond<null>> => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.revokeMemberLevel,
    TH: TH_API.revokeMemberLevel,
    ID: ID_API.revokeMemberLevel,
  };
  return await combine[global.isoCode](params);
};
export const listBusinessTransactionsAPI = async (options: IListBusinessTransactions) => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.listBusinessTransactions,
    TH: TH_API.listBusinessTransactions,
    ID: ID_API.listBusinessTransactions,
  };
  return await combine[global.isoCode](params);
};
export const sendBusinessReportAPI = async (options: ISendBusinessReport) => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.sendBusinessReport,
    TH: TH_API.sendBusinessReport,
    ID: ID_API.sendBusinessReport,
  };
  return await combine[global.isoCode](params);
};

export const getBusinessTopUpSettingInfoAPI = async (
  options: IParamsGetBusinessTopUpSettingInfo,
): Promise<IRespond<IResponseGetBusinessTopUpSettingInfo>> => {
  const params = {
    ...options,
  };
  const combine = {
    VN: VN_API.getBusinessTopUpSettingInfo,
    TH: TH_API.getBusinessTopUpSettingInfo,
    ID: ID_API.getBusinessTopUpSettingInfo,
  };
  return await combine[global.isoCode](params);
};
