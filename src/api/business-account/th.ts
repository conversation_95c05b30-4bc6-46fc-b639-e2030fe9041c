import { fetchAPI } from '@helper';

import {
  IAddMemberToBusiness,
  IAddMemberToLevel,
  ICreateBusinessLevel,
  ICreateBusinessParams,
  IGetTotalRevokeParams,
  IGetTotalTopUpParams,
  IListBusinessMemberTransactions,
  IListBusinessTransactions,
  IListMember,
  IParamsGetBusinessTopUpSettingInfo,
  IRemoveBusinessLevel,
  IRemoveMemberFormBusiness,
  IRemoveMemberFormGroup,
  IRevokeMemberLevelParams,
  ISendBusinessReport,
  ITopUpMemberLevelParams,
  IUpdateBusinessLevel,
  IUpdateGroupName,
  IVerifyMember,
} from './type';

// TODO: check lại endpoint TH
export const getListMember = async (options: IListMember) => {
  return await fetchAPI('v5/api-asker-th/list-members-by-level', options);
};

// TODO: check lại endpoint TH
export const updateGroupName = async (options: IUpdateGroupName) => {
  return await fetchAPI('v5/api-asker-th/update-member-level', options);
};

// TODO: check lại endpoint TH
export const removeMemberFromBusiness = async (options: IRemoveMemberFormBusiness) => {
  return await fetchAPI('v5/api-asker-th/remove-member-from-business', options);
};

// TODO: check lại endpoint TH
export const removeMemberFromGroup = async (options: IRemoveMemberFormGroup) => {
  return await fetchAPI('v5/api-asker-th/remove-members-level', options);
};

// TODO: check lại endpoint TH
export const listBusinessMemberTransactions = async (options: IListBusinessMemberTransactions) => {
  return await fetchAPI('v5/api-asker-th/list-business-member-transactions', options);
};

// TODO: check lại endpoint TH
export const addMemberToLevel = async (options: IAddMemberToLevel) => {
  return await fetchAPI('v5/api-asker-th/add-members-level', options);
};

// TODO: check lại endpoint TH
export const createBusinessLevel = async (options: ICreateBusinessLevel) => {
  return await fetchAPI('v5/api-asker-th/create-business-level', options);
};

// TODO: check lại endpoint TH
export const updateBusinessLevel = async (options: IUpdateBusinessLevel) => {
  return await fetchAPI('v5/api-asker-th/update-business-level', options);
};

// TODO: check lại endpoint TH
export const removeBusinessLevel = async (options: IRemoveBusinessLevel) => {
  return await fetchAPI('v5/api-asker-th/remove-business-level', options);
};

// TODO: check lại endpoint TH

export const verifyMember = async (options: IVerifyMember) => {
  return await fetchAPI('v5/api-asker-th/verify-members', options);
};

// TODO: check lại endpoint TH
export const AddMemberToBusiness = async (options: IAddMemberToBusiness) => {
  return await fetchAPI('v5/api-asker-th/add-members-to-business', options);
};

// TODO: check lại endpoint TH
export const createBusiness = async (options: ICreateBusinessParams) => {
  return await fetchAPI('v5/api-asker-th/create-business', options);
};

// TODO: check lại endpoint TH
export const getTotalTopUp = async (options: IGetTotalTopUpParams) => {
  return await fetchAPI('v5/api-asker-th/get-total-topup-bpay', options);
};

// TODO: check lại endpoint TH
export const topUpMemberLevel = async (options: ITopUpMemberLevelParams) => {
  return await fetchAPI('v5/api-asker-th/topup-members-level', options);
};

// TODO: check lại endpoint TH
export const getTotalRevoke = async (options: IGetTotalRevokeParams) => {
  return await fetchAPI('v5/api-asker-th/get-total-revoke-bpay', options);
};

// TODO: check lại endpoint TH
export const revokeMemberLevel = async (options: IRevokeMemberLevelParams) => {
  return await fetchAPI('v5/api-asker-th/revoke-members-level', options);
};
export const listBusinessTransactions = async (options: IListBusinessTransactions) => {
  return await fetchAPI('v5/api-asker-th/list-business-transactions', options);
};
export const sendBusinessReport = async (options: ISendBusinessReport) => {
  return await fetchAPI('v5/api-asker-th/send-business-report', options);
};

export const getBusinessTopUpSettingInfo = async (options: IParamsGetBusinessTopUpSettingInfo) => {
  return await fetchAPI('v5/api-asker-th/get-business-topup-setting-info', options);
};
