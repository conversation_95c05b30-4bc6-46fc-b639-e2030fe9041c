import { fetchAPI } from '@helper';

/**
 * @param filterBy Object {from: ''}
 * @param sortBy Object {from: ''}
 * @param page Number
 * @param isoCode Number
 * @param limit number
 */
export const getIncentive = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-reward-home-page', options);
};

/**
 @param id String
 */

export const getIncentiveDetail = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-incentive-detail', options);
};

export const redeemGift = async (options) => {
  return await fetchAPI('v5/api-asker-my/redeem-gift', options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getMyRewards = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-my-rewards-v2', options);
};

/**
 @param incentiveId String
 @param isoCode String
 */
export const getGiftDetail = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-gift-detail', options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getMyReward = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-user-gift', options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getTransactionUsed = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-list-point-transaction-used', options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getTransactionReceived = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-list-point-transaction-received', options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getSpecialIncentive = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-special-incentive', options);
};

/**
 * @description Update gift from your friend
 * @param userId String
 * @param sharedUserId String
 * @param giftId String
 */
export const updateGiftFromFriendShare = async (options) => {
  return await fetchAPI('v5/api-asker-my/share-gift', options);
};

export const getNumberOfGift = async (options) => {
  return await fetchAPI('v5/api-asker-my/count-new-gift-v2', options);
};

/**
 @param userId String
 */

export const getRewardHomePage = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-reward-home-page', options);
};

/**
 * @param filterBy Object {from: ''}
 * @param sortBy Object {from: ''}
 * @param page Number
 * @param isoCode Number
 * @param limit number
 */

export const getListReward = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-list-reward', options);
};

/**
 * @param userId string
 * @param isoCode string
 */
export const getRewardsForYou = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-rewards-for-you', options);
};

/**
 * @param userId string
 * @param isoCode string
 */
export const getRewardsFlashSale = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-rewards-flash-sale', options);
};

/**
 * @param userId string
 */
export const getMemberInfo = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-member-info', options);
};
