import { getVersionAppName } from '@helper';
import { IRespond } from '@src/lib/types';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import { IGiftDetail, IParamsLiXiTasker } from './type';
import * as VN_API from './vn';

/**
 * @param filterBy Object {from: ''}
 * @param sortBy Object {from: ''}
 * @param page Number
 * @param isoCode Number
 * @param limit number
 */
export const getIncentive = async ({ isoCode, filterBy, sortBy, page, limit }) => {
  const options = {
    isoCode: global.isoCode || isoCode,
  };

  if (filterBy) {
    options.filterBy = filterBy;
  }
  if (sortBy) {
    options.sortBy = sortBy;
  }
  if (filterBy) {
    options.filterBy = filterBy;
  }
  if (page) {
    options.page = page;
  }
  if (limit) {
    options.limit = limit;
  }
  const combine = {
    VN: VN_API.getIncentive,
    TH: TH_API.getIncentive,
    ID: ID_API.getIncentive,
    MY: MY_API.getIncentive,
  };
  return await combine[global.isoCode](options);
};
/**
 @param id String
 */

export const getIncentiveDetail = async (id) => {
  const options = {
    id: id,
  };
  const combine = {
    VN: VN_API.getIncentiveDetail,
    TH: TH_API.getIncentiveDetail,
    ID: ID_API.getIncentiveDetail,
    MY: MY_API.getIncentiveDetail,
  };
  return await combine[global.isoCode](options);
};

export const redeemGift = async (giftId: string, data: object) => {
  const options = {
    giftId: giftId,
    data: data,
    appVersion: getVersionAppName(),
  };
  const combine = {
    VN: VN_API.redeemGift,
    TH: TH_API.redeemGift,
    ID: ID_API.redeemGift,
    MY: MY_API.redeemGift,
  };
  return await combine[global.isoCode](options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getMyRewards = async (params) => {
  const options = {
    isoCode: global.isoCode,
    ...params,
  };
  const combine = {
    VN: VN_API.getMyRewards,
    TH: TH_API.getMyRewards,
    ID: ID_API.getMyRewards,
    MY: MY_API.getMyRewards,
  };
  return await combine[global.isoCode](options);
};

/**
 @param incentiveId String
 @param isoCode String
 */
export const getGiftDetail = async (giftId: string): Promise<IRespond<IGiftDetail>> => {
  const options = {
    _id: giftId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getGiftDetail,
    TH: TH_API.getGiftDetail,
    ID: ID_API.getGiftDetail,
    MY: MY_API.getGiftDetail,
  };
  return await combine[global.isoCode](options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getMyReward = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getMyReward,
    TH: TH_API.getMyReward,
    ID: ID_API.getMyReward,
    MY: MY_API.getMyReward,
  };
  return await combine[global.isoCode](options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getTransactionUsed = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getTransactionUsed,
    TH: TH_API.getTransactionUsed,
    ID: ID_API.getTransactionUsed,
    MY: MY_API.getTransactionUsed,
  };
  return await combine[global.isoCode](options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getTransactionReceived = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getTransactionReceived,
    TH: TH_API.getTransactionReceived,
    ID: ID_API.getTransactionReceived,
    MY: MY_API.getTransactionReceived,
  };
  return await combine[global.isoCode](options);
};

/**
 @param userId String
 @param isoCode String
 */
export const getSpecialIncentive = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getSpecialIncentive,
    TH: TH_API.getSpecialIncentive,
    ID: ID_API.getSpecialIncentive,
    MY: MY_API.getSpecialIncentive,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description Update gift from your friend
 * @param userId String
 * @param sharedUserId String
 * @param giftId String
 */
export const updateGiftFromFriendShare = async (sharedUserId, giftId) => {
  const options = {
    sharedUserId: sharedUserId,
    giftId: giftId,
  };
  const combine = {
    VN: VN_API.updateGiftFromFriendShare,
    TH: TH_API.updateGiftFromFriendShare,
    ID: ID_API.updateGiftFromFriendShare,
    MY: MY_API.updateGiftFromFriendShare,
  };
  return await combine[global.isoCode](options);
};

export const getNumberOfGift = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getNumberOfGift,
    TH: TH_API.getNumberOfGift,
    ID: ID_API.getNumberOfGift,
    MY: MY_API.getNumberOfGift,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param filterBy Object {from: ''}
 * @param sortBy Object {from: ''}
 * @param page Number
 * @param isoCode Number
 * @param limit number
 */
export const getRewardHomePage = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getRewardHomePage,
    TH: TH_API.getRewardHomePage,
    ID: ID_API.getRewardHomePage,
    MY: MY_API.getRewardHomePage,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param filterBy Object {from: ''}
 * @param sortBy Object {from: ''}
 * @param page Number
 * @param isoCode Number
 * @param limit number
 */
export const getListReward = async ({ isoCode, filterBy, sortBy, page, limit }) => {
  const options = {
    isoCode: global.isoCode || isoCode,
  };

  if (filterBy) {
    options.filterBy = filterBy;
  }
  if (sortBy) {
    options.sortBy = sortBy;
  }
  if (filterBy) {
    options.filterBy = filterBy;
  }
  if (page) {
    options.page = page;
  }
  if (limit) {
    options.limit = limit;
  }
  const combine = {
    VN: VN_API.getListReward,
    TH: TH_API.getListReward,
    ID: ID_API.getListReward,
    MY: MY_API.getListReward,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description Get list recommend for you in member detail
 * @param userId String
 * @param isoCode String
 */
export const getRewardsForYouService = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getRewardsForYou,
    TH: TH_API.getRewardsForYou,
    ID: ID_API.getRewardsForYou,
    MY: MY_API.getRewardsForYou,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description Get list rewards flash sale
 * @param userId String
 * @param isoCode String
 */
export const getRewardsFlashSaleService = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getRewardsFlashSale,
    TH: TH_API.getRewardsFlashSale,
    ID: ID_API.getRewardsFlashSale,
    MY: MY_API.getRewardsFlashSale,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description Get list rewards flash sale
 * @param userId String
 */
export const getMemberInfo = async () => {
  const combine = {
    VN: VN_API.getMemberInfo,
    TH: TH_API.getMemberInfo,
    ID: ID_API.getMemberInfo,
    MY: MY_API.getMemberInfo,
  };
  return await combine[global.isoCode]();
};

export const liXiTaskerAPI = async (options?: IParamsLiXiTasker) => {
  const combine = {
    VN: VN_API.liXiTasker,
    TH: TH_API.liXiTasker,
    ID: ID_API.liXiTasker,
  };
  return await combine[global.isoCode](options);
};
