import { fetchAPI } from '@helper';

import {
  ICancelComboVoucherSubscriptionAPIParams,
  IGetComboVoucherDetailAPIParams,
  IGetFreeComboVoucher,
  IGetUserComboVoucherDetailAPIParams,
  IListUserComboVoucherAPIParams,
  IPayComboVoucher,
} from './type';

export const getListComboVoucherAPI = async () => {
  return await fetchAPI('v5/api-asker-vn/get-list-combo-voucher-v2');
};

export const getListUserComboVoucherAPI = async (options: IListUserComboVoucherAPIParams) => {
  return await fetchAPI('v5/api-asker-vn/get-list-user-combo-voucher', options);
};
export const getListComboVoucherTransactionHistoriesAPI = async () => {
  return await fetchAPI('v5/api-asker-vn/get-list-combo-voucher-transaction-histories');
};

export const getUserComboVoucherDetailAPI = async (options: IGetUserComboVoucherDetailAPIParams) => {
  return await fetchAPI('v5/api-asker-vn/get-user-combo-voucher-detail', options);
};

export const getComboVoucherDetailAPI = async (options: IGetComboVoucherDetailAPIParams) => {
  return await fetchAPI('v5/api-asker-vn/get-combo-voucher-detail', options);
};

export const CancelComboVoucherSubscriptionAPI = async (options: ICancelComboVoucherSubscriptionAPIParams) => {
  return await fetchAPI('v5/api-asker-vn/cancel-combo-voucher-subscription', options);
};

export const getFreeComboVoucherAPI = async (options: IGetFreeComboVoucher) => {
  return await fetchAPI('v5/api-asker-vn/get-free-combo-voucher', options);
};

export const payComboVoucherAPI = async (options: IPayComboVoucher) => {
  return await fetchAPI('v5/payment-vn/pay-combo-voucher-vn', options);
};
