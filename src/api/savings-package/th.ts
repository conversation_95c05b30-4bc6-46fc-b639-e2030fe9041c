import { fetchAPI } from '@helper';

import {
  ICancelComboVoucherSubscriptionAPIParams,
  IGetComboVoucherDetailAPIParams,
  IGetFreeComboVoucher,
  IGetUserComboVoucherDetailAPIParams,
  IListUserComboVoucherAPIParams,
} from './type';

export const getListComboVoucherAPI = async () => {
  return await fetchAPI('v5/api-asker-th/get-list-combo-voucher-v2');
};
export const getListUserComboVoucherAPI = async (options: IListUserComboVoucherAPIParams) => {
  return await fetchAPI('v5/api-asker-th/get-list-user-combo-voucher', options);
};
export const getListComboVoucherTransactionHistoriesAPI = async () => {
  return await fetchAPI('v5/api-asker-th/get-list-combo-voucher-transaction-histories');
};
export const getUserComboVoucherDetailAPI = async (options: IGetUserComboVoucherDetailAPIParams) => {
  return await fetchAPI('v5/api-asker-th/get-user-combo-voucher-detail', options);
};

export const getComboVoucherDetailAPI = async (options: IGetComboVoucherDetailAPIParams) => {
  return await fetchAPI('v5/api-asker-th/get-combo-voucher-detail-th', options);
};

export const CancelComboVoucherSubscriptionAPI = async (options: ICancelComboVoucherSubscriptionAPIParams) => {
  return await fetchAPI('v5/api-asker-th/cancel-combo-voucher-subscription', options);
};

export const getFreeComboVoucherAPI = async (options: IGetFreeComboVoucher) => {
  return await fetchAPI('v5/api-asker-th/get-free-combo-voucher', options);
};

// để tạm
export const payComboVoucherAPI = async (options: IGetFreeComboVoucher) => {
  return await fetchAPI('v5/payment/pay-combo-voucher-th', options);
};
