import { fetchAPI } from '@helper';

import { IParamsGetBundleVoucher, IRedeemBundleVoucher } from './type';

export const getBundleVoucher = async (options: IParamsGetBundleVoucher) => {
  return await fetchAPI('v3/api-asker-th/get-list-bundle-voucher', options);
};

export const redeemBundleVoucher = async (options: IRedeemBundleVoucher) => {
  return await fetchAPI('v3/api-asker-th/redeem-bundle-voucher', options);
};
