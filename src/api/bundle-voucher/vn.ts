import { fetchAPI } from '@helper';

import { IParamsGetBundleVoucher, IRedeemBundleVoucher } from './type';

export const getBundleVoucher = async (options: IParamsGetBundleVoucher) => {
  return await fetchAPI('v5/api-asker-vn/get-list-bundle-voucher', options);
};

export const redeemBundleVoucher = async (options: IRedeemBundleVoucher) => {
  return await fetchAPI('v5/api-asker-vn/redeem-bundle-voucher', options);
};
