import * as ID_API from './id';
import * as TH_API from './th';
import { IParamsGetBundleVoucher, IRedeemBundleVoucher } from './type';
import * as VN_API from './vn';

/**
 * @param userId string
 */
export const getBundleVoucher = async (options: IParamsGetBundleVoucher) => {
  const combine = {
    VN: VN_API.getBundleVoucher,
    TH: TH_API.getBundleVoucher,
    ID: ID_API.getBundleVoucher,
  };
  return await combine[global.isoCode](options);
};

export const redeemBundleVoucher = async (options: IRedeemBundleVoucher) => {
  const combine = {
    VN: VN_API.redeemBundleVoucher,
    TH: TH_API.redeemBundleVoucher,
    ID: ID_API.redeemBundleVoucher,
  };
  return await combine[global.isoCode](options);
};
