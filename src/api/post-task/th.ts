import { fetchAPI } from '@helper';
import { IPostTaskHairStylingState } from '@src/screens/services/beauty-care/hair-styling/redux/reducer/types';
import { IPostTaskMakeupState } from '@src/screens/services/beauty-care/makeup/redux/reducer/types';
import { IPostTaskNailState } from '@src/screens/services/beauty-care/nail/redux/reducer/types';

import { IParamAddActivatedServices, IParamBookTaskForceTasker, IParamGetServices } from './type';

/**
 * @param serviceId {{string}}
 * @param date {{date}} date of task
 */
export const checkTaskSameTime = async (options) => {
  return await fetchAPI('v5/api-asker-th/check-task-sametime', options);
};

export const postTaskCleaning = async (options) => {
  return await fetchAPI('v5/booking-th/home-cleaning', options, 'post', false);
};

export const postTaskAirConditioner = async (options) => {
  return await fetchAPI('v5/booking-th/air-conditioner', options, 'post', false);
};

export const postTaskDeepCleaning = async (options) => {
  return await fetchAPI('v5/booking-th/deep-cleaning', options, 'post', false);
};

export const postTaskSofa = async (options) => {
  return await fetchAPI('v5/booking-th/sofa', options, 'post', false);
};

export const postTaskDisinfection = async (options) => {
  return await fetchAPI('v5/booking-th/disinfection', options, 'post', false);
};

export const postTaskWashingHeater = async (options) => {
  return await fetchAPI('v5/booking-th/water-heater', options, 'post', false);
};

export const postTaskOfficeCleaning = async (options) => {
  return await fetchAPI('v5/booking-th/office-cleaning', options, 'post', false);
};

export const postTaskOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v5/booking-th/carpet-cleaning', options, 'post', false);
};

export const postTaskChildCare = async (options) => {
  return await fetchAPI('v5/booking-th/child-care', options, 'post', false);
};

export const postTaskMassage = async (options) => {
  return await fetchAPI('v5/booking-th/massage', options, 'post', false);
};

export const postTaskHomeMoving = async (options) => {
  return await fetchAPI('v5/booking-th/home-moving', options, 'post', false);
};

export const postIndustrialCleaning = async (options) => {
  return await fetchAPI('v5/booking-th/industrial-cleaning', options, 'post', false);
};

export const postBeautyCare = async (options) => {
  return await fetchAPI('v5/booking-th/beauty-care', options, 'post', false);
};

export const bookTaskForceTasker = async (options: IParamBookTaskForceTasker) => {
  return await fetchAPI('v5/booking-th/book-task-force-tasker', options, 'post', false);
};

export const postTaskLaundry = async (options) => {
  return await fetchAPI('v5/booking/laundry', options, 'post', false);
};
export const getCategories = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-category-grocery-assistant', options, 'post', false);
};

export const findPopularProductGrocery = async (options) => {
  return await fetchAPI('v5/api-asker-th/find-popular-grocery-assistant', options, 'post', false);
};

export const updateShoppingCart = async (options) => {
  return await fetchAPI('v5/api-asker-th/update-shopping-cart', options, 'post', false);
};

export const getShoppingCart = async () => {
  return await fetchAPI('v5/api-asker-th/get-shopping-cart-by-userId', {}, 'post', false);
};

export const groceryFindProduct = async (options) => {
  return await fetchAPI('v5/api-asker-th/find-product-grocery-assistant', options, 'post', false);
};

export const removeProductCart = async (options) => {
  return await fetchAPI('v5/api-asker-th/remove-shopping-card-by-id', options, 'post', false);
};

export const postTaskHousekeeping = async (options) => {
  return await fetchAPI('v5/booking-th/housekeeping-v2', options, 'post', false);
};

export const postTaskElderlyCare = async (options) => {
  return await fetchAPI('v5/booking-th/elderly-care', options, 'post', false);
};

export const postTaskPatientCare = async (options) => {
  return await fetchAPI('v5/booking-th/patient-care', options, 'post', false);
};
export const addActivatedServices = async (options: IParamAddActivatedServices) => {
  return await fetchAPI('v5/api-asker-th/add-activated-services', options, 'post');
};

export const postTaskWashingMachine = async (options) => {
  return await fetchAPI('v5/booking-th/washing-machine', options, 'post', false);
};

export const postTaskIroning = async (options) => {
  return await fetchAPI('v5/booking-th/ironing', options, 'post', false);
};

export const getServices = async (options: IParamGetServices) => {
  return await fetchAPI('v5/api-asker-th/get-services-v2', options, 'post');
};

export const postTaskHairStylingAPI = async (options: IPostTaskHairStylingState['detailHairStyling']) => {
  return await fetchAPI('v5/booking-th/hair-styling', options);
};

export const postTaskMakeupAPI = async (options: IPostTaskMakeupState['detailMakeup']) => {
  return await fetchAPI('v5/booking-th/makeup', options);
};

export const postTaskNailAPI = async (options: IPostTaskNailState['detailNail']) => {
  return await fetchAPI('v5/booking-th/nail', options);
};
