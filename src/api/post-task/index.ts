import { isEmpty } from 'lodash';

import { ID, MY, SERVICES, TH, VN } from '@config';
import { fetchAPI, getIsoCodeGlobal, IRespond } from '@helper';
import { IPostTaskHairStylingState } from '@src/screens/services/beauty-care/hair-styling/redux/reducer/types';
import { IPostTaskMakeupState } from '@src/screens/services/beauty-care/makeup/redux/reducer/types';
import { IPostTaskNailState } from '@src/screens/services/beauty-care/nail/redux/reducer/types';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import { IParamAddActivatedServices, IParamBookTaskForceTasker, IParamGetServices } from './type';
import * as VN_API from './vn';

/**
 * @param serviceId {{string}}
 * @param date {{date}} date of task
 */
export const checkTaskSameTime = async (serviceId, taskDate) => {
  const options = {
    // isMockAPI: true,
    serviceId,
    taskDate,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.checkTaskSameTime,
    TH: TH_API.checkTaskSameTime,
    ID: ID_API.checkTaskSameTime,
    MY: MY_API.checkTaskSameTime,
  };
  return await combine[global.isoCode](options);
};

export const postTaskCleaning = async (dataTask) => {
  const options = {
    // isMockAPI: true,
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskCleaning,
    TH: TH_API.postTaskCleaning,
    ID: ID_API.postTaskCleaning,
    MY: MY_API.postTaskCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskAirConditioner = async (dataTask) => {
  const options = {
    // isMockAPI: true,
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskAirConditioner,
    TH: TH_API.postTaskAirConditioner,
    ID: ID_API.postTaskAirConditioner,
    MY: MY_API.postTaskAirConditioner,
  };
  return await combine[global.isoCode](options);
};

export const postTaskLaundry = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskLaundry,
    TH: TH_API.postTaskLaundry,
    ID: ID_API.postTaskLaundry,
    MY: MY_API.postTaskLaundry,
  };
  return await combine[global.isoCode](options);
};

export const postTaskDeepCleaning = async (dataTask) => {
  const options = {
    // isMockAPI: true,
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskDeepCleaning,
    TH: TH_API.postTaskDeepCleaning,
    ID: ID_API.postTaskDeepCleaning,
    MY: MY_API.postTaskDeepCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskHomeCooking = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  return await fetchAPI('v5/booking/home-cooking', options, 'post', false);
};

export const postTaskSofa = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskSofa,
    TH: TH_API.postTaskSofa,
    ID: ID_API.postTaskSofa,
    MY: MY_API.postTaskSofa,
  };
  return await combine[global.isoCode](options);
};

export const postTaskGroceryAssistant = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  return await fetchAPI('v5/booking/grocery-assistant', options, 'post', false);
};

export const postTaskGroceryAssistantV3 = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  return await fetchAPI('v5/booking/grocery-assistant', options, 'post', false);
};

export const postTaskHousekeeping = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskHousekeeping,
    TH: TH_API.postTaskHousekeeping,
    ID: ID_API.postTaskHousekeeping,
    MY: MY_API.postTaskHousekeeping,
  };
  return await combine[global.isoCode](options);
};

export const postTaskElderlyCare = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    [VN]: VN_API.postTaskElderlyCare,
    [TH]: TH_API.postTaskElderlyCare,
    [ID]: ID_API.postTaskElderlyCare,
    [MY]: MY_API.postTaskElderlyCare,
  };
  return await combine[getIsoCodeGlobal()](options);
};

export const postTaskPatientCare = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskPatientCare,
    TH: TH_API.postTaskPatientCare,
    ID: ID_API.postTaskPatientCare,
    MY: MY_API.postTaskPatientCare,
  };
  return await combine[global.isoCode](options);
};

export const postTaskDisinfection = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskDisinfection,
    TH: TH_API.postTaskDisinfection,
    ID: ID_API.postTaskDisinfection,
    MY: MY_API.postTaskDisinfection,
  };
  return await combine[global.isoCode](options);
};

export const postTaskChildCare = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskChildCare,
    TH: TH_API.postTaskChildCare,
    ID: ID_API.postTaskChildCare,
    MY: MY_API.postTaskChildCare,
  };
  return await combine[global.isoCode](options);
};

export const postTaskOfficeCleaning = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskOfficeCleaning,
    TH: TH_API.postTaskOfficeCleaning,
    ID: ID_API.postTaskOfficeCleaning,
    MY: MY_API.postTaskOfficeCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskOfficeCarpetCleaning = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskOfficeCarpetCleaning,
    TH: TH_API.postTaskOfficeCarpetCleaning,
    ID: ID_API.postTaskOfficeCarpetCleaning,
    MY: MY_API.postTaskOfficeCarpetCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskWashingMachine = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskWashingMachine,
    TH: TH_API.postTaskWashingMachine,
    ID: ID_API.postTaskWashingMachine,
    MY: MY_API.postTaskWashingMachine,
  };
  return await combine[global.isoCode](options);
};

export const postTaskWaterHeater = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskWashingHeater,
    TH: TH_API.postTaskWashingHeater,
    ID: ID_API.postTaskWashingHeater,
    MY: MY_API.postTaskWashingHeater,
  };
  return await combine[global.isoCode](options);
};

export const postTaskHomeMoving = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskHomeMoving,
    TH: TH_API.postTaskHomeMoving,
    ID: ID_API.postTaskHomeMoving,
    MY: MY_API.postTaskHomeMoving,
  };
  return await combine[global.isoCode](options);
};

export const postTaskMassage = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: null,
    TH: TH_API.postTaskMassage,
    ID: ID_API.postTaskMassage,
    MY: MY_API.postTaskMassage,
  };
  return await combine[global.isoCode](options);
};

export const postIndustrialCleaning = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postIndustrialCleaning,
    TH: TH_API.postIndustrialCleaning,
    ID: ID_API.postIndustrialCleaning,
  };
  return await combine[global.isoCode](options);
};
export const postTaskHairStyling = (
  ...args: [IPostTaskHairStylingState['detailHairStyling']]
): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.postTaskHairStylingAPI,
    [TH]: TH_API.postTaskHairStylingAPI,
    [ID]: ID_API.postTaskHairStylingAPI,
    [MY]: MY_API.postTaskHairStylingAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const postTaskMakeup = (...args: [IPostTaskMakeupState['detailMakeup']]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.postTaskMakeupAPI,
    [TH]: TH_API.postTaskMakeupAPI,
    [ID]: ID_API.postTaskMakeupAPI,
    [MY]: MY_API.postTaskMakeupAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const postTaskNail = (...args: [IPostTaskNailState['detailNail']]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.postTaskNailAPI,
    [TH]: TH_API.postTaskNailAPI,
    [ID]: ID_API.postTaskNailAPI,
    [MY]: MY_API.postTaskNailAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const postTaskProvider = async (dataTask, serviceName) => {
  if (dataTask?.dateOptions?.length <= 1) {
    delete dataTask.dateOptions;
  }
  if (!isEmpty(dataTask?.forceTasker)) {
    return await bookTaskForceTasker(dataTask);
  }
  switch (serviceName) {
    case SERVICES.CLEANING:
      return await postTaskCleaning(dataTask);
    case SERVICES.AIR_CONDITIONER:
      return await postTaskAirConditioner(dataTask);
    case SERVICES.LAUNDRY:
      return await postTaskLaundry(dataTask);
    case SERVICES.DEEP_CLEANING:
      return await postTaskDeepCleaning(dataTask);
    case SERVICES.HOME_COOKING:
      return await postTaskHomeCooking(dataTask);
    case SERVICES.SOFA:
      return await postTaskSofa(dataTask);
    case SERVICES.GROCERY_ASSISTANT:
      return await postTaskGroceryAssistantV3(dataTask);
    case SERVICES.HOUSE_KEEPING:
      return await postTaskHousekeeping(dataTask);
    case SERVICES.ELDERLY_CARE:
      return await postTaskElderlyCare(dataTask);
    case SERVICES.PATIENT_CARE:
      return await postTaskPatientCare(dataTask);
    case SERVICES.DISINFECTION_SERVICE:
      return await postTaskDisinfection(dataTask);
    case SERVICES.CHILD_CARE:
      return await postTaskChildCare(dataTask);
    case SERVICES.OFFICE_CLEANING:
      return await postTaskOfficeCleaning(dataTask);
    case SERVICES.WASHING_MACHINE:
      return await postTaskWashingMachine(dataTask);
    case SERVICES.WATER_HEATER:
      return await postTaskWaterHeater(dataTask);
    case SERVICES.OFFICE_CARPET_CLEANING:
      return await postTaskOfficeCarpetCleaning(dataTask);
    case SERVICES.HOME_MOVING:
      return await postTaskHomeMoving(dataTask);
    case SERVICES.MASSAGE:
      return await postTaskMassage(dataTask);
    case SERVICES.INDUSTRIAL_CLEANING:
      return await postIndustrialCleaning(dataTask);
    case SERVICES.IRONING:
      return await postTaskIroning(dataTask);
    case SERVICES.HAIR_STYLING:
      return await postTaskHairStyling(dataTask);
    case SERVICES.MAKEUP:
      return await postTaskMakeup(dataTask);
    case SERVICES.NAIL:
      return await postTaskNail(dataTask);
    default:
      break;
  }
};

export const getCategories = async (categoryId, storeId) => {
  const options = {
    categoryId: categoryId,
    storeId: storeId,
  };
  const combine = {
    VN: VN_API.getCategories,
    TH: TH_API.getCategories,
    ID: ID_API.getCategories,
  };
  return await combine[global.isoCode](options);
};

export const findPopularProductGrocery = async (storeId) => {
  const options = { storeId: storeId };
  const combine = {
    VN: VN_API.findPopularProductGrocery,
    TH: TH_API.findPopularProductGrocery,
    ID: ID_API.findPopularProductGrocery,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description product search
 * @param {text, language, page} Find by text
 * @param {subCategoryId, page} find subCategory
 * @param {productId} find by product
 */

export const groceryFindProduct = async ({ text, language, subCategoryId, productId, page = 1, limit, storeId }) => {
  const options = { storeId: storeId };

  // Find by text
  if (text && language) {
    options.text = text;
    options.language = language;
    options.page = page;
  }

  // Find subCategory
  if (subCategoryId) {
    options.subCategoryId = subCategoryId;
    options.page = page;
  }

  if (productId) {
    options.productId = productId;
  }

  if (limit) {
    options.limit = limit;
  }
  const combine = {
    VN: VN_API.groceryFindProduct,
    TH: TH_API.groceryFindProduct,
    ID: ID_API.groceryFindProduct,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description Update shoping cart
 *
 * @param userId
 * @param action String ('INCREASE', 'DECREASE', 'REMOVE')
 *
 * @param newProduct: {price, text, categoryId, quantity}  For user add manual
 * @param isUserCreated : true (user add manual)
 *
 * **/
export const updateShoppingCart = async ({ productId, action, price, text, isUserCreated, categoryId, quantity }) => {
  const options = {
    action: action,
  };

  if (productId) {
    options.productId = productId;
  }

  if (quantity) {
    options.quantity = quantity;
  }

  // This param for user add product manual
  if (isUserCreated) {
    options.newProduct = {
      text: text,
      price: price,
      categoryId: categoryId,
    };
    options.isUserCreated = true;
  }
  const combine = {
    VN: VN_API.updateShoppingCart,
    TH: TH_API.updateShoppingCart,
    ID: ID_API.updateShoppingCart,
  };
  return await combine[global.isoCode](options);
};

export const getShoppingCart = async () => {
  const combine = {
    VN: VN_API.getShoppingCart,
    TH: TH_API.getShoppingCart,
    ID: ID_API.getShoppingCart,
  };
  return await combine[global.isoCode]();
};

export const removeProductCart = async (shoppingCardId) => {
  const options = {
    shoppingCardId: shoppingCardId,
  };
  const combine = {
    VN: VN_API.removeProductCart,
    TH: TH_API.removeProductCart,
    ID: ID_API.removeProductCart,
  };
  return await combine[global.isoCode](options);
};

export const bookTaskForceTasker = (...args: [IParamBookTaskForceTasker]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.bookTaskForceTasker,
    [TH]: TH_API.bookTaskForceTasker,
    [ID]: ID_API.bookTaskForceTasker,
    [MY]: MY_API.bookTaskForceTasker,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const postTaskIroning = async (dataTask) => {
  const options = {
    // isMockAPI: true,
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskIroning,
    TH: TH_API.postTaskIroning,
    ID: ID_API.postTaskIroning,
  };
  return await combine[global.isoCode](options);
};

export const addActivatedServices = (...args: [IParamAddActivatedServices]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.addActivatedServices,
    [TH]: TH_API.addActivatedServices,
    [ID]: ID_API.addActivatedServices,
  };
  const combine = apis[getIsoCodeGlobal()];
  return combine?.(...args);
};

export const getServicesAPI = (...args: [IParamGetServices]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.getServices,
    [TH]: TH_API.getServices,
    [ID]: ID_API.getServices,
    [MY]: MY_API.getServices,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};
