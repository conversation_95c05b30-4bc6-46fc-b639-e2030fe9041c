import { fetchAPI } from '@helper';
import { IPostTaskHairStylingState } from '@src/screens/services/beauty-care/hair-styling/redux/reducer/types';
import { IPostTaskMakeupState } from '@src/screens/services/beauty-care/makeup/redux/reducer/types';
import { IPostTaskNailState } from '@src/screens/services/beauty-care/nail/redux/reducer/types';

import { IParamAddActivatedServices, IParamBookTaskForceTasker, IParamGetServices } from './type';

export const checkTaskSameTime = async (options) => {
  return await fetchAPI('v5/api-asker-my/check-task-sametime', options);
};

export const postTaskCleaning = async (options) => {
  return await fetchAPI('v5/booking-my/home-cleaning', options, 'post', false);
};

export const postTaskAirConditioner = async (options) => {
  return await fetchAPI('v5/booking-my/air-conditioner', options, 'post', false);
};

export const postTaskDeepCleaning = async (options) => {
  return await fetchAPI('v5/booking-my/deep-cleaning', options, 'post', false);
};

export const postTaskOfficeCleaning = async (options) => {
  return await fetchAPI('v5/booking-my/office-cleaning', options, 'post', false);
};

export const postTaskWashingMachine = async (options) => {
  return await fetchAPI('v5/booking-my/washing-machine', options, 'post', false);
};

export const postTaskWashingHeater = async (options) => {
  return await fetchAPI('v5/booking-my/water-heater', options, 'post', false);
};

export const postTaskOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v5/booking-my/carpet-cleaning', options, 'post', false);
};

export const postTaskSofa = async (options) => {
  return await fetchAPI('v5/booking-my/sofa', options, 'post', false);
};

export const postTaskDisinfection = async (options) => {
  return await fetchAPI('v5/booking-my/disinfection', options, 'post', false);
};

export const postTaskMassage = async (options) => {
  return await fetchAPI('v5/booking-my/massage', options, 'post', false);
};

export const postTaskHomeMoving = async (options) => {
  return await fetchAPI('v5/booking-my/home-moving', options, 'post', false);
};

export const bookTaskForceTasker = async (options: IParamBookTaskForceTasker) => {
  return await fetchAPI('v5/booking-my/book-task-force-tasker', options, 'post', false);
};

export const postTaskLaundry = async (options) => {
  return await fetchAPI('v5/booking-my/laundry', options, 'post', false);
};

export const postTaskHousekeeping = async (options) => {
  return await fetchAPI('v5/booking-my/housekeeping-v2', options, 'post', false);
};

export const postTaskElderlyCare = async (options) => {
  return await fetchAPI('v5/booking-my/elderly-care', options, 'post', false);
};

export const postTaskPatientCare = async (options) => {
  return await fetchAPI('v5/booking-my/patient-care', options, 'post', false);
};

export const postTaskChildCare = async (options) => {
  return await fetchAPI('v5/booking-my/child-care', options, 'post', false);
};

export const addActivatedServices = async (options: IParamAddActivatedServices) => {
  return await fetchAPI('v5/api-asker-my/add-activated-services', options, 'post');
};

export const getServices = async (options: IParamGetServices) => {
  return await fetchAPI('v5/api-asker-my/get-services-v2', options, 'post');
};

export const postTaskHairStylingAPI = async (options: IPostTaskHairStylingState['detailHairStyling']) => {
  return await fetchAPI('v5/booking-my/hair-styling', options);
};

export const postTaskMakeupAPI = async (options: IPostTaskMakeupState['detailMakeup']) => {
  return await fetchAPI('v5/booking-my/makeup', options);
};

export const postTaskNailAPI = async (options: IPostTaskNailState['detailNail']) => {
  return await fetchAPI('v5/booking-my/nail', options);
};
