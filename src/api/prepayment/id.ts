import { fetchAP<PERSON>, getIPAddress } from '@helper';

import { ICancelTryPaymentProcessParams, ITrackTryToProcessPaymentParams } from './types';

export const prepayment = async (options) => {
  options.shopperIP = await getIPAddress();
  return await fetchAPI('v5/payment-indo/prepay-task', options);
};

export const updatePaymentMethod = async (options) => {
  return await fetchAPI('v5/update-task-indo/update-payment-method', options);
};

export const cancelPrepayment = async (options) => {
  // "taskId": "xxx",
  // "userId": "xxxx"
  return await fetchAPI('v5/cancel-task-indo/cancel-prepay-task', options);
};

export const cancelTryPaymentProcess = async (options: ICancelTryPaymentProcessParams) => {
  // {userId, taskId, paymentMethod}
  return await fetchAPI('v5/api-asker-indo/cancel-task-payment-process', options);
};

export const trackTryToProcessPayment = async (options: ITrackTryToProcessPaymentParams) => {
  // {userId, taskId, paymentMethod}
  return await fetchAPI('v5/api-asker-indo/track-asker-try-to-process-payment', options);
};
