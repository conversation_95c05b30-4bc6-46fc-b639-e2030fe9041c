import { fetchAPI, getIPAddress } from '@helper';

import { ICancelTryPaymentProcessParams, ITrackTryToProcessPaymentParams } from './types';

export const prepayment = async (options) => {
  options.shopperIP = await getIPAddress();
  return await fetchAPI('v5/payment/prepayment-task-vn-by-task-id', options);
};

export const updatePaymentMethod = async (options) => {
  return await fetchAPI('v5/update-task-vn/update-payment-method', options);
};

export const cancelPrepayment = async (options) => {
  // "taskId": "xxx",
  // "userId": "xxxx"
  return await fetchAPI('v5/cancel-task-vn/cancel-prepay-task', options);
};

export const cancelTryPaymentProcess = async (options: ICancelTryPaymentProcessParams) => {
  // {userId, taskId, paymentMethod}
  return await fetchAPI('v5/api-asker-vn/cancel-task-payment-process', options);
};

export const trackTryToProcessPayment = async (options: ITrackTryToProcessPaymentParams) => {
  // {userId, taskId, paymentMethod}
  return await fetchAPI('v5/api-asker-vn/track-asker-try-to-process-payment', options);
};
