import { fetchAP<PERSON>, getIsoCodeGlobal } from '@helper';

import {
  IApproveUpdateDetailRequest,
  IParamGetTaskerService,
  IParamsAcceptSuggestionNewDateOption,
  IParamsAcceptTask,
  IParamsAskerCreateRequestUpdateDetailV4,
  IParamsGetIncreaseDurationExtraMoney,
  IParamsRejectSuggestionNewDateOption,
  IParamsRemindTasker,
  IParamsSendCallChatMessage,
  IParamsSetDisabledActionChatMessageV2,
  IRejectUpdateDetailRequest,
} from './type';

export const getConversationAPI = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-chat-list-chat-message-v2', options);
};

export const getChatHistoryAPI = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-chat-history-by-task', options);
};

export const translateMessage = async (options) => {
  return await fetchAPI('v5/api-asker-vn/translate-message', options);
};

export const sendChatMessage = async (options) => {
  return await fetchAPI('v5/api-asker-vn/send-chat-message-v2', options);
};

export const getListChat = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-list-chat-v2', options);
};

export const setIsReadChatMessage = async (options) => {
  return await fetchAPI('v5/api-asker-vn/set-is-read-chat-message-v2', options);
};

export const getIncreaseDurationExtraMoney = async (options) => {
  return await fetchAPI('v5/update-task-vn/get-increase-duration-extra-money', options);
};

export const rejectRequestIncreaseDuration = async (options) => {
  return await fetchAPI('v5/update-task-vn/asker-reject-increase-duration-request', options);
};

export const approveRequestIncreaseDuration = async (options) => {
  return await fetchAPI('v5/update-task-vn/asker-approve-increase-duration-request', options);
};

export const archiveConversation = async (options) => {
  return await fetchAPI('v5/api-asker-vn/archive-chat-message', options);
};

export const removeConversation = async (options) => {
  return await fetchAPI('v5/api-asker-vn/delete-chat-message', options);
};

export const getTaskerServices = async (params: IParamGetTaskerService) => {
  const options = {
    isoCode: getIsoCodeGlobal(),
    ...params,
  };
  return await fetchAPI('v5/api-asker-vn/get-tasker-services', options);
};

export const getMoreMessage = async (options) => {
  return await fetchAPI('v5/api-asker-vn/load-more-messages', options);
};

export const approveUpdateDetailRequest = async (options: IApproveUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-vn/asker-approve-update-detail-request', options);
};

export const rejectUpdateDetailRequest = async (options: IRejectUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-vn/asker-reject-update-detail-request', options);
};

export const getUpdateDetailExtraMoney = async (options: IParamsGetIncreaseDurationExtraMoney) => {
  return await fetchAPI('v5/update-task-vn/get-update-detail-extra-money', options);
};

export const rejectRequestUpdateDetailV4 = async (options: IRejectUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-vn/asker-reject-request', options);
};

export const approveRequestUpdateDetailV4 = async (options: IApproveUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-vn/asker-approve-request', options);
};

export const getUpdateDetailExtraMoneyV4 = async (options: IParamsGetIncreaseDurationExtraMoney) => {
  return await fetchAPI('v5/update-task-vn/get-extra-money', options);
};

export const askerCreateRequestUpdateDetailV4 = async (options: IParamsAskerCreateRequestUpdateDetailV4) => {
  return await fetchAPI('v5/update-task-vn/asker-create-request', options);
};

export const sendCallChatMessage = async (options: IParamsSendCallChatMessage) => {
  return await fetchAPI('v5/api-asker-vn/send-call-chat-message', options);
};

export const remindTasker = async (params: IParamsRemindTasker) => {
  return await fetchAPI('v5/api-asker-vn/send-noti-to-remind-favourite-tasker', params);
};

export const rejectSuggestionNewDateOption = async (params: IParamsRejectSuggestionNewDateOption) => {
  return await fetchAPI('v5/update-task-vn/asker-reject-new-date-option', params);
};

export const acceptSuggestionNewDateOption = async (params: IParamsAcceptSuggestionNewDateOption) => {
  return await fetchAPI('v5/update-task-vn/asker-accept-new-date-option', params);
};

export const acceptTask = async (params: IParamsAcceptTask) => {
  return await fetchAPI('v5/accept-task-vn/asker-accept-new-date-duration', params);
};
