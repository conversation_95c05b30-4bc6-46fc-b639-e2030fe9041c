import { fetchAPI, getIsoCodeGlobal } from '@helper';

import {
  IApproveUpdateDetailRequest,
  IParamGetTaskerService,
  IParamsAskerCreateRequestUpdateDetailV4,
  IParamsGetIncreaseDurationExtraMoney,
  IRejectUpdateDetailRequest,
} from './type';

export const getConversationAPI = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-chat-list-chat-message-v2', options);
};

export const getChatHistoryAPI = async (options) => {
  return await fetchAPI('v5/api-service-my/get-chat-history-by-task', options);
};

export const translateMessage = async (options) => {
  return await fetchAPI('v5/api-asker-my/translate-message', options);
};

export const sendChatMessage = async (options) => {
  return await fetchAPI('v5/api-asker-my/send-chat-message-v2', options);
};

export const getListChat = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-list-chat-v2', options);
};

export const setIsReadChatMessage = async (options) => {
  return await fetchAPI('v5/api-asker-my/set-is-read-chat-message-v2', options);
};

export const getIncreaseDurationExtraMoney = async (options) => {
  return await fetchAPI('v5/update-task-my/get-increase-duration-extra-money', options);
};

export const rejectRequestIncreaseDuration = async (options) => {
  return await fetchAPI('v5/update-task-my/asker-reject-increase-duration-request', options);
};

export const approveRequestIncreaseDuration = async (options) => {
  return await fetchAPI('v5/update-task-my/asker-approve-increase-duration-request', options);
};

export const archiveConversation = async (options) => {
  return await fetchAPI('v5/api-asker-my/archive-chat-message', options);
};

export const removeConversation = async (options) => {
  return await fetchAPI('v5/api-asker-my/delete-chat-message', options);
};

export const getTaskerServices = async (params: IParamGetTaskerService) => {
  const options = {
    isoCode: getIsoCodeGlobal(),
    ...params,
  };
  return await fetchAPI('v5/api-asker-my/get-tasker-services', options);
};

export const getMoreMessage = async (options) => {
  return await fetchAPI('v5/api-asker-my/load-more-messages', options);
};

//TODO:check lại endpoint ID
export const approveUpdateDetailRequest = async (options: IApproveUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-my/asker-approve-update-detail-request', options);
};

//TODO:check lại endpoint ID
export const rejectUpdateDetailRequest = async (options: IRejectUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-my/asker-reject-update-detail-request', options);
};

//TODO:check lại endpoint ID
export const getUpdateDetailExtraMoney = async (options: IParamsGetIncreaseDurationExtraMoney) => {
  return await fetchAPI('v5/update-task-my/get-update-detail-extra-money', options);
};

export const rejectRequestUpdateDetailV4 = async (options: IRejectUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-my/asker-reject-request', options);
};

export const approveRequestUpdateDetailV4 = async (options: IApproveUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-my/asker-approve-request', options);
};

export const getUpdateDetailExtraMoneyV4 = async (options: IParamsGetIncreaseDurationExtraMoney) => {
  return await fetchAPI('v5/update-task-my/get-extra-money', options);
};

export const askerCreateRequestUpdateDetailV4 = async (options: IParamsAskerCreateRequestUpdateDetailV4) => {
  return await fetchAPI('v5/update-task-my/asker-create-request', options);
};
