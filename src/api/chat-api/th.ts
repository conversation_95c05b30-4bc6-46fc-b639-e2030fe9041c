import { fetchAP<PERSON>, getIsoCodeGlobal } from '@helper';

import {
  IApproveUpdateDetailRequest,
  IParamGetTaskerService,
  IParamsAcceptSuggestionNewDateOption,
  IParamsAcceptTask,
  IParamsAskerCreateRequestUpdateDetailV4,
  IParamsGetIncreaseDurationExtraMoney,
  IParamsRejectSuggestionNewDateOption,
  IParamsRemindTasker,
  IParamsSendCallChatMessage,
  IParamsSetDisabledActionChatMessageV2,
  IRejectUpdateDetailRequest,
} from './type';

export const getConversationAPI = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-chat-list-chat-message-v2', options);
};

export const getIncreaseDurationExtraMoney = async (options) => {
  return await fetchAPI('v5/update-task-th/get-increase-duration-extra-money', options);
};

export const rejectRequestIncreaseDuration = async (options) => {
  return await fetchAPI('v5/update-task-th/asker-reject-increase-duration-request', options);
};

export const approveRequestIncreaseDuration = async (options) => {
  return await fetchAPI('v5/update-task-th/asker-approve-increase-duration-request', options);
};

export const archiveConversation = async (options) => {
  return await fetchAPI('v5/api-asker-th/archive-chat-message', options);
};

export const removeConversation = async (options) => {
  return await fetchAPI('v5/api-asker-th/delete-chat-message', options);
};

export const getTaskerServices = async (params: IParamGetTaskerService) => {
  const options = {
    isoCode: getIsoCodeGlobal(),
    ...params,
  };
  return await fetchAPI('v5/api-asker-th/get-tasker-services', options);
};

export const getMoreMessage = async (options) => {
  return await fetchAPI('v5/api-asker-th/load-more-messages', options);
};

export const getChatHistoryAPI = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-chat-history-by-task', options);
};

export const translateMessage = async (options) => {
  return await fetchAPI('v5/api-asker-th/translate-message', options);
};

export const sendChatMessage = async (options) => {
  return await fetchAPI('v5/api-asker-th/send-chat-message-v2', options);
};

export const getListChat = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-list-chat-v2', options);
};

export const setIsReadChatMessage = async (options) => {
  return await fetchAPI('v5/api-asker-th/set-is-read-chat-message-v2', options);
};

//TODO:check lại endpoint TH
export const approveUpdateDetailRequest = async (options: IApproveUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-th/asker-approve-update-detail-request', options);
};

//TODO:check lại endpoint TH
export const rejectUpdateDetailRequest = async (options: IRejectUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-th/asker-reject-update-detail-request', options);
};

//TODO:check lại endpoint TH
export const getUpdateDetailExtraMoney = async (options: IParamsGetIncreaseDurationExtraMoney) => {
  return await fetchAPI('v5/update-task-th/get-update-detail-extra-money', options);
};

export const rejectRequestUpdateDetailV4 = async (options: IRejectUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-th/asker-reject-request', options);
};

export const approveRequestUpdateDetailV4 = async (options: IApproveUpdateDetailRequest) => {
  return await fetchAPI('v5/update-task-th/asker-approve-request', options);
};

export const getUpdateDetailExtraMoneyV4 = async (options: IParamsGetIncreaseDurationExtraMoney) => {
  return await fetchAPI('v5/update-task-th/get-extra-money', options);
};

export const askerCreateRequestUpdateDetailV4 = async (options: IParamsAskerCreateRequestUpdateDetailV4) => {
  return await fetchAPI('v5/update-task-th/asker-create-request', options);
};

export const sendCallChatMessage = async (options: IParamsSendCallChatMessage) => {
  return await fetchAPI('v5/api-asker-th/send-call-chat-message', options);
};

export const remindTasker = async (params: IParamsRemindTasker) => {
  return await fetchAPI('v5/api-asker-th/send-noti-to-remind-favourite-tasker', params);
};

export const rejectSuggestionNewDateOption = async (params: IParamsRejectSuggestionNewDateOption) => {
  return await fetchAPI('v5/update-task-th/asker-reject-new-date-option', params);
};

export const acceptSuggestionNewDateOption = async (params: IParamsAcceptSuggestionNewDateOption) => {
  return await fetchAPI('v5/update-task-th/asker-accept-new-date-option', params);
};
export const acceptTask = async (params: IParamsAcceptTask) => {
  return await fetchAPI('v5/accept-task-th/asker-accept-new-date-duration', params);
};
