import { UserType } from '@config';
import { IUser } from '@models/user/detail.model';
import { StatusMessageCall } from '@src/screens/voice-call-v2/types';

export type IParamGetListChat = {
  skip?: number;
  limit?: number;
  chatId?: string;
  taskId?: string;
  memberIds?: string[];
};

export type IParamGetTaskerService = {
  taskerId: IUser['_id'];
  appVersion?: string;
};

export type IRespondAPIGetTaskerServices = {
  serviceIds: string[];
};

export type IRejectRequestIncreaseDuration = {
  chatId: string;
  messageId: string;
};

export type IApproveRequestIncreaseDuration = {
  taskId: string;
  messageId: string;
};

export type IRejectUpdateDetailRequest = {
  chatId: string;
  messageId: string;
};

export type IApproveUpdateDetailRequest = {
  taskId: string;
  messageId: string;
};

export type IParamsGetIncreaseDurationExtraMoney = {
  taskId: string;
  messageId: string;
};

export type IParamsAskerCreateRequestUpdateDetailV4 = {
  taskId: string;
  userId: string;
  newDuration?: number;
  newDate?: string;
  action: string;
  taskerId: string;
};

export type IParamsSendCallChatMessage = {
  taskId?: string;
  chatId?: string;
  from?: UserType;
  status?: StatusMessageCall;
  duration?: number;
};

export type IParamsRemindTasker = {
  chatId?: string;
  taskerId?: string;
  taskId?: string;
};

export type IParamsRejectSuggestionNewDateOption = {
  chatId?: string;
  taskId?: string;
  messageId?: string;
};

export type IParamsSetDisabledActionChatMessageV2 = {
  chatId?: string;
  taskId?: string;
  key?: string;
};

export type IParamsAcceptSuggestionNewDateOption = {
  chatId?: string;
  taskId?: string;
  date?: string;
  taskerId?: string;
};

export type IParamsAcceptTask = {
  taskId?: string;
  taskerId?: string;
};
