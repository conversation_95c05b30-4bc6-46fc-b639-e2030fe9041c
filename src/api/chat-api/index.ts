import { ID, MY, TH, VN } from '@config';
import { getIsoCodeGlobal, IRespond } from '@helper';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import {
  IApproveRequestIncreaseDuration,
  IApproveUpdateDetailRequest,
  IParamGetListChat,
  IParamGetTaskerService,
  IParamsAcceptTask,
  IParamsAskerCreateRequestUpdateDetailV4,
  IParamsGetIncreaseDurationExtraMoney,
  IParamsRejectSuggestionNewDateOption,
  IParamsRemindTasker,
  IParamsSendCallChatMessage,
  IRejectRequestIncreaseDuration,
  IRejectUpdateDetailRequest,
  IRespondAPIGetTaskerServices,
} from './type';
import * as VN_API from './vn';

export const getConversationAPI = async (options: IParamGetListChat) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getConversationAPI,
    TH: TH_API.getConversationAPI,
    ID: ID_API.getConversationAPI,
    MY: MY_API.getConversationAPI,
  };
  return await combine[global.isoCode](params);
};

export const getChatHistoryAPI = async (options: object) => {
  const combine = {
    VN: VN_API.getChatHistoryAPI,
    TH: TH_API.getChatHistoryAPI,
    ID: ID_API.getChatHistoryAPI,
    MY: MY_API.getChatHistoryAPI,
  };
  return await combine[global.isoCode](options);
};

export const sendChatMessage = async (options: object) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.sendChatMessage,
    TH: TH_API.sendChatMessage,
    ID: ID_API.sendChatMessage,
    MY: MY_API.sendChatMessage,
  };
  return await combine[global.isoCode](params);
};

export const translateMessage = async (chatId: string, text: string, messageId: string | number, language: string) => {
  const options = {
    chatId: chatId,
    text: text,
    messageId: messageId,
    language: language,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.translateMessage,
    TH: TH_API.translateMessage,
    ID: ID_API.translateMessage,
    MY: MY_API.translateMessage,
  };
  return await combine[global.isoCode](options);
};

export const getListChat = async (params: any) => {
  const options = {
    isoCode: global.isoCode,
    ...params,
  };
  const combine = {
    VN: VN_API.getListChat,
    TH: TH_API.getListChat,
    ID: ID_API.getListChat,
    MY: MY_API.getListChat,
  };
  return await combine[global.isoCode](options);
};

export const setIsReadChatMessage = async (chatId: string) => {
  if (!chatId) return;

  const options = {
    chatId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.setIsReadChatMessage,
    TH: TH_API.setIsReadChatMessage,
    ID: ID_API.setIsReadChatMessage,
    MY: MY_API.setIsReadChatMessage,
  };
  return await combine[global.isoCode](options);
};

export const getIncreaseDurationExtraMoney = async ({
  taskId,
  messageId,
}: {
  taskId?: string;
  messageId?: string | number;
}) => {
  const options = {
    taskId,
    messageId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getIncreaseDurationExtraMoney,
    TH: TH_API.getIncreaseDurationExtraMoney,
    ID: ID_API.getIncreaseDurationExtraMoney,
    MY: MY_API.getIncreaseDurationExtraMoney,
  };
  return await combine[global.isoCode](options);
};

export const rejectRequestIncreaseDuration = async ({ chatId, messageId }: IRejectRequestIncreaseDuration) => {
  const options = {
    chatId,
    messageId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.rejectRequestIncreaseDuration,
    TH: TH_API.rejectRequestIncreaseDuration,
    ID: ID_API.rejectRequestIncreaseDuration,
    MY: MY_API.rejectRequestIncreaseDuration,
  };
  return await combine[global.isoCode](options);
};

export const approveRequestIncreaseDuration = async ({ taskId, messageId }: IApproveRequestIncreaseDuration) => {
  const options = {
    taskId,
    messageId,
  };
  const combine = {
    VN: VN_API.approveRequestIncreaseDuration,
    TH: TH_API.approveRequestIncreaseDuration,
    ID: ID_API.approveRequestIncreaseDuration,
    MY: MY_API.approveRequestIncreaseDuration,
  };
  return await combine[global.isoCode](options);
};

export const archiveConversation = async (chatId?: string) => {
  const options = {
    chatId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.archiveConversation,
    TH: TH_API.archiveConversation,
    ID: ID_API.archiveConversation,
    MY: MY_API.archiveConversation,
  };
  return await combine[global.isoCode](options);
};

export const removeConversation = async (chatId?: string) => {
  const options = {
    chatId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.removeConversation,
    TH: TH_API.removeConversation,
    ID: ID_API.removeConversation,
    MY: MY_API.removeConversation,
  };
  return await combine[global.isoCode](options);
};

export const getTaskerServices = (
  ...args: [IParamGetTaskerService]
): Promise<IRespond<IRespondAPIGetTaskerServices>> => {
  const apis = {
    [VN]: VN_API.getTaskerServices,
    [TH]: TH_API.getTaskerServices,
    [ID]: ID_API.getTaskerServices,
    [MY]: MY_API.getTaskerServices,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};

export const getMoreMessage = async (options) => {
  const params = {
    ...options,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getMoreMessage,
    TH: TH_API.getMoreMessage,
    ID: ID_API.getMoreMessage,
    MY: MY_API.getMoreMessage,
  };
  return await combine[global.isoCode](params);
};

export const approveRequestUpdateDetailV4 = async ({ taskId, messageId }: IApproveUpdateDetailRequest) => {
  const options = {
    taskId,
    messageId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.approveRequestUpdateDetailV4,
    TH: TH_API.approveRequestUpdateDetailV4,
    ID: ID_API.approveRequestUpdateDetailV4,
    MY: MY_API.approveRequestUpdateDetailV4,
  };
  return await combine[global.isoCode](options);
};

export const rejectRequestUpdateDetailV4 = async ({ chatId, messageId }: IRejectUpdateDetailRequest) => {
  const options = {
    chatId,
    messageId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.rejectRequestUpdateDetailV4,
    TH: TH_API.rejectRequestUpdateDetailV4,
    ID: ID_API.rejectRequestUpdateDetailV4,
    MY: MY_API.rejectRequestUpdateDetailV4,
  };
  return await combine[global.isoCode](options);
};

export const getUpdateDetailExtraMoneyV4 = async ({ taskId, messageId }: IParamsGetIncreaseDurationExtraMoney) => {
  const options = {
    taskId,
    messageId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getUpdateDetailExtraMoneyV4,
    TH: TH_API.getUpdateDetailExtraMoneyV4,
    ID: ID_API.getUpdateDetailExtraMoneyV4,
    MY: MY_API.getUpdateDetailExtraMoneyV4,
  };
  return await combine[global.isoCode](options);
};

export const askerCreateRequestUpdateDetailV4 = async (params: IParamsAskerCreateRequestUpdateDetailV4) => {
  const options = {
    isoCode: global.isoCode,
    ...params,
  };
  const combine = {
    VN: VN_API.askerCreateRequestUpdateDetailV4,
    TH: TH_API.askerCreateRequestUpdateDetailV4,
    ID: ID_API.askerCreateRequestUpdateDetailV4,
    MY: MY_API.askerCreateRequestUpdateDetailV4,
  };
  return await combine[global.isoCode](options);
};

export const sendCallChatMessage = async (params: IParamsSendCallChatMessage) => {
  const combine = {
    VN: VN_API.sendCallChatMessage,
    TH: TH_API.sendCallChatMessage,
    ID: ID_API.sendCallChatMessage,
  };
  return await combine[global.isoCode](params);
};

export const remindTasker = async (params: IParamsRemindTasker) => {
  const combine = {
    VN: VN_API.remindTasker,
    TH: TH_API.remindTasker,
    ID: ID_API.remindTasker,
  };
  return await combine[global.isoCode](params);
};

export const rejectSuggestionNewDateOption = async (params: IParamsRejectSuggestionNewDateOption) => {
  const combine = {
    VN: VN_API.rejectSuggestionNewDateOption,
    TH: TH_API.rejectSuggestionNewDateOption,
    ID: ID_API.rejectSuggestionNewDateOption,
  };
  return await combine[global.isoCode](params);
};

export const acceptSuggestionNewDateOption = async (params: IParamsAcceptSuggestionNewDateOption) => {
  const combine = {
    VN: VN_API.acceptSuggestionNewDateOption,
    TH: TH_API.acceptSuggestionNewDateOption,
    ID: ID_API.acceptSuggestionNewDateOption,
  };
  return await combine[global.isoCode](params);
};

export const acceptTask = async (params: IParamsAcceptTask) => {
  const combine = {
    VN: VN_API.acceptTask,
    TH: TH_API.acceptTask,
    ID: ID_API.acceptTask,
  };
  return await combine[global.isoCode](params);
};
