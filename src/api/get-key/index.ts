import { ISO_CODE } from '@config';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';
/**
 * @param fAccountId (required)
 */
export const getKeyConfigSetting = async (baseUrl: string, accessKey: string, isoCode?: ISO_CODE) => {
  const combine = {
    VN: async () => await VN_API.getKey(baseUrl, accessKey),
    TH: async () => await TH_API.getKey(baseUrl, accessKey),
    ID: async () => await ID_API.getKey(baseUrl, accessKey),
    MY: async () => await MY_API.getKey(baseUrl, accessKey),
  };

  return await combine[isoCode || global.isoCode]();
};

export type IAWS3 = {
  keyPrefix: string;
  bucket: string;
  region: string;
  secretKey: string;
  accessKey: string;
};

export type IADYEN = {
  key: string;
  libraryLocation: string;
};

export type IMIDTRANS = {
  key: string;
  libraryLocation: string;
};

export type I2C2P = {
  libraryLocation: string;
};

export type IVNPAY = {
  tmnCode: string;
};

export type ILOGIN_GOOGLE = {
  signInKeyIOS: string;
  signInKeyAndroid: string;
};

export type IKeyConfigSetting = {
  ACCESS_KEY: string;
  SECRET_KEY: string;
  ENCRYPT_KEY: string;
  AWS3: IAWS3 | null;
  ADYEN: IADYEN | null;
  API_MAINTAIN_URL: string;
  WEB_SOCKET_ENDPOINT: string;
  API_SERVER_URL: string;
  MIDTRANS: IMIDTRANS | null;
  CONFIG_2C2P: I2C2P | null;
  VNPAY: IVNPAY | null;
  LOGIN_GOOGLE: ILOGIN_GOOGLE | null;
};
