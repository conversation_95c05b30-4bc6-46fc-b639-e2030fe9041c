import { fetchAPI } from '@helper';

/**
 * @param rate Number
 * @param taskId String
 * @param review String
 * @param feedBack Array
 * @param isFavourite Booleans
 * @param tip Number
 */
export const customerRating = async (options) => {
  return await fetchAPI('v5/rating-vn/customer-rating', options);
};

/**
 * @param taskId String
 */
export const closeRatingTask = async (taskId) => {
  return await fetchAPI('v5/api-asker-vn/close-rating-task', { taskId: taskId });
};

export const updateRatingTask = async (options) => {
  return await fetchAPI('v5/rating-vn/update', options);
};

export const setReviewStore = async (options) => {
  return await fetchAPI('v5/api-asker-vn/set-review-store', options);
};
