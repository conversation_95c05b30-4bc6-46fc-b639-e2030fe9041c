import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

interface IParamReview {
  userId?: string;
  count?: number;
  isReviewStore?: boolean;
}

/**
 * @param rate Number
 * @param taskId String
 * @param review String
 * @param feedBack Array
 * @param isFavourite Booleans
 * @param tip Number
 */
export const customerRating = async (options) => {
  const combine = {
    VN: VN_API.customerRating,
    TH: TH_API.customerRating,
    ID: ID_API.customerRating,
    MY: MY_API.customerRating,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId String
 */
export const closeRatingTask = async (taskId) => {
  const combine = {
    VN: VN_API.closeRatingTask,
    TH: TH_API.closeRatingTask,
    ID: ID_API.closeRatingTask,
    MY: MY_API.closeRatingTask,
  };
  return await combine[global.isoCode](taskId);
};

export const updateRatingTask = async ({ taskId: taskId, rate: rate, review: review }) => {
  const options = {
    taskId: taskId,
    rate: rate,
  };
  if (review) {
    options.review = review;
  }
  const combine = {
    VN: VN_API.updateRatingTask,
    TH: TH_API.updateRatingTask,
    ID: ID_API.updateRatingTask,
    MY: MY_API.updateRatingTask,
  };
  return await combine[global.isoCode](options);
};

export const setReviewStore = async ({ count, isReviewStore }: IParamReview) => {
  const options: IParamReview = {};
  if (count) {
    options.count = count;
  }
  if (isReviewStore) {
    options.isReviewStore = isReviewStore;
  }
  const combine = {
    VN: VN_API.setReviewStore,
    TH: TH_API.setReviewStore,
    ID: ID_API.setReviewStore,
    MY: MY_API.setReviewStore,
  };
  return await combine[global.isoCode](options);
};
