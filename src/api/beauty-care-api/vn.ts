import { fetchAPI } from '@helper';

import { IParamGetBeautyAccessories, IParamGetBeautyQuestions } from './type';

export const getBeautyAccessoriesAPI = async (params: IParamGetBeautyAccessories) => {
  return await fetchAPI('v5/api-asker-vn/get-beauty-accessories', params);
};

export const getBeautyQuestionsAPI = async (params: IParamGetBeautyQuestions) => {
  return await fetchAPI('v5/api-asker-vn/get-beauty-questions', params);
};
