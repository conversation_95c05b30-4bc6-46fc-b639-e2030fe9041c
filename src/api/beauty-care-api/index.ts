import { ID, TH, VN } from '@config';
import { getIsoCodeGlobal, IRespond } from '@helper';

import * as ID_API from './id';
import * as TH_API from './th';
import { IParamGetBeautyAccessories, IParamGetBeautyQuestions } from './type';
import * as VN_API from './vn';

export const getBeautyAccessories = (...args: [IParamGetBeautyAccessories]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.getBeautyAccessoriesAPI,
    [TH]: TH_API.getBeautyAccessoriesAPI,
    [ID]: ID_API.getBeautyAccessoriesAPI,
  };
  return apis[getIsoCodeGlobal()](...args);
};

export const getBeautyQuestions = (...args: [IParamGetBeautyQuestions]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.getBeautyQuestionsAPI,
    [TH]: TH_API.getBeautyQuestionsAPI,
    [ID]: ID_API.getBeautyQuestionsAPI,
  };
  return apis[getIsoCodeGlobal()](...args);
};
