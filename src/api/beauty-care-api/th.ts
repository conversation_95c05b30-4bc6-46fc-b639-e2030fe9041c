import { fetchAPI } from '@helper';

import { IParamGetBeautyAccessories, IParamGetBeautyQuestions } from './type';

export const getBeautyAccessoriesAPI = async (params: IParamGetBeautyAccessories) => {
  return await fetchAPI('v5/api-asker-th/get-beauty-accessories', params);
};

export const getBeautyQuestionsAPI = async (params: IParamGetBeautyQuestions) => {
  return await fetchAPI('v5/api-asker-th/get-beauty-questions', params);
};
