import { fetchAPI } from '@helper';

import { IParamGetConfigSpecialPreBooking } from './type';

export const getSettingSystem = async (options) => {
  // Call api get setting for user not login
  if (!global.userId) {
    return await fetchAPI('v5/api-asker-indo/get-all-settings-without-login', options);
  }
  return await fetchAPI('v5/api-asker-indo/get-all-settings', options);
};

/**
 * @param userId
 * @param phoneNumber
 * @param name String
 * @param feedback String
 * @param services Array Object
 */
export const sendFeedback = async (options) => {
  return await fetchAPI('v5/api-asker-indo/create-feedback', options);
};

export const getNewVersion = async (options) => {
  return await fetchAPI('v5/api-asker-indo/check-compare-version-app', options);
};

export const getSupportCityAPI = async (options) => {
  return await fetchAPI('v5/api-asker-indo/get-support-city', options, 'post', false);
};

/**
 * @param userId string
 * @param isoCode string
 */
export const getReferralSetting = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  return await fetchAPI('v5/api-asker-indo/get-referral-setting', options, 'post');
};

/**
 * @param userId string
 */
export const getReferralFriend = async () => {
  return await fetchAPI('v5/api-asker-indo/get-referral-friends');
};

/**
 * @param appVersion string
 */
export const getConfigSpecialPreBookingAPI = async (options: IParamGetConfigSpecialPreBooking) => {
  return await fetchAPI('v5/api-asker-indo/get-event-config', options, 'post');
};
