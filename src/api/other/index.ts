import { Platform } from 'react-native';

import { API_RESULT_STATUS } from '@config';
import { getVersionAppName } from '@helper';
import { getRemoteConfigSetting } from '@helper/remoteConfigKeychain';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export const getSettingSystem = async (isoCode) => {
  const options = {
    appVersion: getVersionAppName(),
    isoCode: global.isoCode || isoCode,
  };
  const combine = {
    VN: VN_API.getSettingSystem,
    TH: TH_API.getSettingSystem,
    ID: ID_API.getSettingSystem,
    MY: MY_API.getSettingSystem,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param phoneNumber
 * @param name String
 * @param feedback String
 * @param services Array Object
 */
export const sendFeedback = async ({ phoneNumber, name, feedback = null, services }) => {
  const options = {
    name: name,
    phoneNumber: phoneNumber,
    services: services,
  };

  if (feedback) {
    options.feedback = feedback;
  }
  const combine = {
    VN: VN_API.sendFeedback,
    TH: TH_API.sendFeedback,
    ID: ID_API.sendFeedback,
    MY: MY_API.sendFeedback,
  };
  return await combine[global.isoCode](options);
};

export const getNewVersion = async () => {
  const options = {
    version: getVersionAppName(),
    platform: Platform.OS,
  };
  const combine = {
    VN: VN_API.getNewVersion,
    TH: TH_API.getNewVersion,
    ID: ID_API.getNewVersion,
    MY: MY_API.getNewVersion,
  };
  return await combine[global.isoCode](options);
};

export const getSupportCityAPI = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getSupportCityAPI,
    TH: TH_API.getSupportCityAPI,
    ID: ID_API.getSupportCityAPI,
    MY: MY_API.getSupportCityAPI,
  };
  return await combine[global.isoCode](options);
};

export const checkSystemMaintain = () =>
  new Promise(async (resolve, reject) => {
    const options = {
      isoCode: global.isoCode,
    };
    const { API_MAINTAIN_URL } = await getRemoteConfigSetting();

    if (!API_MAINTAIN_URL.startsWith('http://') && !API_MAINTAIN_URL.startsWith('https://')) {
      return reject({
        status: API_RESULT_STATUS.ERROR,
        isSuccess: false,
        error: {
          code: 'INVALID_URL',
          message: 'Invalid URL',
        },
      });
    }

    fetch(API_MAINTAIN_URL, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(options),
    })
      .then((response) => {
        return response.json();
      })
      .then((responseJson) => {
        if (responseJson) {
          resolve(responseJson);
        } else {
          reject({ error: responseJson });
        }
      })
      .catch((error) => {
        reject(error);
      });
  });

export const getReferralSetting = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getReferralSetting,
    TH: TH_API.getReferralSetting,
    ID: ID_API.getReferralSetting,
    MY: MY_API.getReferralSetting,
  };
  return await combine[global.isoCode](options);
};

export const getReferralFriend = async () => {
  const combine = {
    VN: VN_API.getReferralFriend,
    TH: TH_API.getReferralFriend,
    ID: ID_API.getReferralFriend,
    MY: MY_API.getReferralFriend,
  };
  return await combine[global.isoCode]();
};

/**
 * @description Get asker end year report
 *
 * @param isoCode
 * @param userId
 * **/
export const getConfigSpecialPreBookingAPI = async () => {
  const options = {
    appVersion: getVersionAppName(),
  };
  const combine = {
    VN: VN_API.getConfigSpecialPreBookingAPI,
    TH: TH_API.getConfigSpecialPreBookingAPI,
    ID: ID_API.getConfigSpecialPreBookingAPI,
  };
  return await combine[global.isoCode](options);
};
