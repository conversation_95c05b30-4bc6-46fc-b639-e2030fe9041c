import { fetchAPI } from '@helper';

export const getSettingSystem = async (options) => {
  // Call api get setting for user not login
  if (!global.userId) {
    return await fetchAPI('v5/api-asker-my/get-all-settings-without-login', options);
  }
  return await fetchAPI('v5/api-asker-my/get-all-settings', options);
};

/**
 * @param userId
 * @param phoneNumber
 * @param name String
 * @param feedback String
 * @param services Array Object
 */
export const sendFeedback = async (options) => {
  return await fetchAPI('v5/api-asker-my/create-feedback', options);
};

export const getNewVersion = async (options) => {
  return await fetchAPI('v5/api-asker-my/check-compare-version-app', options);
};

export const getSupportCityAPI = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-support-city', options, 'post', false);
};

/**
 * @param userId string
 * @param isoCode string
 */
export const getReferralSetting = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  return await fetchAPI('v5/api-asker-my/get-referral-setting', options, 'post');
};

/**
 * @param userId string
 */
export const getReferralFriend = async () => {
  return await fetchAPI('v5/api-asker-my/get-referral-friends');
};
