import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export const getOutstandingPaymentDebt = async () => {
  const combine = {
    VN: VN_API.getOutstandingPaymentDebt,
    TH: TH_API.getOutstandingPaymentDebt,
    ID: ID_API.getOutstandingPaymentDebt,
    MY: MY_API.getOutstandingPaymentDebt,
  };
  return await combine[global.isoCode]();
};

export const repayOutstanding = async (options) => {
  const combine = {
    VN: VN_API.repayOutstanding,
    TH: TH_API.repayOutstanding,
    ID: ID_API.repayOutstanding,
    MY: MY_API.repayOutstanding,
  };
  return await combine[global.isoCode](options);
};
