import { fetchAPI } from '@helper';

export const signUp = async (options) => {
  return await fetchAPI('v5/user-asker-vn/register', options);
};

export const resendActivationCode = async (options) => {
  return await fetchAPI('v5/user-asker-vn/resend-activation-code-v2', options);
};

/**
 * @param phone
 * @param countryCode
 * @param userId
 * @param language
 */
export const sendActivationCode = async (options) => {
  return await fetchAPI('v5/user-asker-vn/send-activation-code-v2', options);
};

export const validateActivationCode = async (options) => {
  return await fetchAPI('v5/user-asker-vn/validate-activation-code', options);
};

/**
 * "{
    ""userId"": ""xxx"",
    ""phone"": ""xxx"",
    ""countryCode"": ""+84"",
    ""isoCode"": ""VN"",
    .....
}"
 */
export const updateSocialInfoToUser = async (options) => {
  return await fetchAPI('v5/user-asker-vn/update-login-social-info', options);
};

export const setUserPassword = async (options) => {
  return await fetchAPI('v5/user-asker-vn/set-password', options, 'post', false);
};

export const getUser = async () => {
  return await fetchAPI('v5/api-asker-vn/get-user');
};

/**
 * @param taskerId
 */
export const getTaskerReviews = async (taskerId) => {
  return await fetchAPI('v5/api-asker-vn/get-tasker-reviews', { taskerId });
};

export const updateUserCountry = async () => {
  return await fetchAPI('v5/user-asker-vn/update-user-country');
};

/**
 * @param userId
 */
export const getFavoriteTasker = async () => {
  return await fetchAPI('v5/api-asker-vn/get-favorite-tasker-v2');
};

/**
 * @param userId String
 * @param taskerIds Array
 */
export const addFavoriteTasker = async (taskerIds) => {
  return await fetchAPI('v5/api-asker-vn/add-favorite-tasker', {
    taskerIds: taskerIds,
  });
};

/**
 * @param userId
 * @param taskerId
 */
export const removeFavoriteTasker = async (taskerId) => {
  return await fetchAPI('v5/api-asker-vn/remove-favorite-tasker', {
    taskerIds: [taskerId],
  });
};

/**
 * @param userId
 **/
export const getBlackListTasker = async () => {
  return await fetchAPI('v5/api-asker-vn/get-blacklist-taskers');
};

/**
 * @param userId
 **/
export const getSuggestBlackList = async () => {
  return await fetchAPI('v5/api-asker-vn/get-suggest-blacklist-taskers');
};

/**
 * @param userId
 * @param taskerId
 **/
export const removeBlackListTasker = async (taskerId) => {
  return await fetchAPI('v5/api-asker-vn/remove-tasker-from-blackList', {
    taskerIds: [taskerId],
  });
};

/**
 * @param userId
 * @param taskerIds
 **/
export const addBlackListTasker = async (taskerIds) => {
  return await fetchAPI('v5/api-asker-vn/add-blackList-tasker', {
    taskerIds: taskerIds,
  });
};

/**
 * @param userId
 * @param avatarUrl
 **/
export const updateAvatar = async (avatarUrl) => {
  return await fetchAPI('v5/api-asker-vn/update-user-avatar', {
    avatarUrl: avatarUrl,
  });
};

/**
 * @param userId
 **/
export const getNumberShared = async () => {
  return await fetchAPI('v5/api-asker-vn/get-number-shared');
};

/**
 * @param userId
 * @param name
 **/
export const updateUserInfo = async (params) => {
  return await fetchAPI('v5/api-asker-vn/update-user-info', params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const updateLocation = async (params) => {
  return await fetchAPI('v5/api-asker-vn/update-location-asker', params);
};

/**
 * @param userId
 * @param locationId
 **/
export const deleteLocation = async (params) => {
  return await fetchAPI('v5/api-asker-vn/delete-location-asker', params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocation = async (params) => {
  return await fetchAPI('v5/api-asker-vn/add-location-asker', {
    ...params,
    isoCode: global.isoCode,
  });
};

/**
 * @param userId
 * @param status {{String}} oneOf: ["UNVERIFIED","ACTIVE","LOCKED","INACTIVE","IN_PROBATION","DISABLED"]
 **/
export const updateUserStatus = async (options) => {
  return await fetchAPI('v5/api-asker-vn/update-user-status', options, 'post', false);
};

/**
 * @param userId
 * @param status {{String}} oneOf: ["UNVERIFIED","ACTIVE","LOCKED","INACTIVE","IN_PROBATION","DISABLED"]
 **/
export const checkUserForForgotPassWord = async (options) => {
  return await fetchAPI('v5/user-asker-vn/check-user-for-forgot-password', options);
};

/**
 * @param userId
 **/
export const sendVerifyEmail = async (params) => {
  return await fetchAPI('v5/user-asker-vn/send-verify-email', params);
};

/**
 * @param language
 **/
export const updateLanguage = async (language) => {
  const options = {
    language,
  };
  return await fetchAPI('v5/api-asker-vn/update-user-language', options);
};

/**
 * @param language
 **/
export const updateLastOnline = async () => {
  return await fetchAPI('v5/api-asker-vn/update-user-last-online-native');
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocationHospital = async (options) => {
  return await fetchAPI('v5/user-asker-vn/add-hospital-location-asker', options);
};

/**
 * @param userId
 **/
export const deleteAccount = async (options) => {
  return await fetchAPI('v5/user-asker-vn/delete', options);
};

/**
 * @param phone
 * @param countryCode
 * @param isoCode
 */
export const validatePhone = async (options) => {
  return await fetchAPI('v5/user-asker-vn/validate-phone', options);
};

/**
 * @param email
 * @param countryCode
 * @param isoCode
 */
export const validateEmail = async (options) => {
  return await fetchAPI('v5/user-asker-vn/validate-email', options);
};

/**
 * @param friendCode
 */
export const validateFriendCode = async (options) => {
  return await fetchAPI('v5/user-asker-vn/validate-friendCode', options);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocationHomeMoving = async (params) => {
  return await fetchAPI('v5/api-asker-vn/add-home-moving-location', {
    ...params,
    isoCode: global.isoCode,
  });
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const updateLocationHomeMoving = async (params) => {
  return await fetchAPI('v5/api-asker-vn/update-home-moving-location', {
    ...params,
    isoCode: global.isoCode,
  });
};

/**
 * @param userId
 * @param locationId
 **/
export const deleteLocationHomeMoving = async (params) => {
  return await fetchAPI('v5/api-asker-vn/delete-home-moving-location', params);
};

/**
 * @param userId
 **/
export const getListTaskerWorked = async () => {
  return await fetchAPI('v5/api-asker-vn/get-list-tasker-worked');
};

/**
 * @param userId
 * @param phone
 **/
export const updatePrivacyPolicy = async () => {
  return await fetchAPI('v5/user-asker-vn/update-privacy-policy');
};
