import { getPhoneNumber, getVersionAppName } from '@helper';
import { SHA256 } from '@helper/sha256';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export const signUp = async (options) => {
  const combine = {
    VN: VN_API.signUp,
    TH: TH_API.signUp,
    ID: ID_API.signUp,
    MY: MY_API.signUp,
  };
  return await combine[global.isoCode](options);
};

export const resendActivationCode = async (phone, countryCode, supplier) => {
  const options = {
    phone: getPhoneNumber(phone, countryCode),
    countryCode: countryCode,
    appVersion: getVersionAppName(),
  };
  if (supplier) {
    options.supplier = supplier;
  }
  const combine = {
    VN: VN_API.resendActivationCode,
    TH: TH_API.resendActivationCode,
    ID: ID_API.resendActivationCode,
    MY: MY_API.resendActivationCode,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param phone
 * @param countryCode
 * @param userId
 * @param language
 */
export const sendActivationCode = async (phone, countryCode, userId, language = 'vi') => {
  const options = {
    phone: getPhoneNumber(phone, countryCode),
    countryCode: countryCode,
    userId,
    language,
    appVersion: getVersionAppName(),
  };
  const combine = {
    VN: VN_API.sendActivationCode,
    TH: TH_API.sendActivationCode,
    ID: ID_API.sendActivationCode,
    MY: MY_API.sendActivationCode,
  };
  return await combine[global.isoCode](options);
};

export const validateActivationCode = async (phone, countryCode, code) => {
  const options = {
    phone: getPhoneNumber(phone, countryCode),
    countryCode,
    code,
    appVersion: getVersionAppName(),
  };
  const combine = {
    VN: VN_API.validateActivationCode,
    TH: TH_API.validateActivationCode,
    ID: ID_API.validateActivationCode,
    MY: MY_API.validateActivationCode,
  };
  return await combine[global.isoCode](options);
};

/**
 * "{
    ""userId"": ""xxx"",
    ""phone"": ""xxx"",
    ""countryCode"": ""+84"",
    ""isoCode"": ""VN"",
    .....
}"
 */
export const updateSocialInfoToUser = async (options) => {
  const combine = {
    VN: VN_API.updateSocialInfoToUser,
    TH: TH_API.updateSocialInfoToUser,
    ID: ID_API.updateSocialInfoToUser,
    MY: MY_API.updateSocialInfoToUser,
  };
  return await combine[global.isoCode](options);
};

export const setUserPassword = async ({ userId, password }) => {
  const options = {
    userId: userId,
    password: SHA256(password),
  };
  const combine = {
    VN: VN_API.setUserPassword,
    TH: TH_API.setUserPassword,
    ID: ID_API.setUserPassword,
    MY: MY_API.setUserPassword,
  };
  return await combine[global.isoCode](options);
};

export const getUser = async () => {
  const combine = {
    VN: VN_API.getUser,
    TH: TH_API.getUser,
    ID: ID_API.getUser,
    MY: MY_API.getUser,
  };
  return await combine[global.isoCode]();
};

/**
 * @param taskerId
 */
export const getTaskerReviews = async (taskerId) => {
  const combine = {
    VN: VN_API.getTaskerReviews,
    TH: TH_API.getTaskerReviews,
    ID: ID_API.getTaskerReviews,
    MY: MY_API.getTaskerReviews,
  };
  return await combine[global.isoCode](taskerId);
};

/**
 * @deprecated đổi quốc gia
 * @param isoCode
 */
export const updateUserCountry = async (isoCode) => {
  const combine = {
    VN: VN_API.updateUserCountry,
    TH: TH_API.updateUserCountry,
    ID: ID_API.updateUserCountry,
    MY: MY_API.updateUserCountry,
  };
  return await combine[isoCode]();
};

/**
 * @param userId
 */
export const getFavoriteTasker = async () => {
  const combine = {
    VN: VN_API.getFavoriteTasker,
    TH: TH_API.getFavoriteTasker,
    ID: ID_API.getFavoriteTasker,
    MY: MY_API.getFavoriteTasker,
  };
  return await combine[global.isoCode]();
};

/**
 * @param userId String
 * @param taskerIds Array
 */
export const addFavoriteTasker = async (taskerIds) => {
  const combine = {
    VN: VN_API.addFavoriteTasker,
    TH: TH_API.addFavoriteTasker,
    ID: ID_API.addFavoriteTasker,
    MY: MY_API.addFavoriteTasker,
  };
  return await combine[global.isoCode](taskerIds);
};

/**
 * @param userId
 * @param taskerId
 */
export const removeFavoriteTasker = async (taskerId) => {
  const combine = {
    VN: VN_API.removeFavoriteTasker,
    TH: TH_API.removeFavoriteTasker,
    ID: ID_API.removeFavoriteTasker,
    MY: MY_API.removeFavoriteTasker,
  };
  return await combine[global.isoCode](taskerId);
};

/**
 * @param userId
 **/
export const getBlackListTasker = async () => {
  const combine = {
    VN: VN_API.getBlackListTasker,
    TH: TH_API.getBlackListTasker,
    ID: ID_API.getBlackListTasker,
    MY: MY_API.getBlackListTasker,
  };
  return await combine[global.isoCode]();
};

/**
 * @param userId
 **/
export const getSuggestBlackList = async () => {
  const combine = {
    VN: VN_API.getSuggestBlackList,
    TH: TH_API.getSuggestBlackList,
    ID: ID_API.getSuggestBlackList,
    MY: MY_API.getSuggestBlackList,
  };
  return await combine[global.isoCode]();
};

/**
 * @param userId
 * @param taskerId
 **/
export const removeBlackListTasker = async (taskerId) => {
  const combine = {
    VN: VN_API.removeBlackListTasker,
    TH: TH_API.removeBlackListTasker,
    ID: ID_API.removeBlackListTasker,
    MY: MY_API.removeBlackListTasker,
  };
  return await combine[global.isoCode](taskerId);
};

/**
 * @param userId
 * @param taskerIds
 **/
export const addBlackListTasker = async (taskerIds) => {
  const combine = {
    VN: VN_API.addBlackListTasker,
    TH: TH_API.addBlackListTasker,
    ID: ID_API.addBlackListTasker,
    MY: MY_API.addBlackListTasker,
  };
  return await combine[global.isoCode](taskerIds);
};

/**
 * @param userId
 * @param avatarUrl
 **/
export const updateAvatar = async (avatarUrl) => {
  const combine = {
    VN: VN_API.updateAvatar,
    TH: TH_API.updateAvatar,
    ID: ID_API.updateAvatar,
    MY: MY_API.updateAvatar,
  };
  return await combine[global.isoCode](avatarUrl);
};

/**
 * @param userId
 **/
export const getNumberShared = async () => {
  const combine = {
    VN: VN_API.getNumberShared,
    TH: TH_API.getNumberShared,
    ID: ID_API.getNumberShared,
    MY: MY_API.getNumberShared,
  };
  return await combine[global.isoCode]();
};

/**
 * @param userId
 * @param name
 **/
export const updateUserInfo = async (params) => {
  const combine = {
    VN: VN_API.updateUserInfo,
    TH: TH_API.updateUserInfo,
    ID: ID_API.updateUserInfo,
    MY: MY_API.updateUserInfo,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const updateLocation = async (params) => {
  const combine = {
    VN: VN_API.updateLocation,
    TH: TH_API.updateLocation,
    ID: ID_API.updateLocation,
    MY: MY_API.updateLocation,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param userId
 * @param locationId
 **/
export const deleteLocation = async (params) => {
  const combine = {
    VN: VN_API.deleteLocation,
    TH: TH_API.deleteLocation,
    ID: ID_API.deleteLocation,
    MY: MY_API.deleteLocation,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocation = async (params) => {
  const combine = {
    VN: VN_API.addLocation,
    TH: TH_API.addLocation,
    ID: ID_API.addLocation,
    MY: MY_API.addLocation,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param userId
 * @param status {{String}} oneOf: ["UNVERIFIED","ACTIVE","LOCKED","INACTIVE","IN_PROBATION","DISABLED"]
 **/
export const updateUserStatus = async (userId, status) => {
  const options = {
    userId,
    status,
    type: 'ASKER',
  };
  const combine = {
    VN: VN_API.updateUserStatus,
    TH: TH_API.updateUserStatus,
    ID: ID_API.updateUserStatus,
    MY: MY_API.updateUserStatus,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param status {{String}} oneOf: ["UNVERIFIED","ACTIVE","LOCKED","INACTIVE","IN_PROBATION","DISABLED"]
 **/
export const checkUserForForgotPassWord = async (phone, countryCode) => {
  const options = {
    phone: getPhoneNumber(phone, countryCode),
    countryCode,
    type: 'ASKER',
  };
  const combine = {
    VN: VN_API.checkUserForForgotPassWord,
    TH: TH_API.checkUserForForgotPassWord,
    ID: ID_API.checkUserForForgotPassWord,
    MY: MY_API.checkUserForForgotPassWord,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 **/
export const sendVerifyEmail = async (params) => {
  const combine = {
    VN: VN_API.sendVerifyEmail,
    TH: TH_API.sendVerifyEmail,
    ID: ID_API.sendVerifyEmail,
    MY: MY_API.sendVerifyEmail,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param language
 **/
export const updateLanguage = async (language) => {
  const combine = {
    VN: VN_API.updateLanguage,
    TH: TH_API.updateLanguage,
    ID: ID_API.updateLanguage,
    MY: MY_API.updateLanguage,
  };
  return await combine[global.isoCode](language);
};

/**
 * @param language
 **/
export const updateLastOnline = async () => {
  if (!global.userId) {
    return;
  }
  const combine = {
    VN: VN_API.updateLastOnline,
    TH: TH_API.updateLastOnline,
    ID: ID_API.updateLastOnline,
    MY: MY_API.updateLastOnline,
  };
  return await combine[global.isoCode]();
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocationHospital = async ({
  lat,
  lng,
  country,
  city,
  district,
  address,
  shortAddress,
  description,
  phoneNumber,
  countryCode,
}) => {
  const options = {
    lat: lat,
    lng: lng,
    city: city,
    address: address,
    country: country,
    district: district,
    isoCode: global.isoCode,
    phoneNumber: phoneNumber,
    description: description,
    shortAddress: shortAddress,
    countryCode: countryCode,
  };
  const combine = {
    VN: VN_API.addLocationHospital,
    TH: TH_API.addLocationHospital,
    ID: ID_API.addLocationHospital,
    MY: MY_API.addLocationHospital,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 **/
export const deleteAccount = async (reason) => {
  if (!global.userId) {
    return;
  }
  const options = { reason };

  const combine = {
    VN: VN_API.deleteAccount,
    TH: TH_API.deleteAccount,
    ID: ID_API.deleteAccount,
    MY: MY_API.deleteAccount,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param phone
 * @param countryCode
 * @param isoCode
 */
export const validatePhone = async (phone, countryCode) => {
  const options = {
    phone: getPhoneNumber(phone, countryCode),
    countryCode,
    isoCode: global.isoCode,
  };

  const combine = {
    VN: VN_API.validatePhone,
    TH: TH_API.validatePhone,
    ID: ID_API.validatePhone,
    MY: MY_API.validatePhone,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param email
 * @param countryCode
 * @param isoCode
 */
export const validateEmail = async (email, countryCode) => {
  const options = {
    email: email,
    countryCode,
    isoCode: global.isoCode,
  };

  const combine = {
    VN: VN_API.validateEmail,
    TH: TH_API.validateEmail,
    ID: ID_API.validateEmail,
    MY: MY_API.validateEmail,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param friendCode
 */
export const validateFriendCode = async (friendCode) => {
  const options = {
    friendCode,
  };

  const combine = {
    VN: VN_API.validateFriendCode,
    TH: TH_API.validateFriendCode,
    ID: ID_API.validateFriendCode,
    MY: MY_API.validateFriendCode,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocationHomeMoving = async (params) => {
  const combine = {
    VN: VN_API.addLocationHomeMoving,
    TH: TH_API.addLocationHomeMoving,
    ID: ID_API.addLocationHomeMoving,
    MY: MY_API.addLocationHomeMoving,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const updateLocationHomeMoving = async (params) => {
  const combine = {
    VN: VN_API.updateLocationHomeMoving,
    TH: TH_API.updateLocationHomeMoving,
    ID: ID_API.updateLocationHomeMoving,
    MY: MY_API.updateLocationHomeMoving,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param userId
 * @param locationId
 **/
export const deleteLocationHomeMoving = async (params) => {
  const combine = {
    VN: VN_API.deleteLocationHomeMoving,
    TH: TH_API.deleteLocationHomeMoving,
    ID: ID_API.deleteLocationHomeMoving,
    MY: MY_API.deleteLocationHomeMoving,
  };
  return await combine[global.isoCode](params);
};

/**
 * @param userId
 * @param locationId
 **/
export const getListTaskerWorked = async () => {
  const combine = {
    VN: VN_API.getListTaskerWorked,
    TH: TH_API.getListTaskerWorked,
    ID: ID_API.getListTaskerWorked,
    MY: MY_API.getListTaskerWorked,
  };
  return await combine[global.isoCode]();
};

/**
 * @param userId
 * @param phone
 **/
export const updatePrivacyPolicy = async () => {
  const combine = {
    VN: VN_API.updatePrivacyPolicy,
    TH: TH_API.updatePrivacyPolicy,
    ID: ID_API.updatePrivacyPolicy,
    MY: MY_API.updatePrivacyPolicy,
  };
  return await combine[global.isoCode]();
};
