import { fetchAPI } from '@helper';

export const signUp = async (options) => {
  return await fetchAPI('v5/user-asker-my/register', options);
};

export const resendActivationCode = async (options) => {
  return await fetchAPI('v5/user-asker-my/resend-activation-code', options);
};

/**
 * @param phone
 * @param countryCode
 * @param userId
 * @param language
 */
export const sendActivationCode = async (options) => {
  return await fetchAPI('v5/user-asker-my/send-activation-code', options);
};

export const validateActivationCode = async (options) => {
  return await fetchAPI('v5/user-asker-my/validate-activation-code', options);
};

/**
 * "{
    ""userId"": ""xxx"",
    ""phone"": ""xxx"",
    ""countryCode"": ""+84"",
    ""isoCode"": ""VN"",
    .....
}"
 */
export const updateSocialInfoToUser = async (options) => {
  return await fetchAPI('v5/user-asker-my/update-login-social-info', options);
};

export const setUserPassword = async (options) => {
  return await fetchAPI('v5/user-asker-my/set-password', options, 'post', false);
};

export const getUser = async () => {
  return await fetchAPI('v5/api-asker-my/get-user');
};

/**
 * @param taskerId
 */
export const getTaskerReviews = async (taskerId) => {
  const options = {
    userId: taskerId,
    askerId: global.userId,
  };
  return await fetchAPI('v5/api-asker-my/get-tasker-reviews', options, 'post', false);
};

export const updateUserCountry = async (isoCode) => {
  const options = {
    isoCode,
  };
  return await fetchAPI('v5/user-asker-my/update-user-country', options);
};

/**
 * @param userId
 */
export const getFavoriteTasker = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-favorite-tasker-v2', options);
};

/**
 * @param userId String
 * @param taskerIds Array
 */
export const addFavoriteTasker = async (taskerIds) => {
  return await fetchAPI('v5/api-asker-my/add-favorite-tasker', {
    taskerIds: taskerIds,
  });
};

/**
 * @param userId
 * @param taskerId
 */
export const removeFavoriteTasker = async (taskerId) => {
  return await fetchAPI('v5/api-asker-my/remove-favorite-tasker', {
    taskerIds: [taskerId],
  });
};

/**
 * @param userId
 **/
export const getBlackListTasker = async () => {
  return await fetchAPI('v5/api-asker-my/get-blacklist-taskers');
};

/**
 * @param userId
 **/
export const getSuggestBlackList = async () => {
  return await fetchAPI('v5/api-asker-my/get-suggest-blacklist-taskers');
};

/**
 * @param userId
 * @param taskerId
 **/
export const removeBlackListTasker = async (taskerId) => {
  return await fetchAPI('v5/api-asker-my/remove-tasker-from-blackList', {
    taskerIds: [taskerId],
  });
};

/**
 * @param userId
 * @param taskerIds
 **/
export const addBlackListTasker = async (taskerIds) => {
  return await fetchAPI('v5/api-asker-my/add-blackList-tasker', {
    taskerIds: taskerIds,
  });
};

/**
 * @param userId
 * @param avatarUrl
 **/
export const updateAvatar = async (avatarUrl) => {
  return await fetchAPI('v5/api-asker-my/update-user-avatar', {
    avatarUrl: avatarUrl,
  });
};

/**
 * @param userId
 **/
export const getNumberShared = async () => {
  return await fetchAPI('v5/api-asker-my/get-number-shared');
};

/**
 * @param userId
 * @param name
 **/
export const updateUserInfo = async (params) => {
  return await fetchAPI('v5/api-asker-my/update-user-info', params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const updateLocation = async (params) => {
  return await fetchAPI('v5/api-asker-my/update-location-asker', params);
};

/**
 * @param userId
 * @param locationId
 **/
export const deleteLocation = async (params) => {
  return await fetchAPI('v5/api-asker-my/delete-location-asker', params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocation = async (params) => {
  return await fetchAPI('v5/api-asker-my/add-location-asker', {
    ...params,
    isoCode: global.isoCode,
  });
};

/**
 * @param userId
 * @param status {{String}} oneOf: ["UNVERIFIED","ACTIVE","LOCKED","INACTIVE","IN_PROBATION","DISABLED"]
 **/
export const updateUserStatus = async (options) => {
  return await fetchAPI('v5/api-asker-my/update-user-status', options, 'post', false);
};

/**
 * @param userId
 * @param status {{String}} oneOf: ["UNVERIFIED","ACTIVE","LOCKED","INACTIVE","IN_PROBATION","DISABLED"]
 **/
export const checkUserForForgotPassWord = async (options) => {
  return await fetchAPI('v5/user-asker-my/check-user-for-forgot-password', options);
};

/**
 * @param userId
 **/
export const sendVerifyEmail = async (params) => {
  return await fetchAPI('v5/user-asker-my/send-verify-email', params);
};

/**
 * @param language
 **/
export const updateLanguage = async (language) => {
  const options = {
    language,
  };
  return await fetchAPI('v5/api-asker-my/update-user-language', options);
};

/**
 * @param language
 **/
export const updateLastOnline = async () => {
  return await fetchAPI('v5/api-asker-my/update-user-last-online-native');
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocationHospital = async (options) => {
  return await fetchAPI('v5/user-asker-my/add-hospital-location-asker', options);
};

/**
 * @param userId
 **/
export const deleteAccount = async (options) => {
  return await fetchAPI('v5/user-asker-my/delete', options);
};

/**
 * @param phone
 * @param countryCode
 * @param isoCode
 */
export const validatePhone = async (options) => {
  return await fetchAPI('v5/user-asker-my/validate-phone', options);
};

/**
 * @param email
 * @param countryCode
 * @param isoCode
 */
export const validateEmail = async (options) => {
  return await fetchAPI('v5/user-asker-my/validate-email', options);
};

/**
 * @param friendCode
 */
export const validateFriendCode = async (options) => {
  return await fetchAPI('v5/user-asker-my/validate-friendCode', options);
};

/**
 * @param userId
 **/
export const getListTaskerWorked = async (params) => {
  return await fetchAPI('v5/api-asker-my/get-list-tasker-worked', params);
};

/**
 * @param userId
 * @param phone
 **/
export const updatePrivacyPolicy = async (params) => {
  return await fetchAPI('v5/user-asker-my/update-privacy-policy', params);
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const addLocationHomeMoving = async (params) => {
  return await fetchAPI('v5/api-asker-my/add-home-moving-location', {
    ...params,
    isoCode: global.isoCode,
  });
};

/**
 * @param userId
 * @param address
 * @param city
 * @param countryCode
 * @param description
 * @param district
 * @param lat
 * @param lng
 * @param phoneNumber
 * @param shortAddress
 **/
export const updateLocationHomeMoving = async (params) => {
  return await fetchAPI('v5/api-asker-my/update-home-moving-location', {
    ...params,
    isoCode: global.isoCode,
  });
};

/**
 * @param userId
 * @param locationId
 **/
export const deleteLocationHomeMoving = async (params) => {
  return await fetchAPI('v5/api-asker-my/delete-home-moving-location', params);
};
