import { fetchAPI } from '@helper';

import { IDailyRollCallAPI, IGetHistoryTransaction } from './type';

export const dailyRollCall = async (params: IDailyRollCallAPI) => {
  return await fetchAPI('v5/api-asker-vn/daily-roll-call', params);
};

export const getWeeklyRollCall = async () => {
  return await fetchAPI('v5/api-asker-vn/get-weekly-roll-call');
};

export const redeemGift = async (gameCampaignId?: string) => {
  return await fetchAPI('v5/event-vn/use-present-unboxing', { gameCampaignId });
};

export const getHistoryReceiveSpin = async (params: IGetHistoryTransaction) => {
  return await fetchAPI('v5/api-asker-vn/get-history-receive-spin', params);
};

export const getHistoryReceiveReward = async (params: IGetHistoryTransaction) => {
  return await fetchAPI('v5/api-asker-vn/get-history-receive-reward', params);
};
