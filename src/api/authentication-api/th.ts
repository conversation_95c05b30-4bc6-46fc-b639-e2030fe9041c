import { fetchAPI } from '@helper';
import { SHA256 } from '@helper/sha256';

/**
 * @param username
 * @param password
 */
export const loginWithPassword = async (username: string, password: string) => {
  return await fetchAPI('v5/user-asker-th/login', {
    username,
    password: SHA256(password),
    type: 'ASKER',
  });
};

export const loginWithFacebook = async (data: Object) => {
  return await fetchAPI('v5/user-asker-th/login-facebook', data);
};

export const loginWithGoogle = async (data: Object) => {
  return await fetchAPI('v5/user-asker-th/login-google', data);
};

export const loginWithApple = async (data: Object) => {
  return await fetchAPI('v5/user-asker-th/login-apple', data);
};

/**
 * @param token
 */
export const logOut = async (data: Object) => {
  return await fetchAPI('v5/user-asker-th/logout', data);
};
