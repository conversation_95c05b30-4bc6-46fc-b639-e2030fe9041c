import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';
/**
 * @param username
 * @param password
 */
export const loginWithPassword = async (username: string, password: string) => {
  const combine = {
    VN: VN_API.loginWithPassword,
    TH: TH_API.loginWithPassword,
    ID: ID_API.loginWithPassword,
    MY: MY_API.loginWithPassword,
  };
  return await combine[global.isoCode](username, password);
};

export const loginWithFacebook = async (data: Object) => {
  const combine = {
    VN: VN_API.loginWithFacebook,
    TH: TH_API.loginWithFacebook,
    ID: ID_API.loginWithFacebook,
    MY: MY_API.loginWithFacebook,
  };
  return await combine[global.isoCode](data);
};

export const loginWithGoogle = async (data: Object) => {
  const combine = {
    VN: VN_API.loginWithGoogle,
    TH: TH_API.loginWithGoogle,
    ID: ID_API.loginWithGoogle,
    MY: MY_API.loginWithGoogle,
  };
  return await combine[global.isoCode](data);
};

export const loginWithApple = async (data: Object) => {
  const combine = {
    VN: VN_API.loginWithApple,
    TH: TH_API.loginWithApple,
    ID: ID_API.loginWithApple,
    MY: MY_API.loginWithApple,
  };
  return await combine[global.isoCode](data);
};

/**
 * @param token
 */
export const logOut = async (data: Object) => {
  const combine = {
    VN: VN_API.logOut,
    TH: TH_API.logOut,
    ID: ID_API.logOut,
    MY: MY_API.logOut,
  };
  return await combine[global.isoCode](data);
};
