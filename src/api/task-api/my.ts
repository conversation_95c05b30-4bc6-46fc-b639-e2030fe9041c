import { fetchAPI, getIsoCodeGlobal } from '@helper';

import {
  IParamCheckTaskerConflictTime,
  IParamChooseDateOption,
  IParamGetTaskCheckList,
  IParamRemoveForceTasker,
  IParamUpdateTask,
} from './type';

/**
 * @param serviceId
 */
export const getLastPostedTask = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-last-post-task', options);
};

export const getLastPostedTaskCleaning = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-last-post-task-cleaning', options);
};

export const getTaskWithTaskId = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-task-detail', options);
};

export const getTaskUpComming = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-up-coming-tasks', options);
};

/**
 * @param userId
 * @param taskId
 * @param reason
 * @param changeTaskToPosted
 */
export const cancelTask = async (options) => {
  return await fetchAPI('v5/cancel-task-my/asker-cancel', options);
};

/**
 * @param taskId
 * @param taskerId
 */
export const chooseTasker = async (options) => {
  return await fetchAPI('v5/accept-task-my/choose-tasker', options);
};

/**
 * @param taskId
 * @param reason
 * @param language
 */
export const checkCancelFee = async (options) => {
  return await fetchAPI('v5/cancel-task-my/get-asker-cancel-fee', options);
};

export const checkNumberOfCancel = async (options) => {
  return await fetchAPI('v5/cancel-task-my/check-number-of-cancel', options);
};

/**
 * @param userId
 */
export const getTaskSubscription = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-subscription-by-userId', options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getHistoryTask = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-list-history-tasks', options);
};
//
// {
//     "userId": "xxx",
//     "taskId": "xxx",
//     "newDate": "2020-11-02 12:06:07.229Z",
//     "newDuration": 4
// }

// api update chuen sang v2 het
/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Number
 */
export const updateTaskHomeCleaning = async (options) => {
  return await fetchAPI('v5/update-task-my/home-cleaning', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskAirConditioner = async (options) => {
  return await fetchAPI('v5/update-task-my/air-conditioner', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskDeepCleaning = async (options) => {
  return await fetchAPI('v5/update-task-my/deep-cleaning', options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getTaskDone = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-tasks-done', options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getTaskConfirmedToDone = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-tasks-confirmed-asker', options);
};

/**
 * @param userId
 * @param taskId
 */
export const doneTask = async (options) => {
  // TODO V3
  return await fetchAPI('v5/done-booking/done', options);
};

/**
 * @param taskId
 * @param reportData {reason: {vi,en,vi,th}}
 */
export const updateTaskReport = async (options) => {
  return await fetchAPI('v5/api-asker-my/update-task-report', options);
};

/**
 * @param taskId String
 * @param reason String
 * @param reportData {String}
 */
export const createUserComplaint = async (options) => {
  return await fetchAPI('v5/api-asker-my/create-user-complaint', options);
};

/**
 * @param taskId
 */
export const updateBringToToolsAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/add-cleaning-tool', options);
};

/**
 * @param taskId String
 * @param newTaskNote String
 */
export const updateTaskNote = async (options) => {
  return await fetchAPI('v5/update-task-my/update-task-note', options);
};

/**
 * @param options = {
      "taskId": "x62a73b18ef9e21f89ea73b63",
      "userId": "0834567890",
      "isPremium": false
    }
 */
export const changeToPremiumService = async (options) => {
  return await fetchAPI('v5/update-task-my/update-task-premium', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskOfficeCleaning = async (options) => {
  return await fetchAPI('v5/update-task-my/office-cleaning', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskWashingMachine = async (options) => {
  return await fetchAPI('v5/update-task-my/washing-machine', options);
};

/**
 * @param options =  {
    "taskId": "xxx"
    "newDate": "11/11/1111",
    "newCollectionDate": "11/11/1111",
    "detailHostel": {
        "rooms": [],
        ...
    },
    "newDuration": 2,
    "userId": "0834567890"
  }
 */
export const getExtraMoneyUpdateDateTimeAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/get-extra-money-update-date-time', options);
};

/**
     * @param options =  {
        "taskId": "xxx"
        "newDate": "11/11/1111",
        "newCollectionDate": "11/11/1111",
        "detailHostel": {
            "rooms": [],
            ...
        },
        "newDuration": 2,
        "userId": "0834567890"
      }
     */
export const checkTaskerConflictTimeAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/check-tasker-conflict-time', options);
};

/**
     * @param options =  {
        "taskId": "xxx"
        "newDate": "11/11/1111",
        "newCollectionDate": "11/11/1111",
        "detailHostel": {
            "rooms": [],
            ...
        },
        "newDuration": 2,
        "userId": "0834567890"
      }
     */
export const createUpdateDateTimeRequestAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/asker-create-update-date-time-request', options);
};

/**
     * @param options = {
        "chatId":    "xxxx"
        "messageId": "xxxx"
      }
     */
export const setDisableActionChatMessageAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/set-disable-action-chat-message', options);
};

export const updateTaskWaterHeater = async (options) => {
  return await fetchAPI('v5/update-task-my/water-heater', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskSofaAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/sofa', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskDisinfectionAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/disinfection', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskMassageAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-my/massage', options);
};

export const updateDishedHomeCooking = async (options) => {
  return await fetchAPI('v5/update-task-my/update-dish-detail', options);
};

export const checkTaskerConflictTime = async (params: IParamCheckTaskerConflictTime) => {
  const options = {
    isoCode: getIsoCodeGlobal(),
    ...params,
  };
  return await fetchAPI('v5/api-asker-my/check-tasker-conflict-time', options);
};

export const getTaskerFreeTime = async (params: IParamCheckTaskerConflictTime) => {
  const options = {
    isoCode: getIsoCodeGlobal(),
    ...params,
  };
  return await fetchAPI('v5/api-asker-my/get-tasker-free-time', options);
};

export const removeForceTasker = async (params: IParamRemoveForceTasker) => {
  return await fetchAPI('v5/update-task-my/asker-remove-force-tasker', params);
};

export const chooseDateOption = async (params: IParamChooseDateOption) => {
  return await fetchAPI('v5/update-task-my/asker-choose-date-option', params);
};

export const updateHouseKeepingAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-my/housekeeping', options);
};

export const updateTaskElderlyCareAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/elderly-care', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskPatientCareAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/patient-care', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskIndustrialCleaning = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-my/industrial-cleaning', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHomeMovingAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-my/home-moving', options);
};

export const getTaskChecklist = async (params: IParamGetTaskCheckList) => {
  return await fetchAPI('v5/api-asker-my/get-task-todo-list', params);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskChildCareAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/child-care', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateGroceryAssistantAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/grocery-assistant', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskLaundryAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/laundry', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHomeCookingAPI = async (options) => {
  return await fetchAPI('v5/update-task-my/home-cooking', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHairStylingAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-my/hair-styling', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskMakeupAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-my/makeup', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskNailAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-my/nail', options);
};
