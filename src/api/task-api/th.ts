import { fetchAPI, getIsoCodeGlobal } from '@helper';

import {
  IParamCheckTaskerConflictTime,
  IParamChooseDateOption,
  IParamGetTaskCheckList,
  IParamRemoveForceTasker,
  IParamUpdateTask,
} from './type';

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskDisinfectionAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/disinfection', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskSofaAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/sofa', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskOfficeCleaning = async (options) => {
  return await fetchAPI('v5/update-task-th/office-cleaning', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskWashingMachine = async (options) => {
  return await fetchAPI('v5/update-task-th/washing-machine', options);
};

/**
 * @param options =  {
    "taskId": "xxx"
    "newDate": "11/11/1111",
    "newCollectionDate": "11/11/1111",
    "detailHostel": {
        "rooms": [],
        ...
    },
    "newDuration": 2,
    "userId": "0834567890"
  }
 */
export const getExtraMoneyUpdateDateTimeAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/get-extra-money-update-date-time', options);
};

/**
   * @param options =  {
      "taskId": "xxx"
      "newDate": "11/11/1111",
      "newCollectionDate": "11/11/1111",
      "detailHostel": {
          "rooms": [],
          ...
      },
      "newDuration": 2,
      "userId": "0834567890"
    }
   */
export const checkTaskerConflictTimeAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/check-tasker-conflict-time', options);
};

/**
   * @param options =  {
      "taskId": "xxx"
      "newDate": "11/11/1111",
      "newCollectionDate": "11/11/1111",
      "detailHostel": {
          "rooms": [],
          ...
      },
      "newDuration": 2,
      "userId": "0834567890"
    }
   */
export const createUpdateDateTimeRequestAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/asker-create-update-date-time-request', options);
};

/**
   * @param options = {
      "chatId":    "xxxx"
      "messageId": "xxxx"
    }
   */
export const setDisableActionChatMessageAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/set-disable-action-chat-message', options);
};

/**
 * @param taskId
 * @param taskerId
 */
export const chooseTasker = async (options) => {
  return await fetchAPI('v5/accept-task-th/choose-tasker', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskChildCareAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/child-care', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskMassageAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-th/massage', options);
};

/**
 * @param taskId
 */
export const updateBringToToolsAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/add-cleaning-tool', options);
};

/**
 * @param taskId String
 * @param newTaskNote String
 */
export const updateTaskNote = async (options) => {
  return await fetchAPI('v5/update-task-th/update-task-note', options);
};

/**
 * @param options = {
      "taskId": "x62a73b18ef9e21f89ea73b63",
      "userId": "0834567890",
      "isPremium": false
    }
 */
export const changeToPremiumService = async (options) => {
  return await fetchAPI('v5/update-task-th/update-task-premium', options);
};

export const updateDishedHomeCooking = async (options) => {
  return await fetchAPI('v5/update-task-th/update-dish-detail', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Number
 */
export const updateTaskHomeCleaning = async (options) => {
  return await fetchAPI('v5/update-task-th/home-cleaning', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskAirConditioner = async (options) => {
  return await fetchAPI('v5/update-task-th/air-conditioner', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskDeepCleaning = async (options) => {
  return await fetchAPI('v5/update-task-th/deep-cleaning', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskWaterHeater = async (options) => {
  return await fetchAPI('v5/update-task-th/water-heater', options);
};

export const checkTaskerConflictTime = async (params: IParamCheckTaskerConflictTime) => {
  const options = {
    isoCode: getIsoCodeGlobal(),
    ...params,
  };
  return await fetchAPI('v5/api-asker-th/check-tasker-conflict-time', options);
};

export const getTaskerFreeTime = async (params: IParamCheckTaskerConflictTime) => {
  const options = {
    isoCode: getIsoCodeGlobal(),
    ...params,
  };
  return await fetchAPI('v5/api-asker-th/get-tasker-free-time', options);
};

export const removeForceTasker = async (params: IParamRemoveForceTasker) => {
  return await fetchAPI('v5/update-task-th/asker-remove-force-tasker', params);
};

export const chooseDateOption = async (params: IParamChooseDateOption) => {
  return await fetchAPI('v5/update-task-th/asker-choose-date-option', params);
};

/**
 * @param userId
 * @param taskId
 * @param reason
 * @param changeTaskToPosted
 */
export const cancelTask = async (options) => {
  return await fetchAPI('v5/cancel-task-th/cancel', options);
};

/**
 * @param taskId
 * @param reason
 * @param language
 */
export const checkCancelFee = async (options) => {
  return await fetchAPI('v5/cancel-task-th/get-fee-asker-cancel-task', options);
};

export const checkNumberOfCancel = async (options) => {
  return await fetchAPI('v5/cancel-task-th/check-number-of-cancel', options);
};

export const getLastPostedTask = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-last-post-task', options);
};

export const getLastPostedTaskCleaning = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-last-post-task-cleaning', options);
};

export const getTaskWithTaskId = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-task-detail', options);
};

export const getTaskSchedule = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-schedule-tasks', options);
};

/**
 * @param userId
 * @param scheduleId
 */
export const cancelTaskSchedule = async (options) => {
  return await fetchAPI('v5/api-asker-th/remove-task-schedule', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const updateTaskSchedule = async (options) => {
  return await fetchAPI('v5/api-asker-th/active-task-schedule', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const activeTaskScheduleByTaskId = async (options) => {
  return await fetchAPI('v5/api-asker-th/active-task-schedule-by-task-id', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param weekday
 * @param scheduleTime
 * @param scheduleDuration
 */
export const updateTaskScheduleTime = async (options) => {
  return await fetchAPI('v5/api-asker-th/update-schedule-time', options);
};

/**
 * @param userId
 */
export const getTaskSubscription = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-subscription-by-userId', options);
};

/**
 * @param userId
 */
export const getTaskUpComming = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-up-coming-tasks', options);
};

/**
 * @param isoCode
 */
export const getHistoryTask = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-list-history-tasks', options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getTaskDone = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-tasks-done', options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getTaskConfirmedToDone = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-tasks-confirmed-asker', options);
};

/**
 * @param userId
 * @param taskId
 */
export const doneTask = async (options) => {
  return await fetchAPI('v5/done-booking/done', options);
};

/**
 * @param taskId
 * @param reportData {reason: {vi,en,vi,th}}
 */
export const updateTaskReport = async (options) => {
  return await fetchAPI('v5/api-asker-th/update-task-report', options);
};

/**
 * @param taskId String
 * @param reason String
 * @param reportData {String}
 */
export const createUserComplaint = async (options) => {
  return await fetchAPI('v5/api-asker-th/create-user-complaint', options);
};

export const getStoreGrocery = async (params: any) => {
  return await fetchAPI('v5/api-asker-th/get-stores-grocery-assistant', params);
};

export const updateTaskElderlyCareAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/elderly-care', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskPatientCareAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/patient-care', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskIndustrialCleaning = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-th/industrial-cleaning', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHomeMovingAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-th/home-moving', options);
};

export const getTaskChecklist = async (params: IParamGetTaskCheckList) => {
  return await fetchAPI('v5/api-asker-th/get-task-todo-list', params);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHomeCookingAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/home-cooking', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskLaundryAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/laundry', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Date
 * @param detailHostel Object //  params same get price
 */
export const updateHouseKeepingAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/housekeeping', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateGroceryAssistantAPI = async (options) => {
  return await fetchAPI('v5/update-task-th/grocery-assistant', options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Number
 */
export const updateTaskIroning = async (options) => {
  return await fetchAPI('v5/update-task-th/ironing', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHairStylingAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-th/hair-styling', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskMakeupAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-th/makeup', options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskNailAPI = async (options: IParamUpdateTask) => {
  return await fetchAPI('v5/update-task-th/nail', options);
};
