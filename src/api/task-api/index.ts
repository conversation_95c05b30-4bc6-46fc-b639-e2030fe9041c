import { ID, MY, TH, VN } from '@config';
import { getIsoCodeGlobal, IRespond } from '@helper';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import {
  IParamCheckTaskerConflictTime,
  IParamChooseDateOption,
  IParamGetTaskCheckList,
  IParamRemoveForceTasker,
  IParamUpdateTask,
} from './type';
import * as VN_API from './vn';

/**
 * @param serviceId
 */
export const getLastPostedTask = async (serviceId: string) => {
  const options = {
    serviceId,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getLastPostedTask,
    TH: TH_API.getLastPostedTask,
    ID: ID_API.getLastPostedTask,
    MY: MY_API.getLastPostedTask,
  };
  return await combine[global.isoCode](options);
};

export const getLastPostedTaskCleaning = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getLastPostedTaskCleaning,
    TH: TH_API.getLastPostedTaskCleaning,
    ID: ID_API.getLastPostedTaskCleaning,
    MY: MY_API.getLastPostedTaskCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getTaskWithTaskId = async (taskId) => {
  const options = {
    taskId: taskId,
  };
  const combine = {
    VN: VN_API.getTaskWithTaskId,
    TH: TH_API.getTaskWithTaskId,
    ID: ID_API.getTaskWithTaskId,
    MY: MY_API.getTaskWithTaskId,
  };
  return await combine[global.isoCode](options);
};

export const getTaskUpComming = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getTaskUpComming,
    TH: TH_API.getTaskUpComming,
    ID: ID_API.getTaskUpComming,
    MY: MY_API.getTaskUpComming,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param taskId
 * @param reason
 * @param changeTaskToPosted
 */
export const cancelTask = async (taskId: string, reason: string, changeTaskToPosted: boolean) => {
  const options = {
    taskId: taskId,
    reason: reason,
    changeTaskToPosted: changeTaskToPosted,
  };
  const combine = {
    VN: VN_API.cancelTask,
    TH: TH_API.cancelTask,
    ID: ID_API.cancelTask,
    MY: MY_API.cancelTask,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId
 * @param taskerId
 */
export const chooseTasker = async (taskId: string, taskerId: string) => {
  const options = {
    taskId,
    taskerId,
  };
  const combine = {
    VN: VN_API.chooseTasker,
    TH: TH_API.chooseTasker,
    ID: ID_API.chooseTasker,
    MY: MY_API.chooseTasker,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId
 * @param reason
 * @param language
 */
export const checkCancelFee = async (taskId: string, reason: string, language?: string) => {
  const options = {
    taskId: taskId,
    reason: reason,
  };
  if (language) {
    options.language = language;
  }

  const combine = {
    VN: VN_API.checkCancelFee,
    TH: TH_API.checkCancelFee,
    ID: ID_API.checkCancelFee,
    MY: MY_API.checkCancelFee,
  };
  return await combine[global.isoCode](options);
};

export const checkNumberOfCancel = async () => {
  const options = {};
  const combine = {
    VN: VN_API.checkNumberOfCancel,
    TH: TH_API.checkNumberOfCancel,
    ID: ID_API.checkNumberOfCancel,
    MY: MY_API.checkNumberOfCancel,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 */
export const getTaskSubscription = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getTaskSubscription,
    TH: TH_API.getTaskSubscription,
    ID: ID_API.getTaskSubscription,
    MY: MY_API.getTaskSubscription,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getHistoryTask = async (page, limit = 10) => {
  const options = {
    isoCode: global.isoCode,
    page: page,
    limit: limit,
  };
  const combine = {
    VN: VN_API.getHistoryTask,
    TH: TH_API.getHistoryTask,
    ID: ID_API.getHistoryTask,
    MY: MY_API.getHistoryTask,
  };
  return await combine[global.isoCode](options);
};
//
// {
//     "userId": "xxx",
//     "taskId": "xxx",
//     "newDate": "2020-11-02 12:06:07.229Z",
//     "newDuration": 4
// }

// api update chuen sang v2 het
/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Number
 */
export const updateTaskHomeCleaning = async ({ taskId, newDate, newDuration, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
    newDuration: newDuration,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }

  if (messageId) {
    options.messageId = messageId;
  }

  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskHomeCleaning,
    TH: TH_API.updateTaskHomeCleaning,
    ID: ID_API.updateTaskHomeCleaning,
    MY: MY_API.updateTaskHomeCleaning,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskAirConditioner = async ({ taskId, newDate, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskAirConditioner,
    TH: TH_API.updateTaskAirConditioner,
    ID: ID_API.updateTaskAirConditioner,
    MY: MY_API.updateTaskAirConditioner,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHomeCooking = async ({ taskId, newDate, dishDetail, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };

  if (newDate) {
    options.newDate = newDate;
  }

  if (dishDetail) {
    options.dishDetail = dishDetail;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskHomeCookingAPI,
    TH: TH_API.updateTaskHomeCookingAPI,
    ID: ID_API.updateTaskHomeCookingAPI,
    MY: MY_API.updateTaskHomeCookingAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskDeepCleaning = async ({
  taskId,
  newDate,
  newDuration,
  detailDeepCleaning,
  newTaskNote,
  messageId,
  timezone,
}) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newDuration) {
    options.newDuration = newDuration;
    options.detailDeepCleaning = detailDeepCleaning;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskDeepCleaning,
    TH: TH_API.updateTaskDeepCleaning,
    ID: ID_API.updateTaskDeepCleaning,
    MY: MY_API.updateTaskDeepCleaning,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newCollectionDate Date
 */
export const updateTaskLaundry = async ({ taskId, newDate, newCollectionDate, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (newDate) {
    options.newDate = newDate;
  }
  if (newCollectionDate) {
    options.newCollectionDate = newCollectionDate;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }

  const combine = {
    VN: VN_API.updateTaskLaundryAPI,
    TH: TH_API.updateTaskLaundryAPI,
    ID: ID_API.updateTaskLaundryAPI,
    MY: MY_API.updateTaskLaundryAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateGroceryAssistant = async ({ taskId, newDate, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateGroceryAssistantAPI,
    TH: TH_API.updateGroceryAssistantAPI,
    ID: ID_API.updateGroceryAssistantAPI,
    MY: MY_API.updateGroceryAssistantAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Date
 * @param detailHostel Object //  params same get price
 */
export const updateHouseKeeping = async ({
  taskId,
  newDate,
  newDuration,
  detailHostel,
  newTaskNote,
  messageId,
  timezone,
}) => {
  const options: any = {
    taskId: taskId,
  };

  if (newDate) {
    options.newDate = newDate;
  }

  if (newDuration) {
    options.newDuration = newDuration;
  }

  if (detailHostel) {
    options.detailHostel = detailHostel;
  }

  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }

  const combine = {
    VN: VN_API.updateHouseKeepingAPI,
    TH: TH_API.updateHouseKeepingAPI,
    ID: ID_API.updateHouseKeepingAPI,
    MY: MY_API.updateHouseKeepingAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskSofa = async ({ taskId, newDate, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskSofaAPI,
    TH: TH_API.updateTaskSofaAPI,
    ID: ID_API.updateTaskSofaAPI,
    MY: MY_API.updateTaskSofaAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Number
 */
export const updateTaskElderlyCare = async ({ taskId, newDate, newDuration, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
    newDuration: newDuration,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskElderlyCareAPI,
    TH: TH_API.updateTaskElderlyCareAPI,
    ID: ID_API.updateTaskElderlyCareAPI,
    MY: MY_API.updateTaskElderlyCareAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Number
 */
export const updateTaskPatientCare = async ({ taskId, newDate, newDuration, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
    newDuration: newDuration,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskPatientCareAPI,
    TH: TH_API.updateTaskPatientCareAPI,
    ID: ID_API.updateTaskPatientCareAPI,
    MY: MY_API.updateTaskPatientCareAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskDisinfection = async ({ taskId, newDate, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskDisinfectionAPI,
    TH: TH_API.updateTaskDisinfectionAPI,
    ID: ID_API.updateTaskDisinfectionAPI,
    MY: MY_API.updateTaskDisinfectionAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getTaskDone = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getTaskDone,
    TH: TH_API.getTaskDone,
    ID: ID_API.getTaskDone,
    MY: MY_API.getTaskDone,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param isoCode
 */
export const getTaskConfirmedToDone = async (options) => {
  const combine = {
    VN: VN_API.getTaskConfirmedToDone,
    TH: TH_API.getTaskConfirmedToDone,
    ID: ID_API.getTaskConfirmedToDone,
    MY: MY_API.getTaskConfirmedToDone,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId
 * @param taskId
 */
export const doneTask = async (options) => {
  const combine = {
    VN: VN_API.doneTask,
    TH: TH_API.doneTask,
    ID: ID_API.doneTask,
    MY: MY_API.doneTask,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId
 * @param reportData {reason: {vi,en,vi,th}}
 */
export const updateTaskReport = async ({ taskId: taskId, reportData: reportData }) => {
  const options = {
    taskId: taskId,
    reportData: reportData,
  };
  const combine = {
    VN: VN_API.updateTaskReport,
    TH: TH_API.updateTaskReport,
    ID: ID_API.updateTaskReport,
    MY: MY_API.updateTaskReport,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId String
 * @param reason String
 * @param reportData {String}
 */
export const createUserComplaint = async (options) => {
  const combine = {
    VN: VN_API.createUserComplaint,
    TH: TH_API.createUserComplaint,
    ID: ID_API.createUserComplaint,
    MY: MY_API.createUserComplaint,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId
 */
export const updateBringToToolsAPI = async (taskId) => {
  const options = {
    taskId: taskId,
  };
  const combine = {
    VN: VN_API.updateBringToToolsAPI,
    TH: TH_API.updateBringToToolsAPI,
    ID: ID_API.updateBringToToolsAPI,
    MY: MY_API.updateBringToToolsAPI,
  };
  return await combine[global.isoCode](options);
};

export const updateDishedHomeCooking = async (taskId, dishDetail, newTaskNote) => {
  const options = {
    taskId: taskId,
    cookingDetail: { dishDetail: dishDetail },
  };
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }

  const combine = {
    VN: VN_API.updateDishedHomeCooking,
    TH: TH_API.updateDishedHomeCooking,
    ID: ID_API.updateDishedHomeCooking,
    MY: MY_API.updateDishedHomeCooking,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskPlace: {country, city, district, lat, lng}
 **/
export const getStoreGrocery = async ({ taskPlace }) => {
  const options = {
    taskPlace: {
      country: taskPlace.country,
      city: taskPlace.city,
      district: taskPlace.district,
      lat: taskPlace.lat,
      lng: taskPlace.lng,
    },
  };

  const combine = {
    VN: VN_API.getStoreGrocery,
    TH: TH_API.getStoreGrocery,
    ID: ID_API.getStoreGrocery,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId String
 * @param newTaskNote String
 */
export const updateTaskNote = async ({ taskId, newTaskNote }) => {
  const options = {
    taskId: taskId,
  };
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  const combine = {
    VN: VN_API.updateTaskNote,
    TH: TH_API.updateTaskNote,
    ID: ID_API.updateTaskNote,
    MY: MY_API.updateTaskNote,
  };
  return await combine[global.isoCode](options);
};
export const updateTaskChildCare = async ({ taskId, newDate, newDuration, newTaskNote, messageId }) => {
  const options: any = {
    taskId: taskId,
  };

  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }

  if (newDate) {
    options.newDate = newDate;
  }

  if (newDuration) {
    options.newDuration = newDuration;
  }

  if (messageId) {
    options.messageId = messageId;
  }

  const combine = {
    VN: VN_API.updateTaskChildCareAPI,
    TH: TH_API.updateTaskChildCareAPI,
    ID: ID_API.updateTaskChildCareAPI,
    MY: MY_API.updateTaskChildCareAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskOfficeCleaning = async ({ taskId, newDate, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }

  const combine = {
    VN: VN_API.updateTaskOfficeCleaning,
    TH: TH_API.updateTaskOfficeCleaning,
    ID: ID_API.updateTaskOfficeCleaning,
    MY: MY_API.updateTaskOfficeCleaning,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskWashingMachine = async ({ taskId, newDate, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskWashingMachine,
    TH: TH_API.updateTaskWashingMachine,
    ID: ID_API.updateTaskWashingMachine,
    MY: MY_API.updateTaskWashingMachine,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskWaterHeater = async ({ taskId, newDate, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskWaterHeater,
    TH: TH_API.updateTaskWaterHeater,
    ID: ID_API.updateTaskWaterHeater,
    MY: MY_API.updateTaskWaterHeater,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param options = {
      "taskId": "x62a73b18ef9e21f89ea73b63",
      "userId": "0834567890",
      "isPremium": false
    }
 */
export const changeToPremiumService = async (options) => {
  const combine = {
    VN: VN_API.changeToPremiumService,
    TH: TH_API.changeToPremiumService,
    ID: ID_API.changeToPremiumService,
    MY: MY_API.changeToPremiumService,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param options =  {
    "taskId": "xxx"
    "newDate": "11/11/1111",
    "newCollectionDate": "11/11/1111",
    "detailHostel": {
        "rooms": [],
        ...
    },
    "newDuration": 2,
    "userId": "0834567890"
  }
 */
export const getExtraMoneyUpdateDateTimeAPI = async (options) => {
  const combine = {
    VN: VN_API.getExtraMoneyUpdateDateTimeAPI,
    TH: TH_API.getExtraMoneyUpdateDateTimeAPI,
    ID: ID_API.getExtraMoneyUpdateDateTimeAPI,
    MY: MY_API.getExtraMoneyUpdateDateTimeAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param options =  {
    "taskId": "xxx"
    "newDate": "11/11/1111",
    "newCollectionDate": "11/11/1111",
    "detailHostel": {
        "rooms": [],
        ...
    },
    "newDuration": 2,
    "userId": "0834567890"
  }
 */
export const checkTaskerConflictTimeAPI = async (options) => {
  const combine = {
    VN: VN_API.checkTaskerConflictTimeAPI,
    TH: TH_API.checkTaskerConflictTimeAPI,
    ID: ID_API.checkTaskerConflictTimeAPI,
    MY: MY_API.checkTaskerConflictTimeAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param options =  {
    "taskId": "xxx"
    "newDate": "11/11/1111",
    "newCollectionDate": "11/11/1111",
    "detailHostel": {
        "rooms": [],
        ...
    },
    "newDuration": 2,
    "userId": "0834567890"
  }
 */
export const createUpdateDateTimeRequestAPI = async (options) => {
  const combine = {
    VN: VN_API.createUpdateDateTimeRequestAPI,
    TH: TH_API.createUpdateDateTimeRequestAPI,
    ID: ID_API.createUpdateDateTimeRequestAPI,
    MY: MY_API.createUpdateDateTimeRequestAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param options = {
    "chatId":    "xxxx"
    "messageId": "xxxx"
  }
 */
export const setDisableActionChatMessage = async (options) => {
  const combine = {
    VN: VN_API.setDisableActionChatMessageAPI,
    TH: TH_API.setDisableActionChatMessageAPI,
    ID: ID_API.setDisableActionChatMessageAPI,
    MY: MY_API.setDisableActionChatMessageAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskOfficeCarpetCleaning = async ({
  taskId,
  newDate,
  newTaskNote,
  messageId,
  timezone,
}: IParamUpdateTask) => {
  const options: any = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }
  if (messageId) {
    options.messageId = messageId;
  }
  if (timezone) {
    options.timezone = timezone;
  }
  // const combine = {
  // VN: VN_API.updateTaskOfficeCarpetCleaningAPI,
  // TH: null,
  // ID: null,
  // };
  return await VN_API.updateTaskOfficeCarpetCleaningAPI(options);
};

export const updateTaskMassage = async (options: IParamUpdateTask) => {
  const combine = {
    VN: VN_API.updateTaskMassageAPI,
    TH: TH_API.updateTaskMassageAPI,
    ID: ID_API.updateTaskMassageAPI,
    MY: MY_API.updateTaskMassageAPI,
  };
  return await combine[global.isoCode](options);
};

export const checkTaskerConflictTime = (...args: [IParamCheckTaskerConflictTime]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.checkTaskerConflictTime,
    [TH]: TH_API.checkTaskerConflictTime,
    [ID]: ID_API.checkTaskerConflictTime,
    [MY]: MY_API.checkTaskerConflictTime,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const getTaskerFreeTime = (...args: [IParamCheckTaskerConflictTime]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.getTaskerFreeTime,
    [TH]: TH_API.getTaskerFreeTime,
    [ID]: ID_API.getTaskerFreeTime,
    [MY]: MY_API.getTaskerFreeTime,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const removeForceTasker = (...args: [IParamRemoveForceTasker]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.removeForceTasker,
    [TH]: TH_API.removeForceTasker,
    [ID]: ID_API.removeForceTasker,
    [MY]: MY_API.removeForceTasker,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const chooseDateOption = (...args: [IParamChooseDateOption]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.chooseDateOption,
    [TH]: TH_API.chooseDateOption,
    [ID]: ID_API.chooseDateOption,
    [MY]: MY_API.chooseDateOption,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskIndustrialCleaning = async ({ taskId, newDate }: IParamUpdateTask) => {
  const options: IParamUpdateTask = {
    taskId: taskId,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  const combine = {
    VN: VN_API.updateTaskIndustrialCleaning,
    TH: TH_API.updateTaskIndustrialCleaning,
    ID: ID_API.updateTaskIndustrialCleaning,
    MY: MY_API.updateTaskIndustrialCleaning,
  };
  return await combine[getIsoCodeGlobal()](options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const getTaskChecklist = async (options: IParamGetTaskCheckList) => {
  const combine = {
    VN: VN_API.getTaskChecklist,
    TH: TH_API.getTaskChecklist,
    ID: ID_API.getTaskChecklist,
    MY: MY_API.getTaskChecklist,
  };
  return await combine[getIsoCodeGlobal()](options);
};

/**
 * @param taskId String
 * @param newDate Date
 */
export const updateTaskHomeMoving = async (options: IParamUpdateTask) => {
  const combine = {
    VN: VN_API.updateTaskHomeMovingAPI,
    TH: TH_API.updateTaskHomeMovingAPI,
    ID: ID_API.updateTaskHomeMovingAPI,
    MY: MY_API.updateTaskHomeMovingAPI,
  };
  return await combine[getIsoCodeGlobal()](options);
};

export const updateTaskHairStyling = (...args: [IParamUpdateTask]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.updateTaskHairStylingAPI,
    [TH]: TH_API.updateTaskHairStylingAPI,
    [ID]: ID_API.updateTaskHairStylingAPI,
    [MY]: MY_API.updateTaskHairStylingAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const updateTaskMakeup = (...args: [IParamUpdateTask]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.updateTaskMakeupAPI,
    [TH]: TH_API.updateTaskMakeupAPI,
    [ID]: ID_API.updateTaskMakeupAPI,
    [MY]: MY_API.updateTaskMakeupAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const updateTaskNail = (...args: [IParamUpdateTask]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.updateTaskNailAPI,
    [TH]: TH_API.updateTaskNailAPI,
    [ID]: ID_API.updateTaskNailAPI,
    [MY]: MY_API.updateTaskNailAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

// api update chuen sang v2 het
/**
 * @param userId String
 * @param taskId String
 * @param newDate Date
 * @param newDuration Number
 */
export const updateTaskIroning = async ({ taskId, newDate, newDuration, newTaskNote, messageId, timezone }) => {
  const options: any = {
    taskId: taskId,
    newDuration: newDuration,
  };
  if (newDate) {
    options.newDate = newDate;
  }
  if (newTaskNote) {
    options.newTaskNote = newTaskNote;
  }

  if (messageId) {
    options.messageId = messageId;
  }

  if (timezone) {
    options.timezone = timezone;
  }
  const combine = {
    VN: VN_API.updateTaskIroning,
    TH: TH_API.updateTaskIroning,
    ID: ID_API.updateTaskIroning,
  };
  return await combine[global.isoCode](options);
};
