import { IRespond } from '@src/lib/types';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import { AskerSendSurveyParams, ISendSurveyResponse, ISurveyDetail, SurveyDetailParams } from './type';
import * as VN_API from './vn';

export async function getSurveyDetailAPI(surveyId: SurveyDetailParams['surveyId']): Promise<IRespond<ISurveyDetail>> {
  const data: SurveyDetailParams = {
    isoCode: global.isoCode,
    surveyId,
  };

  const combine = {
    VN: VN_API.getSurveyDetail,
    TH: TH_API.getSurveyDetail,
    ID: ID_API.getSurveyDetail,
    MY: MY_API.getSurveyDetail,
  };

  return await combine[global.isoCode](data);
}

export async function askerSendSurveyAPI({
  surveyId,
  surveyAnswers,
}: {
  surveyId: AskerSendSurveyParams['surveyId'];
  surveyAnswers: AskerSendSurveyParams['surveyAnswers'];
}): Promise<IRespond<ISendSurveyResponse>> {
  const data: AskerSendSurveyParams = {
    isoCode: global.isoCode,
    surveyId,
    surveyAnswers,
  };

  const combine = {
    VN: VN_API.askerSendSurvey,
    TH: TH_API.askerSendSurvey,
    ID: ID_API.askerSendSurvey,
    MY: MY_API.askerSendSurvey,
  };

  return await combine[global.isoCode](data);
}
