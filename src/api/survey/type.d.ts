import { PromotionType, SurveyQuestionType } from '@config';
import { IObjectText } from '@src/lib/types';

export type SurveyDetailParams = {
  isoCode: string;
  surveyId: string;
};

export type AskerSendSurveyParams = {
  isoCode: string;
  surveyId: string;
  surveyAnswers: IQuestionSurvey[];
};

export type IAnswer = IObjectText;

export type IQuestionSurvey = {
  type?: SurveyQuestionType;
  question?: IObjectText;
  answers?: IAnswer[];
  rate?: number;
  input?: string;
};

export type IReward = {
  title?: IObjectText;
  content?: IObjectText;
  note?: IObjectText;
  promotionPeriod?: number;
  promotionPrefix?: string;
  promotionType?: PromotionType;
  promotionValue?: number;
  image?: string;
  promotionServiceId?: string;
};

export type ISurveyDetail = {
  _id?: string;
  name?: string;
  questions?: IQuestionSurvey[];
  reward?: IReward;
};

export type ISendSurveyResponse = {
  giftId?: string;
};
