import { fetchAPI } from '@helper';

import { IParamsAddHousekeepingLocation, IParamsDeleteHousekeepingLocation } from './type';

export const createHousekeepingLocation = async (data: IParamsAddHousekeepingLocation) => {
  return await fetchAPI('v5/api-asker-vn/add-housekeeping-location', data);
};

export const deleteRoomHousekeepingLocation = async (data: IParamsDeleteHousekeepingLocation) => {
  return await fetchAPI('v5/api-asker-vn/delete-housekeeping-location', data);
};

export const updateRoomHousekeepingLocation = async (data: IParamsAddHousekeepingLocation) => {
  return await fetchAPI('v5/api-asker-vn/update-housekeeping-location', data);
};
