import { fetchAPI } from '@helper';

export const getMarketingCampaignAPI = async () => {
  const options = {
    isoCode: global.isoCode,
  };

  // Call this api with user not login
  if (!global.userId) {
    return await fetchAPI('v5/api-asker-vn/get-marketing-campaign-without-login', options);
  }

  return await fetchAPI('v5/api-asker-vn/get-marketing-campaign', options);
};

export const getMarketingCampaignDetail = async (campaignId) => {
  const options = {
    isoCode: global.isoCode,
    campaignId,
  };
  return await fetchAPI('v5/api-asker-vn/get-marketing-campaign-detail', options);
};

export const getSpecialMarketingCampaignAPI = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  return await fetchAPI('v5/api-asker-vn/get-special-campaign', options);
};

export const trackingMarketingCampaignAPI = async (campaignId, campaignAction = 'seen') => {
  const options = {
    campaignId,
    campaignAction,
  };

  // User not login, no call api
  if (!global.userId) {
    return null;
  }

  return await fetchAPI('v5/api-asker-vn/track-user-campaign', options);
};
