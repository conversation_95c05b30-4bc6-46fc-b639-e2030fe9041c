import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export const getMarketingCampaignAPI = async () => {
  const combine = {
    VN: VN_API.getMarketingCampaignAPI,
    TH: TH_API.getMarketingCampaignAPI,
    ID: ID_API.getMarketingCampaignAPI,
    MY: MY_API.getMarketingCampaignAPI,
  };
  return await combine[global.isoCode]();
};

export const getMarketingCampaignDetail = async (campaignId) => {
  const combine = {
    VN: VN_API.getMarketingCampaignDetail,
    TH: TH_API.getMarketingCampaignDetail,
    ID: ID_API.getMarketingCampaignDetail,
    MY: MY_API.getMarketingCampaignDetail,
  };
  return await combine[global.isoCode](campaignId);
};

export const getSpecialMarketingCampaignAPI = async () => {
  const combine = {
    VN: VN_API.getSpecialMarketingCampaignAPI,
    TH: TH_API.getSpecialMarketingCampaignAPI,
    ID: ID_API.getSpecialMarketingCampaignAPI,
    MY: MY_API.getSpecialMarketingCampaignAPI,
  };
  return await combine[global.isoCode]();
};

export const trackingMarketingCampaignAPI = async (campaignId, campaignAction = 'seen') => {
  const combine = {
    VN: VN_API.trackingMarketingCampaignAPI,
    TH: TH_API.trackingMarketingCampaignAPI,
    ID: ID_API.trackingMarketingCampaignAPI,
    MY: MY_API.trackingMarketingCampaignAPI,
  };
  return await combine[global.isoCode](campaignId, (campaignAction = 'seen'));
};
