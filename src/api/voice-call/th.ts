import { fetchAPI } from '@helper';

export const updateVoiceCallToken = async () => {
  return await fetchAPI('v5/user-asker-th/update-voice-call-token');
};

export const updateVoiceCallTokenV2 = async (params) => {
  return await fetchAPI('v5/user-asker-th/update-voice-call-token-v2', params);
};

export const setVoiceCallHistory = async (params) => {
  return await fetchAPI('v5/user-asker-th/set-voice-call-history', params);
};
