import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

/**
 * @param taskId String
 * @param data Object
 */
export const setVoiceCallHistory = async (taskId, data) => {
  /*"data": {
    "callId":   "xxxx",
    "status": "xxxx",
    "from":    "xxxx",
    "to":    "xxxx",
    "type":    "xxxx",
},*/
  const options = {
    taskId,
    data,
  };
  const combine = {
    VN: VN_API.setVoiceCallHistory,
    TH: TH_API.setVoiceCallHistory,
    ID: ID_API.setVoiceCallHistory,
    MY: MY_API.setVoiceCallHistory,
  };

  return await combine[global.isoCode](options);
};

/**
 */
export const updateVoiceCallToken = async () => {
  const combine = {
    VN: VN_API.updateVoiceCallToken,
    TH: TH_API.updateVoiceCallToken,
    ID: ID_API.updateVoiceCallToken,
    MY: MY_API.updateVoiceCallToken,
  };

  return await combine[global.isoCode]();
};

export const updateVoiceCallTokenV2 = async () => {
  const options = {
    userId: global.userId,
  };
  const combine = {
    VN: VN_API.updateVoiceCallTokenV2,
    TH: TH_API.updateVoiceCallTokenV2,
    ID: ID_API.updateVoiceCallTokenV2,
  };

  return await combine[global.isoCode](options);
};
