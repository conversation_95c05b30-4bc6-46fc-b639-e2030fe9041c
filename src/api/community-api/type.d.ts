import { IImagesCommunity, IPostTheme, IVideoCommunity } from '@models/community/news-feed.model';
import { IUserCommunity } from '@models/community/user';
import { DisplayPositionHashTag, TypeReportCommunity } from '@src/lib/config/community';

export interface IParamGetListNewsFeeds {
  tagIds?: string[]; // Nếu chưa đăng nhập
  tagId?: string; // Nếu filter tag cụ thể
  sortKey?: string; // MOST_RECENT, MOST_LIKED, MOST_SHARED
  fromDate?: string; // Nếu filter theo ngày Infinity roll (createAt của record cuối cùng)
  lastNumberOfLikes?: number;
  lastNumberOfShares?: number;
}

export interface IParamSearchPostAPI {
  searchText: string;
  sortKey?: string; // MOST_RECENT, MOST_RELEVANT
  fromDate?: string; // Nếu filter theo ngày Infinity roll (createAt của record cuối cùng)
  lastScore?: number; // dien score cua gia tri cuoi cung sortKey = MOST_RELEVANT
}

export interface IParamGetNewsFeedDetailAPI {
  postId: string;
}

export interface IParamSearchUserAPI {
  searchText?: string;
  fromDate?: Date; // Nếu filter theo ngày Infinity roll (createAt của record cuối cùng)
}

export interface IParamInsertUserCommunityAPI {
  favouriteTagIds?: string[];
}

export interface IParamCreateNewsPost {
  tagIds: string[];
  content?: string;
  isNeedHelpPost?: boolean;
  images?: IImagesCommunity[];
  videos?: IVideoCommunity[];
  postTheme?: IPostTheme;
}
export interface IParamGetUserProfileCommunityAPI {
  userId?: string;
  profileId?: string;
}

export interface IParamGetUserPostsAPI {
  userId?: string;
  profileId?: string;
}

export interface IParamGetUserSharedPostsAPI {
  userId?: string;
  profileId?: string;
}

export type IParamEditPost = {
  _id: string;
  tagIds: string[];
  content?: string;
  images?: IImagesCommunity[];
  videos?: IVideoCommunity[];
};

export type IParamDeletePost = {
  postId: string;
};

export type IParamsGetNotificationsAPI = {
  userId?: string;
  limit: number;
  page: number;
};

export type IParamsSetIsReadNotificationAPI = {
  notificationId: string;
};

export type IParamReportUser = {
  reason: {
    name: TypeReportCommunity;
    content?: string;
  };
  profileId?: string;
  postId?: string;
  commentId?: string;
  userId?: string;
};
export type IParamCommentPost = {
  content: string;
  postId: string;
  mentions?: {
    userId: string;
    username: string;
  }[];
};

export type IParamReplyComment = {
  content: string;
  commentId: string;
  postId: string;
  mentions?: {
    userId: string;
    username: string;
  }[];
};

export type IParamLikeAndUnlikeComment = {
  postId: string;
  commentId: string;
};

export type IParamLikeAndUnlikePost = {
  postId: string;
};

export type IParamGetComments = {
  postId: string;
  fromDate?: string;
  commentId?: string;
};

export type IParamUpdateUserProfile = {
  avatar?: string;
  bio?: string;
  medalIdInUse?: string;
  name?: string;
};

export type IParamSharePost = {
  sharedByPostId?: string;
  content?: string;
};

export interface IParamGetAllHashTags {
  displayPosition: DisplayPositionHashTag;
}

export type IParamFollowUser = {
  targetUserId: string;
};

export type IParamUnFollowUser = {
  targetUserId: string;
};

export type IParamGetListFollowing = {
  targetUserId: string;
  skip?: number;
  searchText?: string;
};

export type IParamGetListFollowers = {
  targetUserId: string;
  skip?: number;
  searchText?: string;
};

export type IResponseGetListFollowing = {
  listFollowing?: IUserCommunity[];
  numberOfFollowers?: number;
  numberOfFollowing?: number;
};

export type IResponseGetListFollowers = {
  listFollowers?: IUserCommunity[];
  numberOfFollowers?: number;
  numberOfFollowing?: number;
};

export type IParamHideAndUnHidePost = {
  postId: string;
};

export type IParamBlockAndUnBlockUser = {
  blockedUserId: string;
};
