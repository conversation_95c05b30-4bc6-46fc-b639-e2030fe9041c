import { fetchAPI } from '@helper';

import {
  IParamBlockAndUnBlockUser,
  IParamCommentPost,
  IParamCreateNewsPost,
  IParamDeletePost,
  IParamEditPost,
  IParamFollowUser,
  IParamGetAllHashTags,
  IParamGetComments,
  IParamGetListFollowers,
  IParamGetListFollowing,
  IParamGetListNewsFeeds,
  IParamGetNewsFeedDetailAPI,
  IParamGetUserPostsAPI,
  IParamGetUserProfileCommunityAPI,
  IParamGetUserSharedPostsAPI,
  IParamHideAndUnHidePost,
  IParamInsertUserCommunityAPI,
  IParamLikeAndUnlikeComment,
  IParamLikeAndUnlikePost,
  IParamReplyComment,
  IParamReportUser,
  IParamSearchPostAPI,
  IParamSearchUserAPI,
  IParamsGetNotificationsAPI,
  IParamSharePost,
  IParamsSetIsReadNotificationAPI,
  IParamUnFollowUser,
  IParamUpdateUserProfile,
} from './type';

export const getListNewsFeedsAPI = async (params: IParamGetListNewsFeeds) => {
  return await fetchAPI('v5/community-indo/get-posts', params);
};

export const getNewsFeedDetailAPI = async (params: IParamGetNewsFeedDetailAPI) => {
  return await fetchAPI('v5/community-indo/get-post-detail', params);
};

export const searchPostAPI = async (params: IParamSearchPostAPI) => {
  return await fetchAPI('v5/community-indo/search-post', params);
};

export const searchUserAPI = async (params: IParamSearchUserAPI) => {
  return await fetchAPI('v5/community-indo/search-user', params);
};

export const getAllTagsAPI = async (params: IParamGetAllHashTags) => {
  return await fetchAPI('v5/community-indo/get-all-tags', params);
};

export const insertUserCommunityAPI = async (params: IParamInsertUserCommunityAPI) => {
  return await fetchAPI('v5/community-indo/insert-user', params);
};

export const getUserCommunityAPI = async () => {
  return await fetchAPI('v5/community-indo/get-user');
};

export const getUserProfileCommunityAPI = async (params: IParamGetUserProfileCommunityAPI) => {
  return await fetchAPI('v5/community-vn/get-indo', params);
};

export const createNewsPost = async (params: IParamCreateNewsPost) => {
  return await fetchAPI('v5/community-indo/create-post', params);
};

export const getUserPostsAPI = async (params: IParamGetUserPostsAPI) => {
  return await fetchAPI('v5/community-indo/get-user-posts', params);
};

export const getUserSharedPostsAPI = async (params: IParamGetUserSharedPostsAPI) => {
  return await fetchAPI('v5/community-indo/get-user-shared-posts', params);
};

export const editPost = async (params: IParamEditPost) => {
  return await fetchAPI('v5/community-indo/edit-post', params);
};

export const deletePost = async (params: IParamDeletePost) => {
  return await fetchAPI('v5/community-indo/delete-post', params);
};

export const getNotificationsAPI = async (params: IParamsGetNotificationsAPI) => {
  return await fetchAPI('v5/community-indo/get-notification', params);
};

export const setIsReadNotificationAPI = async (params: IParamsSetIsReadNotificationAPI) => {
  return await fetchAPI('v5/community-indo/set-notification-as-read', params);
};

export const reportUser = async (params: IParamReportUser) => {
  return await fetchAPI('v5/community-indo/insert-user-report', params);
};
export const commentPost = async (params: IParamCommentPost) => {
  return await fetchAPI('v5/community-indo/comment-post', params);
};

export const replyComment = async (params: IParamReplyComment) => {
  return await fetchAPI('v5/community-indo/reply-comment', params);
};

export const likeAndUnLikeComment = async (params: IParamLikeAndUnlikeComment) => {
  return await fetchAPI('v5/community-indo/like-and-unlike-comment', params);
};

export const likeAndUnLikePost = async (params: IParamLikeAndUnlikePost) => {
  return await fetchAPI('v5/community-indo/like-and-unlike-post', params);
};

export const getComments = async (params: IParamGetComments) => {
  return await fetchAPI('v5/community-indo/get-comments', params);
};

export const updateUserProfile = async (params: IParamUpdateUserProfile) => {
  return await fetchAPI('v5/community-indo/update-user-profile', params);
};

export const getOwnedMedals = async () => {
  return await fetchAPI('v5/community-indo/get-owned-medals');
};

export const sharePost = async (params: IParamSharePost) => {
  return await fetchAPI('v5/community-indo/share-post', params);
};

export const followUser = async (params: IParamFollowUser) => {
  return await fetchAPI('v5/community-indo/follow-user', params);
};

export const unFollowUser = async (params: IParamUnFollowUser) => {
  return await fetchAPI('v5/community-indo/unfollow-user', params);
};

export const getListFollowing = async (params: IParamGetListFollowing) => {
  return await fetchAPI('v5/community-indo/get-list-following', params);
};

export const getListFollowers = async (params: IParamGetListFollowers) => {
  return await fetchAPI('v5/community-indo/get-list-followers', params);
};

export const hideAndUnHidePost = async (params: IParamHideAndUnHidePost) => {
  return await fetchAPI('v5/community-indo/hide-and-unhide-post', params);
};

export const blockAndUnBlockUser = async (params: IParamBlockAndUnBlockUser) => {
  return await fetchAPI('v5/community-vn/block-and-unblock-user', params);
};

export const getTermConditions = async () => {
  return await fetchAPI('v5/community-indo/get-term-conditions');
};
