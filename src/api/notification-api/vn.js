import { fetchAPI } from '@helper';

/**
  @param userId
  @param page
  @param limit
**/
export const getAllNotification = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-notification-not-from-btaskee', options);
};

/**
  @param userId
**/
export const removeAllNotification = async () => {
  return await fetchAPI('v5/api-asker-vn/remove-notification-user');
};

export const removeNotificationById = async (options) => {
  return await fetchAPI('v5/api-asker-vn/remove-notification-by-id', options);
};

export const removeNotificationNotFromBtaskeeById = async (options) => {
  return await fetchAPI('v5/api-asker-vn/remove-notification-not-from-btaskee-by-id', options);
};

export const sendTokenToServer = async (options) => {
  return await fetchAPI('v5/api-asker-vn/init-raix-push-token', options);
};

export const updateIsReadNotification = async (options) => {
  return await fetchAPI('v5/api-asker-vn/update-status-notification', options);
};
