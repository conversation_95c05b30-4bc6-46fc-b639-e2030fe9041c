import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

/**
  @param userId
  @param page
  @param limit
**/
export const getAllNotification = async (page, limit) => {
  const options = {
    page: page,
    limit: limit,
  };
  const combine = {
    VN: VN_API.getAllNotification,
    TH: TH_API.getAllNotification,
    ID: ID_API.getAllNotification,
    MY: MY_API.getAllNotification,
  };
  return await combine[global.isoCode](options);
};

/**
  @param userId
**/
export const removeAllNotification = async () => {
  const combine = {
    VN: VN_API.removeAllNotification,
    TH: TH_API.removeAllNotification,
    ID: ID_API.removeAllNotification,
    MY: MY_API.removeAllNotification,
  };
  return await combine[global.isoCode]();
};

export const removeNotificationById = async (id) => {
  const options = {
    id: id,
  };
  const combine = {
    VN: VN_API.removeNotificationById,
    TH: TH_API.removeNotificationById,
    ID: ID_API.removeNotificationById,
    MY: MY_API.removeNotificationById,
  };
  return await combine[global.isoCode](options);
};

export const removeNotificationNotFromBtaskeeById = async (id) => {
  const options = {
    id: id,
  };
  const combine = {
    VN: VN_API.removeNotificationNotFromBtaskeeById,
    TH: TH_API.removeNotificationNotFromBtaskeeById,
    ID: ID_API.removeNotificationNotFromBtaskeeById,
    MY: MY_API.removeNotificationNotFromBtaskeeById,
  };
  return await combine[global.isoCode](options);
};

export const sendTokenToServer = async (options) => {
  const combine = {
    VN: VN_API.sendTokenToServer,
    TH: TH_API.sendTokenToServer,
    ID: ID_API.sendTokenToServer,
    MY: MY_API.sendTokenToServer,
  };
  return await combine[global?.isoCode](options);
};

export const updateIsReadNotification = async (id) => {
  const options = {
    id: id,
  };
  const combine = {
    VN: VN_API.updateIsReadNotification,
    TH: TH_API.updateIsReadNotification,
    ID: ID_API.updateIsReadNotification,
    MY: MY_API.updateIsReadNotification,
  };
  return await combine[global.isoCode](options);
};
