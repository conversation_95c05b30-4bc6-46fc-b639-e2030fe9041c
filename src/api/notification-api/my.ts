import { fetchAPI } from '@helper';

/**
  @param userId
  @param page
  @param limit
**/
export const getAllNotification = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-notification-not-from-btaskee', options);
};

/**
  @param userId
**/
export const removeAllNotification = async () => {
  return await fetchAPI('v5/api-asker-my/remove-notification-user');
};

export const removeNotificationById = async (options) => {
  return await fetchAPI('v5/api-asker-my/remove-notification-by-id', options);
};

export const removeNotificationNotFromBtaskeeById = async (options) => {
  return await fetchAPI('v5/api-asker-my/remove-notification-not-from-btaskee-by-id', options);
};

export const sendTokenToServer = async (options) => {
  return await fetchAPI('v5/api-asker-my/init-raix-push-token', options);
};

export const updateIsReadNotification = async (options) => {
  return await fetchAPI('v5/api-asker-my/update-status-notification', options);
};
