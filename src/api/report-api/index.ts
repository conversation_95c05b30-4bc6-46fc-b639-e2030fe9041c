import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';
/**
 * @description Get asker end year report
 *
 * @param isoCode
 * @param userId
 * **/
export const getEndYearReportApi = async () => {
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getEndYearReportApi,
    TH: TH_API.getEndYearReportApi,
    ID: ID_API.getEndYearReportApi,
    MY: MY_API.getEndYearReportApi,
  };
  return await combine[global.isoCode](options);
};
