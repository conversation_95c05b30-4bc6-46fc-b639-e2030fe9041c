import { ID, MY, TH, VN } from '@config';
import { getIsoCodeGlobal } from '@helper';

import apiID from './id';
import apiMY from './my';
import apiTH from './th';
import apiVN from './vn';

const getEndPoint = async () => {
  const apis = new Map([
    [VN, await apiVN()],
    [TH, await apiTH()],
    [ID, await apiID()],
    [MY, await apiMY()],
  ]);
  return apis.get(getIsoCodeGlobal());
};

export default getEndPoint;
