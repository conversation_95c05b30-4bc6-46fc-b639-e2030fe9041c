import { fetchAPI } from '@helper';

import { IParamPricingTaskDateOptions, IParamsPricingBeautyCare } from './type';

/**
 * @param task {{object}}
 * @param service {{object}}
 * @example
 * task :
 *  date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: <PERSON><PERSON><PERSON>(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    taskId = task._id,
    promotion = { code: task.promotion.code };
    requirements = [{ type: 1}];
    detail = task.detail;
    service :
    {_id: ''}
 */
export const getPriceCleaning = async (options) => {
  return await fetchAPI('v5/pricing-my/home-cleaning', options, 'post', false);
};

export const getPriceAirConditioner = async (options) => {
  return await fetchAPI('v5/pricing-my/air-conditioner-v2', options, 'post', false);
};

export const getPriceDeepCleaning = async (options) => {
  return await fetchAPI('v5/pricing-my/deep-cleaning', options, 'post', false);
};

export const getPriceOfficeCleaning = async (options) => {
  return await fetchAPI('v5/pricing-my/office-cleaning', options, 'post', false);
};

export const getPriceWashingMachine = async (options) => {
  return await fetchAPI('v5/pricing-my/washing-machine', options, 'post', false);
};

export const getPriceWaterHeater = async (options) => {
  return await fetchAPI('v5/pricing-my/water-heater', options, 'post', false);
};

export const getPriceOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v5/pricing-my/carpet-cleaning', options, 'post', false);
};

export const getPriceSofaCleaning = async (options) => {
  return await fetchAPI('v5/pricing-my/sofa', options, 'post', false);
};

export const getPriceDisinfection = async (options) => {
  return await fetchAPI('v5/pricing-my/disinfection', options, 'post', false);
};

export const getPriceMassage = async (options) => {
  return await fetchAPI('v5/pricing-my/massage', options, 'post', false);
};

export const getPriceHomeMoving = async (options) => {
  return await fetchAPI('v5/pricing-my/home-moving', options, 'post', false);
};

export const getPriceChildCare = async (options) => {
  return await fetchAPI('v5/pricing-my/child-care', options, 'post', false);
};

export const pricingTaskDateOptions = async (options: IParamPricingTaskDateOptions) => {
  return await fetchAPI('v5/pricing-my/task-date-options', options, 'post', false);
};

export const pricingLaundry = async (options) => {
  return await fetchAPI('v5/pricing-my/laundry', options, 'post', false);
};

export const getPriceHousekeeping = async (options) => {
  return await fetchAPI('v5/pricing-my/housekeeping-v2', options, 'post', false);
};

export const getPriceElderlyCare = async (options) => {
  return await fetchAPI('v5/pricing-my/elderly-care', options, 'post', false);
};

export const getPricePatientCare = async (options) => {
  return await fetchAPI('v5/pricing-my/patient-care', options, 'post', false);
};

export const getPriceHomeCooking = async (options) => {
  return await fetchAPI('v5/pricing-my/home-cooking', options, 'post', false);
};

export const getPriceGroceryAssistant = async (options) => {
  return await fetchAPI('v5/pricing-my/grocery-assistant', options, 'post', false);
};

export const getPriceMakeupAPI = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-my/makeup', options);
};

export const getPriceHairStylingAPI = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-my/hair-styling', options);
};

export const getPriceNailAPI = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-my/nail', options);
};
