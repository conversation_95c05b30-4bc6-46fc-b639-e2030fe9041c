import { PAYMENT_METHOD } from '@config';
import { IDate, ITimezone } from '@helper/date-time.helpers';
import { IService, ITaskDetail, ITaskPlace } from '@models/task/detail.model';
import { IPostTaskHairStylingState } from '@src/screens/services/beauty-care/hair-styling/redux/reducer/types';
import { IPostTaskMakeupState } from '@src/screens/services/beauty-care/makeup/redux/reducer/types';
import { IPostTaskNailState } from '@src/screens/services/beauty-care/nail/redux/reducer/types';

export interface IParamPricingTaskDateOptions {
  service?: IService;
  task?: ITaskDetail;
}

export type ITaskPricing = {
  timezone?: ITimezone;
  date?: IDate;
  taskPlace?: ITaskPlace;
  homeType?: string;
  payment?: {
    method?: PAYMENT_METHOD;
  };
  promotion?: { code?: string };
};

export type IParamsPricing<T> = {
  task: ITaskPricing & T;
  service: { _id: string };
};

export type IParamsPricingBeautyCare = {
  detailHairStyling?: IPostTaskHairStylingState['detailHairStyling'];
  detailMakeup?: IPostTaskMakeupState['detailMakeup'];
  detailNail?: IPostTaskNailState['detailNail'];
};
