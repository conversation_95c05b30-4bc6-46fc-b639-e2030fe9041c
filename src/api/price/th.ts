import { fetchAPI } from '@helper';

import { IParamPricingTaskDateOptions, IParamsPricingBeautyCare } from './type';

/**
 * @param task {{object}}
 * @param service {{object}}
 * @example
 * task :
 *  date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: <PERSON><PERSON><PERSON>(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    taskId = task._id,
    promotion = { code: task.promotion.code };
    requirements = [{ type: 1}];
    detail = task.detail;
    service :
    {_id: ''}
 */

export const getPriceCleaning = async (options) => {
  return await fetchAPI('v5/pricing-th/home-cleaning', options, 'post', false);
};

export const getPriceAirConditioner = async (options) => {
  return await fetchAPI('v5/pricing-th/air-conditioner-v2', options, 'post', false);
};

export const getPriceDeepCleaning = async (options) => {
  return await fetchAPI('v5/pricing-th/deep-cleaning', options, 'post', false);
};

export const getPriceSofaCleaning = async (options) => {
  return await fetchAPI('v5/pricing-th/sofa', options, 'post', false);
};

export const getPriceDisinfection = async (options) => {
  return await fetchAPI('v5/pricing-th/disinfection', options, 'post', false);
};

export const getPriceOfficeCleaning = async (options) => {
  return await fetchAPI('v5/pricing-th/office-cleaning', options, 'post', false);
};

export const getPriceHomeMoving = async (options) => {
  return await fetchAPI('v5/pricing-th/home-moving', options, 'post', false);
};

export const getPriceMassage = async (options) => {
  return await fetchAPI('v5/pricing-th/massage', options, 'post', false);
};

export const getPriceHouseKeeping = async (options) => {
  return await fetchAPI('v5/pricing-th/housekeeping', options, 'post', false);
};

export const getPriceChildCare = async (options) => {
  return await fetchAPI('v5/pricing-th/child-care', options, 'post', false);
};

export const getPriceWashingMachine = async (options) => {
  return await fetchAPI('v5/pricing-th/washing-machine', options, 'post', false);
};

export const pricingTaskDateOptions = async (options: IParamPricingTaskDateOptions) => {
  return await fetchAPI('v5/pricing-th/task-date-options', options, 'post', false);
};

export const pricingLaundry = async (options) => {
  return await fetchAPI('v5/pricing-vn/laundry', options, 'post', false);
};

export const getPriceElderlyCare = async (options) => {
  return await fetchAPI('v5/pricing-th/elderly-care', options, 'post', false);
};

export const getPricePatientCare = async (options) => {
  return await fetchAPI('v5/pricing-th/patient-care', options, 'post', false);
};

export const getPriceWaterHeater = async (options) => {
  return await fetchAPI('v5/pricing-th/water-heater', options, 'post', false);
};

export const getPriceIndustrialCleaning = async (options) => {
  return await fetchAPI('v5/pricing-th/industrial-cleaning', options, 'post', false);
};

export const getPriceMakeupAPI = async (options) => {
  return await fetchAPI('v5/pricing-th/makeup', options);
};

export const getPriceHairStylingAPI = async (options) => {
  return await fetchAPI('v5/pricing-th/hair-styling', options);
};

export const getPriceOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v5/pricing-th/carpet-cleaning', options, 'post', false);
};

export const getPriceHomeCooking = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-th/home-cooking', options, 'post', false);
};

export const getPriceGroceryAssistant = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-th/grocery-assistant', options, 'post', false);
};

export const getPriceIroning = async (options) => {
  return await fetchAPI('v5/pricing-th/ironing', options, 'post', false);
};

export const getPriceNailAPI = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-th/nail', options);
};
