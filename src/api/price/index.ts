import { isEmpty } from 'lodash';

import { ID, MY, SERVICES, TH, VN } from '@config';
import { getIsoCodeGlobal, IRespond } from '@helper';
import { IPricingDetail } from '@models/pricing/detail.model';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import { IParamPricingTaskDateOptions, IParamsPricing, IParamsPricingBeautyCare } from './type';
import * as VN_API from './vn';

/**
 * @param task {{object}}
 * @param service {{object}}
 * @example
 * task :
 *  date: moment(task.date).utc().format(),
  duration: task.duration,
  autoChooseTasker: Boolean(task.autoChooseTasker),
  taskPlace: task.taskPlace,
  taskPlace: task.taskPlace,
  homeType: task.homeType,
  taskId = task._id,
  promotion = { code: task.promotion.code };
  requirements = [{ type: 1}];
  detail = task.detail;
  service :
  {_id: ''}
 */
export const getPriceCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceCleaning,
    TH: TH_API.getPriceCleaning,
    ID: ID_API.getPriceCleaning,
    MY: MY_API.getPriceCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceAirConditioner = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceAirConditioner,
    TH: TH_API.getPriceAirConditioner,
    ID: ID_API.getPriceAirConditioner,
    MY: MY_API.getPriceAirConditioner,
  };
  return await combine[global.isoCode](options);
};

export const getPriceDeepCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceDeepCleaning,
    TH: TH_API.getPriceDeepCleaning,
    ID: ID_API.getPriceDeepCleaning,
    MY: MY_API.getPriceDeepCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceOfficeCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getPriceOfficeCleaning,
    TH: TH_API.getPriceOfficeCleaning,
    ID: ID_API.getPriceOfficeCleaning,
    MY: MY_API.getPriceOfficeCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceOfficeCarpetCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getPriceOfficeCarpetCleaning,
    TH: TH_API.getPriceOfficeCarpetCleaning,
    ID: ID_API.getPriceOfficeCarpetCleaning,
    MY: MY_API.getPriceOfficeCarpetCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceHomeCooking = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceHomeCooking,
    TH: TH_API.getPriceHomeCooking,
    ID: ID_API.getPriceHomeCooking,
    MY: MY_API.getPriceHomeCooking,
  };
  return await combine[global.isoCode](options);
};

export const getPriceLaundry = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  // new data
  const combine = {
    VN: VN_API.pricingLaundry,
    TH: TH_API.pricingLaundry,
    ID: ID_API.pricingLaundry,
    MY: MY_API.pricingLaundry,
  };
  return await combine[global.isoCode](options);
};

export const getPriceGroceryAssistant = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceGroceryAssistant,
    TH: TH_API.getPriceGroceryAssistant,
    ID: ID_API.getPriceGroceryAssistant,
    MY: MY_API.getPriceGroceryAssistant,
  };
  return await combine[global.isoCode](options);
};

export const getPriceSofa = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };

  const combine = {
    VN: VN_API.getPriceSofaCleaning,
    TH: TH_API.getPriceSofaCleaning,
    ID: ID_API.getPriceSofaCleaning,
    MY: MY_API.getPriceSofaCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceHouseKeeping = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceHouseKeeping,
    TH: TH_API.getPriceHouseKeeping,
    ID: ID_API.getPriceHousekeeping,
    MY: MY_API.getPriceHousekeeping,
  };
  return await combine[global.isoCode](options);
};

export const getPriceElderlyCare = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceElderlyCare,
    TH: TH_API.getPriceElderlyCare,
    ID: ID_API.getPriceElderlyCare,
    MY: MY_API.getPriceElderlyCare,
  };
  return await combine[global.isoCode](options);
};

export const getPricePatientCare = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPricePatientCare,
    TH: TH_API.getPricePatientCare,
    ID: ID_API.getPricePatientCare,
    MY: MY_API.getPricePatientCare,
  };
  return await combine[global.isoCode](options);
};

export const getPriceDisinfection = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceDisinfection,
    TH: TH_API.getPriceDisinfection,
    ID: ID_API.getPriceDisinfection,
    MY: MY_API.getPriceDisinfection,
  };
  return await combine[global.isoCode](options);
};

export const getPriceChildCare = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceChildCare,
    TH: TH_API.getPriceChildCare,
    ID: ID_API.getPriceChildCare,
    MY: MY_API.getPriceChildCare,
  };
  return await combine[global.isoCode](options);
};

export const getPriceWashingMachine = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceWashingMachine,
    TH: TH_API.getPriceWashingMachine,
    ID: ID_API.getPriceWashingMachine,
    MY: MY_API.getPriceWashingMachine,
  };
  return await combine[global.isoCode](options);
};

export const getPriceWaterHeater = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceWaterHeater,
    TH: TH_API.getPriceWaterHeater,
    ID: ID_API.getPriceWaterHeater,
    MY: MY_API.getPriceWaterHeater,
  };
  return await combine[global.isoCode](options);
};

export const getPriceHomeMoving = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceHomeMoving,
    TH: TH_API.getPriceHomeMoving,
    ID: ID_API.getPriceHomeMoving,
    MY: MY_API.getPriceHomeMoving,
  };
  return await combine[global.isoCode](options);
};

export const getPriceMassage = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: null,
    TH: TH_API.getPriceMassage,
    ID: ID_API.getPriceMassage,
    MY: MY_API.getPriceMassage,
  };
  return await combine[global.isoCode](options);
};

export const getPriceIndustrialCleaning = async (data) => {
  const isoCode = getIsoCodeGlobal();
  const options = {
    ...data,
    isoCode,
  };
  const combine = {
    VN: VN_API.getPriceIndustrialCleaning,
    TH: TH_API.getPriceIndustrialCleaning,
    ID: ID_API.getPriceIndustrialCleaning,
  };
  return await combine[isoCode](options);
};

export const getPriceMakeup = async (...args: [IParamsPricingBeautyCare]): Promise<IRespond<IPricingDetail>> => {
  const apis = {
    [VN]: VN_API.getPriceMakeupAPI,
    [TH]: TH_API.getPriceMakeupAPI,
    [ID]: ID_API.getPriceMakeupAPI,
    [MY]: MY_API.getPriceMakeupAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const getPriceHairStyling = async (...args: [IParamsPricingBeautyCare]): Promise<IRespond<IPricingDetail>> => {
  const apis = {
    [VN]: VN_API.getPriceHairStylingAPI,
    [TH]: TH_API.getPriceHairStylingAPI,
    [ID]: ID_API.getPriceHairStylingAPI,
    [MY]: MY_API.getPriceHairStylingAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const getPriceNail = async (...args: [IParamsPricingBeautyCare]): Promise<IRespond<IPricingDetail>> => {
  const apis = {
    [VN]: VN_API.getPriceNailAPI,
    [TH]: TH_API.getPriceNailAPI,
    [ID]: ID_API.getPriceNailAPI,
    [MY]: MY_API.getPriceNailAPI,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

/**
 * @param data {{object}} data of task
 * @param serviceName {{string}} name of service
 */
export const getPriceProvider = async (data, serviceName) => {
  if (!isEmpty(data?.task?.dateOptions)) {
    return await pricingTaskDateOptions(data);
  }
  switch (serviceName) {
    case SERVICES.CLEANING:
      return await getPriceCleaning(data);
    case SERVICES.CLEANING_SUBSCRIPTION:
      return await getPriceCleaning(data);
    case SERVICES.AIR_CONDITIONER:
      return await getPriceAirConditioner(data);
    case SERVICES.DEEP_CLEANING:
      return await getPriceDeepCleaning(data);
    case SERVICES.HOME_COOKING:
      return await getPriceHomeCooking(data);
    case SERVICES.LAUNDRY:
      return await getPriceLaundry(data);
    case SERVICES.HOUSE_KEEPING:
      return await getPriceHouseKeeping(data);
    case SERVICES.SOFA:
      return await getPriceSofa(data);
    case SERVICES.GROCERY_ASSISTANT:
      return await getPriceGroceryAssistant(data);
    case SERVICES.ELDERLY_CARE:
      return await getPriceElderlyCare(data);
    case SERVICES.ELDERLY_CARE_SUBSCRIPTION:
      return await getPriceElderlyCare(data);
    case SERVICES.PATIENT_CARE:
      return await getPricePatientCare(data);
    case SERVICES.PATIENT_CARE_SUBSCRIPTION:
      return await getPricePatientCare(data);
    case SERVICES.DISINFECTION_SERVICE:
      return await getPriceDisinfection(data);
    case SERVICES.CHILD_CARE:
      return await getPriceChildCare(data);
    case SERVICES.CHILD_CARE_SUBSCRIPTION:
      return await getPriceChildCare(data);
    case SERVICES.OFFICE_CLEANING:
      return await getPriceOfficeCleaning(data);
    case SERVICES.OFFICE_CLEANING_SUBSCRIPTION:
      return await getPriceOfficeCleaning(data);
    case SERVICES.WASHING_MACHINE:
      return await getPriceWashingMachine(data);
    case SERVICES.WATER_HEATER:
      return await getPriceWaterHeater(data);
    case SERVICES.OFFICE_CARPET_CLEANING:
      return await getPriceOfficeCarpetCleaning(data);
    case SERVICES.HOME_MOVING:
      return await getPriceHomeMoving(data);
    case SERVICES.MASSAGE:
      return await getPriceMassage(data);
    case SERVICES.INDUSTRIAL_CLEANING:
      return await getPriceIndustrialCleaning(data);
    case SERVICES.IRONING:
      return await getPriceIroning(data);
    case SERVICES.MAKEUP:
      return await getPriceMakeup(data);
    case SERVICES.HAIR_STYLING:
      return await getPriceHairStyling(data);
    case SERVICES.NAIL:
      return await getPriceNail(data);
    default:
      break;
  }
};

export const pricingTaskDateOptions = (...args: [IParamPricingTaskDateOptions]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.pricingTaskDateOptions,
    [TH]: TH_API.pricingTaskDateOptions,
    [ID]: ID_API.pricingTaskDateOptions,
    [MY]: MY_API.pricingTaskDateOptions,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(...args);
};

export const getPriceIroning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceIroning,
    TH: TH_API.getPriceIroning,
    ID: ID_API.getPriceIroning,
  };
  return await combine[global.isoCode](options);
};
