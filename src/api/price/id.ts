import { fetchAPI } from '@helper';

import { IParamPricingTaskDateOptions, IParamsPricingBeautyCare } from './type';

/**
 * @param task {{object}}
 * @param service {{object}}
 * @example
 * task :
 *  date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: <PERSON><PERSON><PERSON>(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    taskId = task._id,
    promotion = { code: task.promotion.code };
    requirements = [{ type: 1}];
    detail = task.detail;
    service :
    {_id: ''}
 */
export const getPriceCleaning = async (options) => {
  return await fetchAPI('v5/pricing-indo/home-cleaning', options, 'post', false);
};

export const getPriceAirConditioner = async (options) => {
  return await fetchAPI('v5/pricing-indo/air-conditioner-v2', options, 'post', false);
};

export const getPriceDeepCleaning = async (options) => {
  return await fetchAPI('v5/pricing-indo/deep-cleaning', options, 'post', false);
};

export const getPriceOfficeCleaning = async (options) => {
  return await fetchAPI('v5/pricing-indo/office-cleaning', options, 'post', false);
};

export const getPriceWashingMachine = async (options) => {
  return await fetchAPI('v5/pricing-indo/washing-machine', options, 'post', false);
};

export const getPriceWaterHeater = async (options) => {
  return await fetchAPI('v5/pricing-indo/water-heater', options, 'post', false);
};

export const getPriceOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v5/pricing-indo/carpet-cleaning', options, 'post', false);
};

export const getPriceSofaCleaning = async (options) => {
  return await fetchAPI('v5/pricing-indo/sofa', options, 'post', false);
};

export const getPriceDisinfection = async (options) => {
  return await fetchAPI('v5/pricing-indo/disinfection', options, 'post', false);
};

export const getPriceMassage = async (options) => {
  return await fetchAPI('v5/pricing-indo/massage', options, 'post', false);
};

export const getPriceHomeMoving = async (options) => {
  return await fetchAPI('v5/pricing-indo/home-moving', options, 'post', false);
};

export const getPriceChildCare = async (options) => {
  return await fetchAPI('v5/pricing-indo/child-care', options, 'post', false);
};

export const pricingTaskDateOptions = async (options: IParamPricingTaskDateOptions) => {
  return await fetchAPI('v5/pricing-indo/task-date-options', options, 'post', false);
};

export const pricingLaundry = async (options) => {
  return await fetchAPI('v5/pricing-indo/laundry', options, 'post', false);
};

export const getPriceHousekeeping = async (options) => {
  return await fetchAPI('v5/pricing-indo/housekeeping-v2', options, 'post', false);
};

export const getPriceElderlyCare = async (options) => {
  return await fetchAPI('v5/pricing-indo/elderly-care', options, 'post', false);
};

export const getPricePatientCare = async (options) => {
  return await fetchAPI('v5/pricing-indo/patient-care', options, 'post', false);
};

export const getPriceIndustrialCleaning = async (options) => {
  return await fetchAPI('v5/pricing-indo/industrial-cleaning', options, 'post', false);
};

export const getPriceMakeupAPI = async (options) => {
  return await fetchAPI('v5/pricing-indo/makeup', options);
};

export const getPriceHairStylingAPI = async (options) => {
  return await fetchAPI('v5/pricing-indo/hair-styling', options);
};

export const getPriceHomeCooking = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-indo/home-cooking', options, 'post', false);
};

export const getPriceGroceryAssistant = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-indo/grocery-assistant', options, 'post', false);
};

export const getPriceIroning = async (options) => {
  return await fetchAPI('v5/pricing-indo/ironing', options, 'post', false);
};

export const getPriceNailAPI = async (options: IParamsPricingBeautyCare) => {
  return await fetchAPI('v5/pricing-indo/nail', options);
};
