import { ID, MY, TH, VN } from '@config';
import { getIsoCodeGlobal, IRespond } from '@helper';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import {
  IParamsActiveTaskScheduleByTaskId,
  IParamsCancelTaskSchedule,
  IParamsGetScheduleDetailById,
  IParamsUpdateTaskSchedule,
  IParamsUpdateTaskScheduleTime,
} from './type';
import * as VN_API from './vn';

export const getTaskSchedule = (): Promise<IRespond<any>> => {
  const options = {
    isoCode: getIsoCodeGlobal(),
  };
  const apis = {
    [VN]: VN_API.getTaskSchedule,
    [TH]: TH_API.getTaskSchedule,
    [ID]: ID_API.getTaskSchedule,
    [MY]: MY_API.getTaskSchedule,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(options);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const updateTaskSchedule = (...args: [IParamsUpdateTaskSchedule]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.updateTaskSchedule,
    [TH]: TH_API.updateTaskSchedule,
    [ID]: ID_API.updateTaskSchedule,
    [MY]: MY_API.updateTaskSchedule,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};

/**
 * @param userId
 * @param scheduleId
 */
export const cancelTaskSchedule = (...args: [IParamsCancelTaskSchedule]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.cancelTaskSchedule,
    [TH]: TH_API.cancelTaskSchedule,
    [ID]: ID_API.cancelTaskSchedule,
    [MY]: MY_API.cancelTaskSchedule,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};

/**
 * @param userId
 * @param scheduleId
 * @param weekday
 * @param scheduleTime
 * @param scheduleDuration
 */
export const updateTaskScheduleTime = (...args: [IParamsUpdateTaskScheduleTime]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.updateTaskScheduleTime,
    [TH]: TH_API.updateTaskScheduleTime,
    [ID]: ID_API.updateTaskScheduleTime,
    [MY]: MY_API.updateTaskScheduleTime,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const activeTaskScheduleByTaskId = (...args: [IParamsActiveTaskScheduleByTaskId]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.activeTaskScheduleByTaskId,
    [TH]: TH_API.activeTaskScheduleByTaskId,
    [ID]: ID_API.activeTaskScheduleByTaskId,
    [MY]: MY_API.activeTaskScheduleByTaskId,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const getScheduleDetailById = (...args: [IParamsGetScheduleDetailById]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.getScheduleDetailById,
    [TH]: TH_API.getScheduleDetailById,
    [ID]: ID_API.getScheduleDetailById,
    [MY]: MY_API.getScheduleDetailById,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};
