import { fetchAPI } from '@helper';

import {
  IParamsActiveTaskScheduleByTaskId,
  IParamsCancelTaskSchedule,
  IParamsGetScheduleDetailById,
  IParamsGetTaskSchedule,
  IParamsUpdateTaskSchedule,
  IParamsUpdateTaskScheduleTime,
} from './type';

export const getTaskSchedule = async (options: IParamsGetTaskSchedule) => {
  return await fetchAPI('v5/api-asker-my/get-schedule-tasks', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const updateTaskSchedule = async (options: IParamsUpdateTaskSchedule) => {
  return await fetchAPI('v5/api-asker-my/active-task-schedule', options);
};

/**
 * @param userId
 * @param scheduleId
 */
export const cancelTaskSchedule = async (options: IParamsCancelTaskSchedule) => {
  return await fetchAPI('v5/api-asker-my/remove-task-schedule', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param weekday
 * @param scheduleTime
 * @param scheduleDuration
 */
export const updateTaskScheduleTime = async (options: IParamsUpdateTaskScheduleTime) => {
  return await fetchAPI('v5/api-asker-my/update-schedule-time', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const activeTaskScheduleByTaskId = async (options: IParamsActiveTaskScheduleByTaskId) => {
  return await fetchAPI('v5/api-asker-my/active-task-schedule-by-task-id', options);
};

/**
 * @param scheduleId
 */
export const getScheduleDetailById = async (options: IParamsGetScheduleDetailById) => {
  return await fetchAPI('v5/api-asker-my/get-task-schedule-detail', options);
};
