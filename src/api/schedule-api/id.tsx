import { fetchAPI } from '@helper';

import {
  IParamsActiveTaskScheduleByTaskId,
  IParamsCancelTaskSchedule,
  IParamsGetScheduleDetailById,
  IParamsGetTaskSchedule,
  IParamsUpdateTaskSchedule,
  IParamsUpdateTaskScheduleTime,
} from './type';

export const getTaskSchedule = async (options: IParamsGetTaskSchedule) => {
  return await fetchAPI('v5/api-asker-indo/get-schedule-tasks', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const updateTaskSchedule = async (options: IParamsUpdateTaskSchedule) => {
  return await fetchAPI('v5/api-asker-indo/active-task-schedule', options);
};

/**
 * @param userId
 * @param scheduleId
 */
export const cancelTaskSchedule = async (options: IParamsCancelTaskSchedule) => {
  return await fetchAPI('v5/api-asker-indo/remove-task-schedule', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param weekday
 * @param scheduleTime
 * @param scheduleDuration
 */
export const updateTaskScheduleTime = async (options: IParamsUpdateTaskScheduleTime) => {
  return await fetchAPI('v5/api-asker-indo/update-schedule-time', options);
};

/**
 * @param userId
 * @param scheduleId
 * @param isActive
 */
export const activeTaskScheduleByTaskId = async (options: IParamsActiveTaskScheduleByTaskId) => {
  return await fetchAPI('v5/api-asker-indo/active-task-schedule-by-task-id', options);
};

/**
 * @param scheduleId
 */
export const getScheduleDetailById = async (options: IParamsGetScheduleDetailById) => {
  return await fetchAPI('v5/api-asker-indo/get-task-schedule-detail', options);
};
