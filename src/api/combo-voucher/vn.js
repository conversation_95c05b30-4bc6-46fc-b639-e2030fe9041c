import { fetchAPI } from '@helper';
/**
 * @param userId (optional)
 */
export const getComboVoucherAPI = async () => {
  return await fetchAPI('v5/api-asker-vn/get-list-combo-voucher');
};
/**
 * @param userId (optional)
 * @param comboVoucherId (required)
 */
export const getComboVoucherDetailAPI = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-combo-voucher-detail', options);
};

export const payComboVoucherAPI = async (options) => {
  return await fetchAPI('v5/payment-vn/pay-combo-voucher-vn', options);
};

/**
 * options: {userId: "xx", comboVoucherId: "xx"}
 * @param {*} options
 * @returns
 *
 */
export const getFreeComboVoucherAPI = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-free-combo-voucher', options);
};
