import { getIPAddress } from '@helper';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

/**
 * @param userId (optional)
 */
export const getComboVoucher = async () => {
  const combine = {
    VN: VN_API.getComboVoucherAPI,
    TH: TH_API.getComboVoucherAPI,
    ID: ID_API.getComboVoucherAPI,
    MY: MY_API.getComboVoucherAPI,
  };
  return await combine[global.isoCode]();
};
/**
 * @param userId (optional)
 * @param comboVoucherId (required)
 */
export const getComboVoucherDetail = async (comboVoucherId) => {
  const options = {
    comboVoucherId: comboVoucherId,
  };
  const combine = {
    VN: VN_API.getComboVoucherDetailAPI,
    TH: TH_API.getComboVoucherDetailAPI,
    ID: ID_API.getComboVoucherDetailAPI,
    MY: MY_API.getComboVoucherDetailAPI,
  };
  return await combine[global.isoCode](options);
};
/**
 * @param options (required)
 * "userId": "0834567890",
    "payment": {
        "method": "CREDIT"
    },
    "comboVoucherId": "x63f5d657d4102a1527c86ed5",
    shopperIP: "xxx"
 */
export const payComboVoucher = async (options) => {
  options.shopperIP = await getIPAddress();
  const combine = {
    VN: VN_API.payComboVoucherAPI,
    TH: TH_API.payComboVoucherAPI,
    ID: ID_API.payComboVoucherAPI,
    MY: MY_API.payComboVoucherAPI,
  };
  return await combine[global.isoCode](options);
};

export const getFreeComboVoucher = async (comboVoucherId) => {
  const options = {
    comboVoucherId: comboVoucherId,
  };
  const combine = {
    VN: VN_API.getFreeComboVoucherAPI,
    TH: TH_API.getFreeComboVoucherAPI,
    ID: ID_API.getFreeComboVoucherAPI,
    MY: MY_API.getFreeComboVoucherAPI,
  };
  return await combine[global.isoCode](options);
};
