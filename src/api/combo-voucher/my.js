import { fetchAPI } from '@helper';
/**
 * @param userId (optional)
 */
export const getComboVoucherAPI = async () => {
  return await fetchAPI('v5/api-asker-my/get-list-combo-voucher');
};
/**
 * @param userId (optional)
 * @param comboVoucherId (required)
 */
export const getComboVoucherDetailAPI = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-combo-voucher-detail', options);
};
/**
 * @param options (required)
 * "userId": "0834567890",
    "payment": {
        "method": "CREDIT"
    },
    "comboVoucherId": "x63f5d657d4102a1527c86ed5"
 */
export const payComboVoucherAPI = async (options) => {
  return await fetchAPI('v5/payment-my/pay-combo-voucher', options);
};

/**
 * options: {userId: "xx", comboVoucherId: "xx"}
 * @param {*} options
 * @returns
 *
 */
export const getFreeComboVoucherAPI = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-free-combo-voucher', options);
};
