import { getVersionAppName } from '@helper';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export const register = async (dataRegister: object) => {
  const options = {
    // isMockAPI: true,
    ...dataRegister,
    appVersion: getVersionAppName(),
  };
  const combine = {
    VN: VN_API.register,
    TH: TH_API.register,
    ID: ID_API.register,
    MY: MY_API.register,
  };
  return await combine[global.isoCode](options);
};

export const checkReferralCode = async (referralCode: string) => {
  const options = {
    code: referralCode,
  };
  const combine = {
    VN: VN_API.checkReferralCode,
    TH: TH_API.checkReferralCode,
    ID: ID_API.checkReferralCode,
    MY: MY_API.checkReferralCode,
  };
  return await combine[global.isoCode](options);
};
