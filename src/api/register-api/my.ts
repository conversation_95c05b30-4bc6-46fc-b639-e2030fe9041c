import { fetchAPI } from '@helper';
/**
 * @param name
 * @param type
 * @param email
 * @param phone
 * @param isoCode
 * @param language
 * @param userName
 * @param countryCode
 * @param avatar (not required)
 * @param friendCode (not required)
 * @example
 * params: {
 *      userName: '84939504182', name: 'tom', phone: '0939504182', countryCode: '+84',
 *      email: '<EMAIL>', friendCode: 'TomACB', avatar: '', type: 'TASKER'
 *      }
 * loginType: 'facebook'
 */

export const register = async (dataRegister: object) => {
  const options = {
    // isMockAPI: true,
    ...dataRegister,
  };
  return await fetchAPI('v5/user-asker-my/sign-up-customer', options);
};

/**
 * @param taskId String
 */
export const checkReferralCode = async (options: object) => {
  return await fetchAPI('v5/promotion-my/check', options);
};
