import { fetchAPI } from '@helper';

export const checkPromotion = async (options) => {
  return await fetchAPI('v5/promotion-vn/post-task', options, 'post', false);
};

export const getPromotionByService = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-gift-v2', options);
};

export const getRewardsByTask = async (options) => {
  return await fetchAPI('v5/api-asker-vn/get-rewards-for-book-task', options);
};
