import { isEmpty } from 'lodash';

import moment from '@moment';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import { ICheckPromotionParams } from './type';
import * as VN_API from './vn';

/**
 * @param data {{object}}
 * "code": "uns6q0",
	"askerId": "aKR5nCB32BLvC4uD7",
	"serviceId": "pcZRQ6PqmjrAPe5gt",
 */
export const checkPromotion = async ({
  code,
  serviceId,
  askerId,
  taskDate,
  finalCost,
  taskPlace,
  paymentMethod,
  cost,
}: ICheckPromotionParams) => {
  const options = {
    promotion: {
      code: code,
    },
    askerId,
    serviceId,
    isoCode: global.isoCode,
  };
  let costDetail = {};

  if (taskDate) {
    options.date = moment(taskDate).utc().format();
  }
  if (taskPlace) {
    options.taskPlace = taskPlace;
  }
  if (finalCost) {
    costDetail = { ...costDetail, finalCost: finalCost };
  }
  if (cost) {
    costDetail = { ...costDetail, cost: cost };
  }
  if (!isEmpty(costDetail)) {
    options.costDetail = costDetail;
  }
  if (paymentMethod) {
    options.payment = {
      method: paymentMethod,
    };
  }
  const combine = {
    VN: VN_API.checkPromotion,
    TH: TH_API.checkPromotion,
    ID: ID_API.checkPromotion,
    MY: MY_API.checkPromotion,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param serviceId {{string}}
 *
 */
export const getPromotionByService = async ({ serviceId, taskDate, taskCost, taskPlace, paymentMethod }) => {
  const options = {
    // isMockAPI: true,
    serviceId,
    taskDate,
    taskCost,
    isoCode: global.isoCode,
    taskPlace,
  };

  if (paymentMethod) {
    options.paymentMethod = paymentMethod;
  }

  const combine = {
    VN: VN_API.getPromotionByService,
    TH: TH_API.getPromotionByService,
    ID: ID_API.getPromotionByService,
    MY: MY_API.getPromotionByService,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param serviceId {{string}}
 *
 */
export const getRewardsByTask = async (serviceId) => {
  const options = {
    // isMockAPI: true,
    serviceId,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getRewardsByTask,
    TH: TH_API.getRewardsByTask,
    ID: ID_API.getRewardsByTask,
    MY: MY_API.getRewardsByTask,
  };
  return await combine[global.isoCode](options);
};
