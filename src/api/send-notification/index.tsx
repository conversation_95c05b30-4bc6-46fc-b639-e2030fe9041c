import { ID, MY, TH, VN } from '@config';
import { IObjectText } from '@src/lib/types';

import apiID from './id';
import apiMY from './my';
import apiTH from './th';
import apiVN from './vn';

export interface IParamsSendNotify {
  userIds: { userId: string; language: string }[];
  title: IObjectText;
  body: IObjectText;
  icon?: string;
  sound?: string;
  payload?: any;
}

const apis = new Map([
  [VN, apiVN],
  [TH, apiTH],
  [ID, apiID],
  [MY, apiMY],
]);

const combine = (args: IParamsSendNotify) => {
  const api = apis.get(global.isoCode);
  return api(args);
};

export default combine;
