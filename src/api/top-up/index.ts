import { Omit } from 'react-native';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import { ITopUpParams } from './types';
import * as VN_API from './vn';

/**
 * @description top up bPay
 * **/
export const bPayTopUpApi = async (topUpData: Omit<ITopUpParams, 'isoCode' | 'shopperIP'>) => {
  const options = {
    isoCode: global.isoCode,
    ...topUpData,
  };
  const combine = {
    VN: VN_API.bPayTopUp,
    TH: TH_API.bPayTopUp,
    ID: ID_API.bPayTopUp,
    MY: MY_API.bPayTopUp,
  };
  return await combine[global.isoCode](options);
};
