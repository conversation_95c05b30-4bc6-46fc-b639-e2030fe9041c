import { fetchAPI } from '@helper';

export const getPaymentCardListAPI = async (options) => {
  return await fetchAPI('v5/api-asker-th/get-list-payment', options);
};

export const removeCardPayment = async (options) => {
  return await fetchAPI('v5/payment/disable-card-th', options);
};

export const setPaymentCardDefault = async (options) => {
  return await fetchAPI('v5/api-asker-th/set-payment-card-default', options);
};
