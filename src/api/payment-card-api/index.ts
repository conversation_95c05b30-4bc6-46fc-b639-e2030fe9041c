import { fetchAPI } from '@helper';

import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export const getPaymentCardListAPI = async () => {
  const options = {
    isoCode: global.isoCode,
    // isMockAPI: true
  };
  const combine = {
    VN: VN_API.getPaymentCardListAPI,
    TH: TH_API.getPaymentCardListAPI,
    ID: ID_API.getPaymentCardListAPI,
    MY: MY_API.getPaymentCardListAPI,
  };
  return await combine[global.isoCode](options);
};

export const setPaymentCardDefault = async (cardId, isDefault = false) => {
  const options = { cardId: cardId, isDefault: isDefault };
  const combine = {
    VN: VN_API.setPaymentCardDefault,
    TH: TH_API.setPaymentCardDefault,
    ID: ID_API.setPaymentCardDefault,
  };
  return await combine[global.isoCode](options);
};

export const addCard = async (cardInfo) => {
  return await fetchAPI('v5/payment/integrate-card', cardInfo, 'post');
};

export const fetchChallengeShopper = async (data) => {
  return await fetchAPI('v5/payment/adyen-get-challenge-shopper', data, 'post');
};

export const removePaymentCard = async (options: { cardId: string }) => {
  const combine = {
    VN: VN_API.removeCardPayment,
    TH: TH_API.removeCardPayment,
    ID: ID_API.removeCardPayment,
    MY: MY_API.removeCardPayment,
  };
  return await combine[global.isoCode](options);
};

// For Thai Lan
export const payment3DSForThaiLan = async (data) => {
  return await fetchAPI('v5/payment/integrate-card-2c2p', data, 'post', false);
};

export const addCardForThaiLan = async (data) => {
  return await fetchAPI('v5/payment/add-card-2c2p', data, 'post');
};

export const addCardPaymentAPI = async (options: ID_API.ICardPaymentIDParams) => {
  const combine = {
    ID: ID_API.addCardPayment,
    MY: MY_API.addCardPayment,
  };
  return await combine[global.isoCode](options);
};
