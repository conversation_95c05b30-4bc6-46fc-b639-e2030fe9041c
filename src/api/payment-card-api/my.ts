import { ISO_CODE } from '@config';
import { fetchAPI, getIPAddress } from '@helper';

export interface ICardPaymentIDParams {
  userId: string;
  tokenId: string;
  cardNumber: string;
  holderName: string;
  expiryMonth: string;
  expiryYear: string;
  isoCode?: ISO_CODE;
  shopperIP?: any;
}

export const getPaymentCardListAPI = async (options) => {
  return await fetchAPI('v5/api-asker-my/get-list-payment', options);
};

export const addCardPayment = async (options: ICardPaymentIDParams) => {
  options.shopperIP = await getIPAddress();
  options.isoCode = ISO_CODE.ID;
  return await fetchAPI('v5/payment-my/integrate-card', options);
};

export const removeCardPayment = async (options) => {
  return await fetchAPI('v5/payment-my/disable-card', options);
};
