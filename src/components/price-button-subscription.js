import React, { useMemo } from 'react';
import { ActivityIndicator, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { getBottomSpace, ifIphoneX } from 'react-native-iphone-x-helper';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import constant from '@constant';
import { LocalizationContext } from '@context';
import { formatMoney, getCurrency, getIncreasePiceTet } from '@helper';

import BlockView from './block-view';
import ConditionView from './condition-view';
import CText from './custom-text';
import AddressDetail from './price-button-address';

const NextButton = ({
  isLoading,
  isUseAnimation,
  price,
  onPress,
  nextButtonContainer,
  isShowAddress,
  testID,
  ...props
}) => {
  const I18n = React.useContext(LocalizationContext);

  const shouldRenderPrice = React.useMemo(() => {
    if (!price) {
      return null;
    }
    // if data task not change, get price from task
    let priceText = '';
    let originPriceText = '';
    const disabledButton = Boolean(isLoading || (price && price.error));

    const isChangeBackground = Boolean(price && price.error);

    if (price.finalCost) {
      priceText = `${I18n.t('COST_AND_CURRENCY', {
        cost: formatMoney(price.finalCost),
        currency: getCurrency(price, 1),
      })}`;
    }

    // with promotion
    if (price && price.cost > price.finalCost) {
      originPriceText = `${I18n.t('COST_AND_CURRENCY', {
        cost: formatMoney(price.cost),
        currency: getCurrency(price, 1),
      })}`;
    }

    const handleClick = () => {
      onPress && onPress();
    };

    const WapperComponent = isUseAnimation ? Animatable.View : BlockView;

    const titleButton = I18n.t('NEXT');

    const _renderIncreaseText = () => {
      const money = getIncreasePiceTet(price?.pricing);
      const increasePrice = `${I18n.t('COST_AND_CURRENCY', {
        cost: formatMoney(money),
        currency: getCurrency(price, 2),
      })}`;
      const txtIncreasePrice = money > 0 ? I18n.t('SUBSCRIPTION_INCREASE_PRICE', { t: increasePrice }) : '';

      const increaseText = `${I18n.t('SUBSCRIPTION_PRICE_INCREASEMENT', {
        count: props.numberIncreasement,
      })} ${txtIncreasePrice}`;

      return (
        <BlockView flex align="center" padding={5}>
          <CText bold color={constant.PRIMARY_COLOR}>
            {increaseText}
          </CText>
        </BlockView>
      );
    };
    const textSession = `/${I18n.t('SESSION', { t: price.session })}`;

    return (
      <WapperComponent style={[styles.container, nextButtonContainer]} useNativeDriver animation="slideInUp">
        <ConditionView
          condition={Boolean(isShowAddress)}
          viewTrue={
            <AddressDetail
              setAddress={props.setAddress}
              setHomeType={props.setHomeType}
              setHomeNumber={props.setHomeNumber}
              getPrice={props.getPrice}
              setPrice={props.setPrice}
            />
          }
        />
        <ConditionView condition={Boolean(props?.numberIncreasement > 0)} viewTrue={_renderIncreaseText()} />

        <TouchableWithoutFeedback onPress={handleClick} disabled={disabledButton}>
          <BlockView row style={[styles.btn, isChangeBackground && styles.btnDisabled]}>
            <BlockView style={styles.left}>
              <ConditionView
                condition={Boolean(isLoading)}
                viewTrue={
                  <BlockView style={styles.loading}>
                    <ActivityIndicator color={constant.COLOR.white} />
                  </BlockView>
                }
                viewFalse={
                  <Animatable.View useNativeDriver animation="fadeIn" style={styles.containerLoading}>
                    <BlockView>
                      <ConditionView
                        condition={Boolean(originPriceText)}
                        viewTrue={
                          <CText testID="lbOriginPrice" style={styles.txtOriginPrice}>
                            {originPriceText}
                          </CText>
                        }
                      />
                      <BlockView row>
                        <CText testID="lbPrice" bold style={styles.txtPrice}>
                          {priceText ? priceText : `0${getCurrency(price, 1)}`}
                        </CText>
                        <CText testID="numberSessions" bold style={styles.txtPrice}>
                          {textSession}
                        </CText>
                      </BlockView>
                    </BlockView>
                  </Animatable.View>
                }
              />
            </BlockView>
            <BlockView style={styles.right}>
              <CText testID={testID} style={styles.txtNext}>
                {titleButton}
              </CText>
            </BlockView>
          </BlockView>
        </TouchableWithoutFeedback>
      </WapperComponent>
    );
  }, [price, isLoading]);

  return shouldRenderPrice;
};

const styles = StyleSheet.create({
  containerLoading: { flexDirection: 'row', alignItems: 'center' },
  loading: {
    alignItems: 'flex-start',
    paddingLeft: '20%',
  },
  txtOriginPrice: {
    textDecorationLine: 'line-through',
    color: constant.COLOR.white,
  },
  txtNext: {
    color: constant.COLOR.white,
    fontSize: 16,
  },
  txtPrice: {
    color: constant.COLOR.white,
    fontSize: 16,
  },
  container: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    right: 0,
    // borderTopLeftRadius: constant.BORDER_RADIUS,
    // borderTopRightRadius: constant.BORDER_RADIUS,
    backgroundColor: constant.COLOR.white,
    padding: constant.MARGIN.medium,
    shadowColor: '#000000',
    shadowOffset: {
      width: 1,
      height: 5,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,

    ...ifIphoneX({ paddingBottom: getBottomSpace() }, { paddingBottom: constant.MARGIN.medium }),
  },
  btnDisabled: {
    backgroundColor: constant.COLOR.grey,
    shadowColor: constant.COLOR.grey,
  },
  left: {
    flex: 1,
    height: 22,
    justifyContent: 'center',
  },
  btn: {
    ...constant.SHADOW,
    shadowColor: constant.SECONDARY_COLOR,
    backgroundColor: constant.SECONDARY_COLOR,
    borderRadius: constant.BORDER_RADIUS,
    padding: constant.MARGIN.medium,
    alignItems: 'center',
  },
});

const mapStateToProps = (store) => ({
  price: store.subscription.price,
  isLoading: store.subscription.isLoadingPrice,
});

const mapDispatchToProps = (dispatch) => bindActionCreators({}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(NextButton);
