import _, { isEmpty } from 'lodash';

import { prepaymentAPI } from '@api/prepayment';
import { getPriceProvider } from '@api/price';
import { checkPromotion } from '@api/promotion';
import { getPromotionPaymentMethodAPI } from '@api/promotion-payment-method';
import { AlertHolder } from '@component';
import { PAYMENT_METHOD, PREPAYMENT, SERVICES } from '@config';
import { formatDate, handleError, handleErrorCheckPromotion, isLaundryService } from '@helper';
import { DateTimeHelpers } from '@helper/date-time.helpers';
import { RouteName } from '@navigation/route-name';
import I18n from '@src/lib/localization';

import { showLoading } from './app-action';

export const POST_TASK_SET_DURATION = 'POST_TASK/SET_DURATION';
export const POST_TASK_SET_AUTO_CHOOSE_TASKER = 'POST_TASK/SET_AUTO_CHOOSE_TASKER';
export const POST_TASK_SET_PET = 'POST_TASK/SET_PET';
export const POST_TASK_SET_FAV_TASKER = 'POST_TASK/SET_FAV_TASKER';
export const POST_TASK_SET_DATE_TIME = 'POST_TASK/SET_DATE_TIME';
export const POST_TASK_SET_SCHEDULE = 'POST_TASK/SET_SCHEDULE';
export const POST_TASK_ENABLED_SCHEDULE = 'POST_TASK/ENABLED_SCHEDULE';
export const POST_TASK_SET_NOTE = 'POST_TASK/SET_NOTE';
export const POST_TASK_SET_NOTE_FOR_ALL_TASK = 'POST_TASK/SET_NOTE_FOR_ALL_TASK';
export const POST_TASK_SET_ADDRESS = 'POST_TASK/SET_ADDRESS';
export const POST_TASK_SET_HOME_TYPE = 'POST_TASK/SET_HOME_TYPE';
export const POST_TASK_SET_HOME_NUMBER = 'POST_TASK/SET_HOME_NUMBER';
export const POST_TASK_SET_PAYMENT_METHOD = 'POST_TASK/SET_PAYMENT_METHOD';
export const POST_TASK_SET_PROMOTION = 'POST_TASK/SET_SET_PROMOTION';
export const POST_TASK_SET_PRICE = 'POST_TASK/SET_SET_PRICE';
export const POST_TASK_SET_LOADING_PRICE = 'POST_TASK/SET_LOADING_PRICE';
export const POST_TASK_SET_SERVICE = 'POST_TASK/SET_SERVICE';
export const POST_TASK_RESET_STATE_WITHOUT_SERVICE = 'POST_TASK/RESET_STATE_WITHOUT_SERVICE';
export const POST_TASK_RESET_STATE_WITHOUT_SERVICE_AND_DATE = 'POST_TASK_RESET_STATE_WITHOUT_SERVICE_AND_DATE';
export const POST_TASK_RESET_ALL_STATE = 'POST_TASK/RESET_ALL_STATE';
export const POST_TASK_SET_PREVIOUS_SERVICE_ID = 'POST_TASK_SET_PREVIOUS_SERVICE_ID';
export const POST_TASK_SET_PREVIOUS_TET_BOOKING = 'POST_TASK_SET_PREVIOUS_TET_BOOKING';
export const RESET_STATE_TO_POST_TASK = 'RESET_STATE_TO_POST_TASK';
export const POST_TASK_SET_PROMOTION_PAYMENT_METHOD = 'POST_TASK_SET_PROMOTION_PAYMENT_METHOD';
export const POST_TASK_SET_SERVICES_TO_POST_TASK = 'POST_TASK_SET_SERVICES_TO_POST_TASK';
export const POST_TASK_ADD_ON_SERVICE = 'POST_TASK_ADD_ON_SERVICE';
export const POST_TASK_RESET_FULL_STATE = 'POST_TASK/RESET_FULL_STATE';
export const POST_TASK_SET_VAT_INFO = 'POST_TASK/SET_VAT_INFO';
export const POST_TASK_SET_SAVE_VAT_INFO_TO_USER = 'POST_TASK/SET_SAVE_VAT_INFO_TO_USER';
export const POST_TASK_SET_GENDER = 'POST_TASK/POST_TASK_SET_GENDER';
export const POST_TASK_SET_FORCE_TASKER = 'POST_TASK/POST_TASK_SET_FORCE_TASKER';
export const POST_TASK_SET_DATE_OPTIONS = 'POST_TASK/POST_TASK_SET_DATE_OPTIONS';
export const POST_TASK_RESET_FORCE_TASKER = 'POST_TASK/POST_TASK_RESET_FORCE_TASKER';
export const POST_TASK_SET_ADDONS = 'POST_TASK/POST_TASK_SET_ADDONS';
export const POST_TASK_SET_ENTRY_POINT = 'POST_TASK/POST_TASK_SET_ENTRY_POINT';
export const POST_TASK_SET_RELATED_TASK = 'POST_TASK/POST_TASK_SET_RELATED_TASK';
export const POST_TASK_RESET_RELATED_TASK = 'POST_TASK/POST_TASK_RESET_RELATED_TASK';
export const POST_TASK_IS_PREFERRED_PAYMENT_METHOD = 'POST_TASK/SET_IS_PREFERRED_PAYMENT_METHOD';
export const POST_TASK_SET_IS_LOADING_PRICE = 'POST_TASK/POST_TASK_SET_IS_LOADING_PRICE';

export const setLoadingPrice = (payload = true) => ({ type: POST_TASK_SET_LOADING_PRICE, payload });
export const _setPromotion = (payload) => ({ type: POST_TASK_SET_PROMOTION, payload });

export const setDuration = (payload) => ({ type: POST_TASK_SET_DURATION, payload });
export const setAutoChooseTasker = (payload) => ({ type: POST_TASK_SET_AUTO_CHOOSE_TASKER, payload });
export const setPet = (payload) => ({ type: POST_TASK_SET_PET, payload });
export const setFavoriteTasker = (payload) => ({ type: POST_TASK_SET_FAV_TASKER, payload });
export const setForceTasker = (payload) => ({ type: POST_TASK_SET_FORCE_TASKER, payload });
export const resetForceTasker = (payload) => ({ type: POST_TASK_RESET_FORCE_TASKER, payload });
export const setDateOptions = (payload) => ({ type: POST_TASK_SET_DATE_OPTIONS, payload });
export const _setDateTime = (payload) => ({ type: POST_TASK_SET_DATE_TIME, payload });
export const setSchedule = (payload) => ({ type: POST_TASK_SET_SCHEDULE, payload });
export const setEnabledSchedule = (payload = true) => ({ type: POST_TASK_ENABLED_SCHEDULE, payload });
export const setNote = (payload) => ({ type: POST_TASK_SET_NOTE, payload });
export const setNoteForAllTask = (payload = true) => ({ type: POST_TASK_SET_NOTE_FOR_ALL_TASK, payload });
export const setAddress = (payload) => ({ type: POST_TASK_SET_ADDRESS, payload });
export const setHomeType = (payload) => ({ type: POST_TASK_SET_HOME_TYPE, payload });
export const setHomeNumber = (payload) => ({ type: POST_TASK_SET_HOME_NUMBER, payload });
export const setPaymentMethod = (payload) => ({ type: POST_TASK_SET_PAYMENT_METHOD, payload });
export const setService = (payload) => ({ type: POST_TASK_SET_SERVICE, payload });
export const resetStateWithoutService = () => ({ type: POST_TASK_RESET_STATE_WITHOUT_SERVICE });
export const resetAllState = () => ({ type: POST_TASK_RESET_ALL_STATE });
export const resetStateToPostTask = () => ({ type: RESET_STATE_TO_POST_TASK });
export const setPrice = (payload) => ({ type: POST_TASK_SET_PRICE, payload });
export const setPreviousServiceId = (payload) => ({ type: POST_TASK_SET_PREVIOUS_SERVICE_ID, payload });
export const setPreviousTetBooking = (payload) => ({ type: POST_TASK_SET_PREVIOUS_TET_BOOKING, payload });
export const resetStateWithoutServiceAndDate = () => ({ type: POST_TASK_RESET_STATE_WITHOUT_SERVICE_AND_DATE });
export const setAddons = (payload) => ({ type: POST_TASK_SET_ADDONS, payload });
export const setPostTaskEntryPoint = (payload) => ({ type: POST_TASK_SET_ENTRY_POINT, payload });
export const setPostTaskRelatedTask = (payload) => ({ type: POST_TASK_SET_RELATED_TASK, payload });
export const resetPostTaskRelatedTask = (payload) => ({ type: POST_TASK_RESET_RELATED_TASK, payload });
export const setPromotionPaymentMethod = (payload) => ({
  type: POST_TASK_SET_PROMOTION_PAYMENT_METHOD,
  payload: payload,
});
export const setServicesToPostTask = (payload) => ({ type: POST_TASK_SET_SERVICES_TO_POST_TASK, payload: payload });
export const resetFullState = () => ({ type: POST_TASK_RESET_FULL_STATE });

export const setVatInfo = (payload) => ({ type: POST_TASK_SET_VAT_INFO, payload: payload });
export const setSaveVatInfoToUser = (payload) => ({ type: POST_TASK_SET_SAVE_VAT_INFO_TO_USER, payload: payload });

const _addRequirement = (payload) => ({ type: POST_TASK_ADD_ON_SERVICE, payload });
export const setGender = (payload) => ({ type: POST_TASK_SET_GENDER, payload });
export const setIsPreferredPaymentMethod = (payload) => ({ type: POST_TASK_IS_PREFERRED_PAYMENT_METHOD, payload });

/**
 * @description refactor data task before call get Price
 */
const getDataTask = (getState) => {
  // get store data from reducer
  const store = getState();
  const { postTask, app } = store;
  const {
    date,
    isAutoChooseTasker,
    duration,
    service,
    address,
    homeType,
    promotion,
    forceTasker,
    dateOptions,
    addons,
    paymentMethod,
  } = postTask;

  // check base condition
  // service not need check duration
  const servicesNotCheckDuration = [
    SERVICES.LAUNDRY,
    SERVICES.HOUSE_KEEPING,
    SERVICES.GROCERY_ASSISTANT,
    SERVICES.DISINFECTION_SERVICE,
    SERVICES.WASHING_MACHINE,
    SERVICES.WATER_HEATER,
    SERVICES.OFFICE_CARPET_CLEANING,
    SERVICES.MASSAGE,
    SERVICES.INDUSTRIAL_CLEANING,
  ];

  if (!address || !date || (!duration && servicesNotCheckDuration.indexOf(service.name) === -1)) {
    return null;
  }
  const timezone = DateTimeHelpers.getTimezoneByCity(address.city);
  // base info
  const task = {
    timezone,
    date: DateTimeHelpers.formatToString({ timezone, date }),
    autoChooseTasker: isAutoChooseTasker,
    taskPlace: {
      country: address.country,
      city: address.city,
      district: address.district,
    },
    homeType: homeType,
  };

  if (duration) {
    task.duration = duration;
  }
  if (!isEmpty(forceTasker)) {
    task.forceTasker = forceTasker;
  }
  if (dateOptions.length > 1) {
    task.dateOptions = dateOptions;
  }

  if (paymentMethod?.value) {
    task.payment = {
      method: postTask.paymentMethod?.value,
    };
  }

  // with promotion
  if (promotion) {
    task.promotion = {
      code: promotion.code,
    };
  } else if (postTask?.paymentMethod?.promotionPaymentMethodCode) {
    // with promotion PaymentMethod
    task.promotion = {
      promotionPaymentMethodCode: postTask.paymentMethod?.promotionPaymentMethodCode,
    };
  }

  // addOnService
  if (postTask?.addOnService && postTask?.addOnService.length > 0) {
    task.requirements = postTask.addOnService.map((req) => ({ type: req.type })); // send type only
  }

  if (!isEmpty(addons)) {
    task.addons = addons;
  }

  // add data by service

  // add data by service
  switch (service.name) {
    case SERVICES.CLEANING: // with cleaning service
      const addOnService = store?.postTaskCleaning?.addOnService;
      if (addOnService && addOnService.length > 0) {
        task.requirements = addOnService.map((e) => ({ type: e.type }));
      }
      // Check premium service
      if (store?.postTaskCleaning?.isPremium) {
        task.isPremium = true;
      }
      break;
    case SERVICES.AIR_CONDITIONER: // with AIR CONDITIONER service
      const aCServices = store?.postTaskAirConditioner?.selectedAirConditioner;
      if (isEmpty(aCServices)) {
        return null;
      }
      task.detailAirConditioner = [...aCServices];
      break;
    case SERVICES.DEEP_CLEANING: // with DEEP CLEANING service
      const numberOfTasker = store?.postTaskDeepCleaning?.numberOfTasker || 0;
      if (numberOfTasker > 0) {
        task.detailDeepCleaning = { numberOfTaskersDeepCleaning: numberOfTasker };
      } else {
        return null; // not get price
      }
      // Check premium service
      if (store?.postTaskDeepCleaning?.isPremium) {
        task.isPremium = true;
      }
      break;
    case SERVICES.HOME_COOKING: // with DEEP CLEANING service
      task.cookingDetail = {
        numberEater: store?.postTaskHomeCooking?.numberOfPeopleEating,
        numberDish: store?.postTaskHomeCooking?.numberOfDish,
        isGoMarket: Boolean(store?.postTaskHomeCooking?.isGoMarket),
        haveFruit: Boolean(store?.postTaskHomeCooking?.haveFruit),
        eatingTime: DateTimeHelpers.formatToString({ timezone, date }),
      };
      if (dateOptions.length > 1) {
        task.dateOptions = dateOptions.map((dateOption) => ({
          eatingTime: dateOption?.date,
        }));
      }
      if (!task.cookingDetail.numberEater || !task.cookingDetail.numberDish) {
        return null;
      }
      break;
    case SERVICES.LAUNDRY: // with Laundry service
      const { postTaskLaundry } = store;
      const detail = {};
      if (postTaskLaundry.city) {
        detail.city = postTaskLaundry.city;
      }
      if (postTaskLaundry.washing) {
        detail.washing = postTaskLaundry.washing;
      }
      if (postTaskLaundry.dryClean) {
        detail.dryClean = postTaskLaundry.dryClean;
      }

      if (postTaskLaundry.others) {
        detail.others = postTaskLaundry.others;
      }

      if (postTaskLaundry?.otherText?.data) {
        // theo list mới, đã có type, text
        if (postTaskLaundry.others) {
          postTaskLaundry.others.data = postTaskLaundry.otherText.data;
        } else {
          // other chưa được tạo, lấy toàn bộ data của otherText
          detail.others = postTaskLaundry.otherText;
        }
      }

      if (postTaskLaundry.collectionDate) {
        task.collectionDate = DateTimeHelpers.formatToString({ timezone, date: postTaskLaundry.collectionDate });
      }

      // No get price when no data
      if (!detail?.washing && !detail?.dryClean && !detail?.others) {
        return null;
      }
      task.detailLaundry = detail;
      break;
    case SERVICES.HOUSE_KEEPING: // with DEEP CLEANING service
      const { postTaskHouseKeeping } = store;
      const rooms = _.get(postTaskHouseKeeping, 'rooms', []) || [];
      const options = _.get(postTaskHouseKeeping, 'options', []) || [];
      // No get price when no rooms
      if (isEmpty(rooms)) {
        return null;
      }

      let detailHousekeeping = {
        name: postTaskHouseKeeping?.homeType?.name,
        text: postTaskHouseKeeping?.homeType?.text,
        roomTypes: rooms,
      };
      if (!isEmpty(options)) {
        detailHousekeeping = { ...detailHousekeeping, options };
      }
      if (postTaskHouseKeeping?.totalRoomNeedToSetUp && postTaskHouseKeeping.totalRoomNeedToSetUp?.quantity) {
        const newOptions = detailHousekeeping?.options || [];
        detailHousekeeping = {
          ...detailHousekeeping,
          options: [...newOptions, postTaskHouseKeeping?.totalRoomNeedToSetUp],
        };
      }
      task.detailHousekeeping = detailHousekeeping;
      break;
    case SERVICES.SOFA: // with SOFA service
      const { postTaskSofa } = store;
      const curtain = {};
      const detailSofaData = {}; // data post task
      if (postTaskSofa.sofa && postTaskSofa.sofa.length > 0) {
        detailSofaData.sofa = postTaskSofa?.sofa;
      }

      if (postTaskSofa.mattress && postTaskSofa.mattress.length > 0) {
        detailSofaData.mattress = postTaskSofa.mattress;
      }
      if (postTaskSofa.curtainDryClean && postTaskSofa.curtainDryClean.length > 0) {
        curtain.dryclean = postTaskSofa.curtainDryClean;
        detailSofaData.curtain = curtain;
      }

      // Only set carpet when has data and length > 0
      if (postTaskSofa.carpet && postTaskSofa.carpet.length > 0) {
        detailSofaData.carpet = postTaskSofa.carpet;
      }

      if (postTaskSofa.curtainWashing) {
        curtain.washing = [postTaskSofa.curtainWashing];
        detailSofaData.curtain = curtain;
      }

      // detailSofa is empty, return here, avoid call api get price
      if (_.isEmpty(detailSofaData)) {
        return null;
      }

      task.detailSofa = detailSofaData;
      break;
    case SERVICES.GROCERY_ASSISTANT: // with GROCERY ASSISTANT service
      const { postTaskGroceryAssistant } = store;

      // Todo: noo need current
      // make sure distance > 0
      // if (!postTaskGroceryAssistant?.distance) {
      //   return null;
      // }
      task.askerId = global.userId;
      task.goMarketDetail = {
        distance: postTaskGroceryAssistant?.distance || 0,
      };
      task.duration = 1;
      break;

    case SERVICES.DISINFECTION_SERVICE: // with GROCERY ASSISTANT service
      const { postTaskDisinfectionThailand } = store;
      const { postTaskDisinfection } = store;

      // make sure area > 0
      if (!postTaskDisinfection?.area?.from && !postTaskDisinfection?.customArea) {
        return null;
      }
      if (postTaskDisinfection?.area?.from && postTaskDisinfection?.area?.to) {
        task.disinfectionDetail = {
          area: postTaskDisinfection?.area,
        };
      }
      if (postTaskDisinfection?.customArea && Number(postTaskDisinfection?.customArea) > 0) {
        task.disinfectionDetail = {
          customArea: postTaskDisinfection?.customArea,
        };
      }

      // make sure area > 0
      if (!postTaskDisinfectionThailand?.area?.from && !postTaskDisinfectionThailand?.customArea) {
        return null;
      }
      if (postTaskDisinfectionThailand?.area?.from && postTaskDisinfectionThailand?.area?.to) {
        task.disinfectionDetail = {
          area: postTaskDisinfectionThailand?.area,
        };
      }
      if (postTaskDisinfectionThailand?.customArea && Number(postTaskDisinfectionThailand?.customArea) > 0) {
        task.disinfectionDetail = {
          customArea: postTaskDisinfectionThailand?.customArea,
        };
      }
      break;
    case SERVICES.CHILD_CARE: // with child care service
      const NUMBER_OF_CHILD = {
        empty: 0,
        single: 1,
        double: 2,
      };
      const postTaskChildCareReducer = store.postTaskChildCareReducer;

      // Check data before get price, no detail children -> no call api get price
      if (
        postTaskChildCareReducer?.detailChildren?.length === NUMBER_OF_CHILD.empty ||
        (postTaskChildCareReducer.numberOfChildren === NUMBER_OF_CHILD.double &&
          postTaskChildCareReducer.detailChildren?.length === NUMBER_OF_CHILD.single)
      ) {
        return null;
      }

      if (addOnService && addOnService.length > 0) {
        task.requirements = addOnService.map((e) => ({ type: e.type }));
      }

      task.detailChildCare = {
        numberOfChildren: postTaskChildCareReducer.numberOfChildren,
        detailChildren: postTaskChildCareReducer.detailChildren,
      };
      break;
    case SERVICES.OFFICE_CLEANING: // with OFFICE CLEANING service
      // Requirement
      const addOnServiceOfficeCleaning = store?.postTaskOfficeCleaning?.addOnService;
      if (addOnServiceOfficeCleaning && addOnServiceOfficeCleaning.length > 0) {
        task.requirements = addOnServiceOfficeCleaning.map((e) => ({ type: e.type }));
      }
      // Detail office cleaning
      if (!_.isEmpty(store?.postTaskOfficeCleaning?.detailOfficeCleaning)) {
        task.detailOfficeCleaning = store?.postTaskOfficeCleaning?.detailOfficeCleaning;
      }
      break;
    case SERVICES.WASHING_MACHINE:
      const tasksWM = _.cloneDeep(store?.postTaskWM?.tasks);
      if (!tasksWM.length) {
        return null;
      }
      task.detailWashingMachine = tasksWM.map((e) => {
        //remove all text in item
        if (e.text) {
          delete e.text;
        }
        if (e.type?.text) {
          delete e.type.text;
        }
        if (e.type?.options) {
          e.type.options = e.type.options.map((option) => {
            if (option.text) {
              delete option.text;
            }
            return option;
          });
        }
        return e;
      });
      break;
    case SERVICES.WATER_HEATER:
      const tasksWH = store?.postTaskWaterHeater?.tasks;
      if (_.isEmpty(tasksWH)) {
        return null;
      }
      task.detailWaterHeater = tasksWH;
      break;
    case SERVICES.OFFICE_CARPET_CLEANING:
      const { postTaskOfficeCarpetCleaning } = store;

      if (postTaskOfficeCarpetCleaning?.area?.from && postTaskOfficeCarpetCleaning?.area?.to) {
        task.detailCarpet = {
          area: postTaskOfficeCarpetCleaning?.area,
        };
      }
      if (postTaskOfficeCarpetCleaning?.customArea && Number(postTaskOfficeCarpetCleaning?.customArea) > 0) {
        task.detailCarpet = {
          customArea: Number(postTaskOfficeCarpetCleaning?.customArea),
        };
      }
      break;
    case SERVICES.HOME_MOVING:
      const { postTaskHomeMoving } = store;
      const homeMovingData = {};
      if (postTaskHomeMoving?.oldHomeDetail) {
        homeMovingData.oldHomeDetail = postTaskHomeMoving.oldHomeDetail;
      }
      if (postTaskHomeMoving?.newHomeDetail) {
        homeMovingData.newHomeDetail = postTaskHomeMoving.newHomeDetail;
      }
      if (postTaskHomeMoving?.furniture) {
        homeMovingData.furniture = postTaskHomeMoving.furniture;
      }
      homeMovingData.isInBuilding = Boolean(postTaskHomeMoving.isInBuilding);
      task.detailHomeMoving = homeMovingData;
      break;
    case SERVICES.MASSAGE:
      const { postTaskMassage } = store;
      const packagesData = [];
      if (postTaskMassage?.firstSelectedPackage) {
        packagesData.push({ ...postTaskMassage.firstSelectedPackage, taskerGender: postTaskMassage.firstGender });
      }
      if (postTaskMassage?.secondSelectedPackage) {
        packagesData.push({
          ...postTaskMassage.secondSelectedPackage,
          taskerGender: postTaskMassage.secondGender,
        });
      }
      const detailMassage = {
        packages: packagesData,
      };
      if (postTaskMassage?.type) {
        detailMassage.type = postTaskMassage?.type;
      }
      task.detailMassage = detailMassage;
      // Neu khong co package nao thi ket thuc
      if (isEmpty(packagesData)) {
        return null;
      }

      // Nếu không phải với Tasker yêu thích(forceTasker) và không ẩn gender và có package nào không có gender thì không gọi api get price
      if (
        isEmpty(forceTasker) &&
        !service?.detailService?.massage?.isHideGender &&
        packagesData.some((e) => !e.taskerGender)
      ) {
        return null;
      }
      break;
    case SERVICES.INDUSTRIAL_CLEANING:
      const { postTaskIndustrialCleaning } = store;
      task.detailIndustrialCleaning = {
        homeType: {},
        services: [],
      };
      if (postTaskIndustrialCleaning.homeType) {
        task.detailIndustrialCleaning.homeType = postTaskIndustrialCleaning.homeType;
      }

      if (postTaskIndustrialCleaning.services?.length) {
        task.detailIndustrialCleaning.services = postTaskIndustrialCleaning.services;
      }
      break;
    default:
      break;
  }

  return { task, service: { _id: service._id }, isoCode: app.isoCode };
};

/**
 * @description get price service
 * @param serviceName {{string}}
 */
export const getPrice = (serviceName) => async (dispatch, getState) => {
  // refactor data after call get price
  const data = getDataTask(getState);
  const store = getState();
  // data is null, no get price
  if (!data) {
    // set price is nul --> hide price button.
    return await dispatch(setPrice(null));
  }

  // show loading
  await dispatch(showLoading());

  // show loading price
  await dispatch(setLoadingPrice());

  // call get price API
  const result = await getPriceProvider(data, serviceName);

  // Danh sách các dịch vụ lấy duration từ API trả về
  const LIST_SERVICE_GET_DURATION_FROM_PRICING = [
    SERVICES.HOUSE_KEEPING,
    SERVICES.WASHING_MACHINE,
    SERVICES.WATER_HEATER,
    SERVICES.OFFICE_CARPET_CLEANING,
    SERVICES.MASSAGE,
    SERVICES.INDUSTRIAL_CLEANING,
    // SERVICES.HOME_MOVING,
  ];
  // Set duration for Housekeeping service. Duration is calculated from server by pricing function
  if (LIST_SERVICE_GET_DURATION_FROM_PRICING.includes(serviceName)) {
    await dispatch(setDuration(result?.data?.duration));
  }

  if (serviceName === SERVICES.GROCERY_ASSISTANT) {
    await dispatch(setDuration(1));
  }

  // end get API, hide loading
  await dispatch(showLoading(false));
  // await dispatch(_setLoadingPrice(false));
  // ok

  if (result?.isSuccess) {
    let messageCondition = null;
    const condition = result?.data?.reason?.promotionCodeErrorCondition;
    if (condition === 'PROMOTION_CODE_ERROR_CONDITION_TASKTIME_SAME_DATE') {
      const { fromHour, toHour, inDate } = result.data.reason;
      messageCondition = {
        text: 'PROMOTION_CODE_ERROR_CONDITION_TASKTIME_SAME_DATE',
        params: { t1: fromHour, t2: toHour, t3: inDate },
      };
    } else if (condition === 'PROMOTION_CODE_ERROR_CONDITION_TASKTIME') {
      const { fromDate, toDate } = result.data.reason;
      messageCondition = {
        text: 'PROMOTION_CODE_ERROR_CONDITION_TASKTIME',
        params: {
          t1: formatDate(store?.app?.locale, fromDate),
          t2: formatDate(store?.app?.locale, toDate),
        },
      };
    } else if (condition === 'PROMOTION_CODE_ERROR_CONDITION_CITY') {
      const { place } = result.data.reason;
      messageCondition = { text: 'PROMOTION_CODE_ERROR_CONDITION_CITY', params: { t: place.join(', ') } };
    } else if (condition === 'PROMOTION_CODE_ERROR_CONDITION_DISTRICT') {
      const { place } = result.data.reason;
      messageCondition = { text: 'PROMOTION_CODE_ERROR_CONDITION_DISTRICT', params: { t: place.join(', ') } };
    } else if (condition === 'PROMOTION_CODE_INVALID') {
      messageCondition = { text: 'PROMOTION_DIALOG_CODE_INVALID' };
    } else if (condition === 'PROMOTION_CODE_ERROR_MIN_ORDER_VALUE_NOT_REACHED') {
      messageCondition = { text: 'PROMOTION_DIALOG_CODE_INVALID' };
    }
    if (messageCondition) {
      AlertHolder.alert.open(
        {
          title: 'DIALOG_TITLE_ERROR',
          message: messageCondition,
          actions: [{ text: 'CLOSE' }],
        },
        true,
      );
      // remove promotion
      dispatch(_setPromotion(null));
    }
    return await dispatch(setPrice(result.data));
  } else {
    handleError(result?.error, I18n.locale);
    return await dispatch(setPrice(null));
  }
};

/**
 * @description check promotion code when post task
 * @param code {{string}}
 * @param serviceId {{string}}
 * @param navigation {{object}}
 */
export const checkPromotionCode =
  (code, serviceId, serviceName, navigation, taskDate, finalCost, taskPlace, paymentMethod, cost) =>
  async (dispatch, getState) => {
    // remove old data promotion if exists
    await dispatch(_setPromotion(null));

    const store = getState();
    const { app } = store;
    const askerId = app?.user?._id;
    const currency = app?.settings?.currency;

    // show loading
    await dispatch(showLoading());
    // check promotion
    const result = await checkPromotion({
      code,
      serviceId,
      askerId,
      taskDate,
      finalCost,
      taskPlace,
      paymentMethod,
      cost,
    });

    // promotion ok
    if (result?.isSuccess) {
      // set data promotion to reducer post task
      await dispatch(_setPromotion(result.data.promotion));
      // get price again
      await dispatch(getPrice(serviceName));
      // hide loading
      await dispatch(showLoading(false));
      // auto go back
      return navigation.goBack();
    }
    // hide loading
    await dispatch(showLoading(false));

    // error
    handleErrorCheckPromotion(result, currency);

    return dispatch(_setPromotion(null));
  };

export const setPriceUpdateTask = (priceDetail) => async (dispatch) => {
  if (priceDetail) {
    return await dispatch(setPrice(priceDetail));
  }
};

export const setDateTime = (newDate, service) => async (dispatch, getState) => {
  if (newDate === null) {
    return dispatch(_setDateTime(null));
  }

  const store = getState();
  const { postTaskLaundry, postTask } = store;
  const collectionDate = postTaskLaundry.collectionDate;
  const timezone = DateTimeHelpers.getTimezoneByCity(postTask?.address?.city);
  const chooseDate = DateTimeHelpers.toDateTz({ date: newDate, timezone });
  const collectionDateMoment = DateTimeHelpers.toDateTz({ date: collectionDate, timezone });

  const diffDate = chooseDate.diff(collectionDateMoment, 'hours');
  // check time when laundry service
  if (service && isLaundryService(service?.name) && diffDate < 0) {
    setTimeout(() => {
      return AlertHolder.alert.open(
        {
          title: 'DIALOG_TITLE_ERROR',
          message: 'INCORRECT_DATE_TIME',
          actions: [{ text: 'CLOSE' }],
        },
        true,
      );
    }, 500);
    return null;
  }

  dispatch(_setDateTime(newDate));
};

export const getPromotionPaymentMethod = () => async (dispatch, getState) => {
  // Check exist userId
  if (!global?.userId) {
    return;
  }

  // show loading
  await dispatch(showLoading());
  const result = await getPromotionPaymentMethodAPI();
  if (result?.data?.length > 0) {
    await dispatch(showLoading(false));

    // Todo: Filter true money, only support true money and shopee pay
    const trueMoneyPayment = _.filter(
      result.data,
      (e) => e?.applyFor === PAYMENT_METHOD.trueMoney || e?.applyFor === PAYMENT_METHOD.shopeePay,
    );

    if (isEmpty(trueMoneyPayment)) {
      return null;
    }

    dispatch(setPromotionPaymentMethod(trueMoneyPayment));
    return trueMoneyPayment;
  }

  // hide loading
  await dispatch(showLoading(false));
};

export const handlePrepayment = (bookingId, navigation, callback) => async (dispatch, getState) => {
  await dispatch(showLoading());
  // get data task from reducer
  // check code valid
  if (!bookingId) {
    return;
  }

  const respond = await prepaymentAPI({
    taskId: bookingId,
  });

  await dispatch(showLoading(false));
  // check respond
  if (respond?.isSuccess && respond?.data) {
    // back to home page
    navigation.canGoBack() && navigation.popToTop();
    // // go to tab Activity
    navigation.navigate(RouteName.Home, { screen: 'Tab_Activity' });
    // // go to tab Upcoming
    navigation.navigate('Tab_Activity', { screen: 'Tab_Upcoming' });
    // get user Info again, renew locations
    // getUserInfo();

    // call reset all state post task
    callback && callback();

    const respondPayment = respond?.data;
    // navigate to payment page
    // trueMoney
    if (respondPayment?.payment?.method === PREPAYMENT.trueMoney) {
      return navigation.navigate(RouteName.WebviewForPayment, {
        url: respondPayment?.data,
        type: 'POST_TASK',
        onBack: () => {},
      });
    }
    // promptPay
    if (respondPayment?.payment?.method === PREPAYMENT.promptPay) {
      return navigation.navigate(RouteName.PayWithQRCode, {
        data: respondPayment,
        amount: respondPayment.amount,
        typePayment: PREPAYMENT.promptPay,
        taskId: bookingId,
      });
    }
    // shoppeePay
    if (respondPayment?.payment?.method === PREPAYMENT.shopeePay) {
      // open modal confirm payment
      return navigation.navigate(RouteName.PaymentConfirmedAppToApp, {
        data: respondPayment,
        amount: respondPayment?.amount,
        url: respondPayment?.redirect_url_http,
        urlApp: respondPayment?.redirect_url_app,
        paymentMethod: PREPAYMENT.shopeePay,
      });
    }
    // zaloPay
    if (respondPayment?.payment?.method === PREPAYMENT.zaloPay) {
      if (respondPayment.token) {
        // Back to previous page before link to Momo app
        // props.navigation.pop(2);
        // open modal confirm payment
        return navigation.navigate(RouteName.PaymentConfirmedAppToApp, {
          data: respondPayment,
          amount: respondPayment?.amount,
          url: respondPayment?.url,
          paymentMethod: PREPAYMENT.zaloPay,
        });
        // PayZaloBridge.payOrder(result.token);
        // openUrl(result.url);
      } else {
        AlertHolder.alert.open(
          {
            title: 'DIALOG_TITLE_ERROR',
            message: { text: 'TOP_UP_PAYMENT_FAILED', params: {} },
            actions: [{ text: 'CLOSE', onPress: () => navigation.goBack() }],
          },
          true,
        );
      }
    }
    // momo
    if (respondPayment?.payment?.method === PREPAYMENT.momo) {
      // open modal confirm payment
      return navigation.navigate(RouteName.PaymentConfirmedAppToApp, {
        data: respondPayment,
        amount: respondPayment?.amount,
        url: respondPayment?.payUrl,
        paymentMethod: PREPAYMENT.momo,
      });
    }

    // VNPay
    if (respondPayment?.payment?.method === PREPAYMENT.vnPay) {
      // open modal confirm payment
      return navigation.navigate(RouteName.PaymentConfirmedAppToApp, {
        data: respondPayment,
        amount: respondPayment?.amount,
        url: respondPayment?.paymentUrl,
        paymentMethod: PREPAYMENT.vnPay,
      });
    }

    // id GoPay
    if (respondPayment?.payment?.method === PREPAYMENT.idGoPay) {
      // open modal confirm payment
      return navigation.navigate(RouteName.PaymentConfirmedAppToApp, {
        data: respondPayment,
        amount: respondPayment?.amount,
        url: respondPayment?.paymentUrl,
        paymentMethod: PREPAYMENT.idGoPay,
      });
    }

    // id QRIS
    if (respondPayment?.payment?.method === PREPAYMENT.idQRIS) {
      return navigation.navigate(RouteName.PayWithQRCode, {
        data: respondPayment,
        amount: respondPayment.amount,
        typePayment: PAYMENT_METHOD.idQRIS,
        taskId: bookingId,
      });
    }

    // id QRIS
    if (respondPayment?.payment?.method === PREPAYMENT.idDANA) {
      // open modal confirm payment
      return navigation.navigate(RouteName.PaymentConfirmedAppToApp, {
        data: respondPayment,
        amount: respondPayment?.amount,
        url: respondPayment?.paymentUrl,
        paymentMethod: PREPAYMENT.idDANA,
      });
    }

    // id vietQR
    if (respondPayment?.payment?.method === PREPAYMENT.vietQR && respondPayment?.qrString) {
      // Navigate to PayWithQRCode
      return navigation.navigate(RouteName.PayWithQRCodeString, {
        data: respondPayment,
        taskId: bookingId,
        paymentMethod: PREPAYMENT.vietQR,
      });
    }
    if (respondPayment?.payment?.method === PAYMENT_METHOD.virtualAccount) {
      return navigation.navigate(RouteName.BankTransferInfo, {
        ...respondPayment,
      });
    }

    // kredivo
    if (respondPayment?.payment?.method === PREPAYMENT.kredivo) {
      // open modal confirm payment
      return navigation.navigate(RouteName.PaymentConfirmedAppToApp, {
        data: respondPayment,
        amount: respondPayment?.amount,
        url: respondPayment?.paymentUrl,
        paymentMethod: PREPAYMENT.kredivo,
      });
    }
  }

  // error payment request
  return AlertHolder.alert.open(
    {
      title: 'DIALOG_TITLE_ERROR',
      message: respond?.error?.code === 'TASK_ALREADY_PAID' ? 'TASK_PAID' : 'ERROR_TRY_AGAIN',
      actions: [{ text: 'CLOSE' }],
    },
    true,
  );
};

export const addOnService = (requirement) => async (dispatch, getState) => {
  const { postTask } = getState();
  let newService = [...postTask.addOnService];
  // check exist
  if (newService.find((e) => e.type === requirement.type)) {
    newService = newService.filter((e) => e.type !== requirement.type);
  } else {
    newService.push(requirement);
  }

  // update requirement
  await dispatch(_addRequirement(newService));

  // Get Price
  const service = postTask?.service;
  service && (await dispatch(getPrice(service?.name)));
};

export const changeDateTime = (dateObj) => async (dispatch, getState) => {
  const store = getState();
  const date = _.get(store, 'postTask.date', null);
  const service = _.get(store, 'postTask.service', null);
  const city = _.get(store, 'postTask.address.city', null);
  const timezone = DateTimeHelpers.getTimezoneByCity(city);
  const isSame = DateTimeHelpers.checkIsSame({ firstDate: date, secondDate: dateObj, timezone });
  // check spam, call api with same data
  if (isSame) return null;
  // set new date time
  await dispatch(setDateTime(dateObj, service));
  // get price again
  await dispatch(getPrice(service?.name));
};
