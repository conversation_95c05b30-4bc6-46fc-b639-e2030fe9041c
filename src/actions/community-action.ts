import _, { isEmpty, uniqBy } from 'lodash';
import { Dispatch } from 'redux';

import { CommunityAPI } from '@api/community-api';
import { IParamSearchPostAPI } from '@api/community-api/type';
import { PAGINATION_DEFAULT } from '@constant';
import { RootState } from '@hooks/app-redux';
import { IItemHashtag } from '@models/community/news-feed.model';
import { INotificationItem } from '@models/community/notification.model';
import { ICommunityState } from '@reducer/community-reducer/type';
import { COMMUNITY_SEARCH_ACTION, COMMUNITY_SORT_KEY } from '@src/lib/config/community';
import { Maybe } from '@src/lib/types';

const MIN_SEARCH_TEXT = 5;

export const COMMUNITY_SET_LIST_NEWS_FEEDS = 'COMMUNITY/COMMUNITY_SET_LIST_NEWS_FEEDS';
export const COMMUNITY_SET_SEARCH_HISTORY = 'COMMUNITY/COMMUNITY_SET_SEARCH_HISTORY';
export const COMMUNITY_SET_SEARCH_TEXT = 'COMMUNITY/COMMUNITY_SET_SEARCH_TEXT';
export const COMMUNITY_SET_SEARCH_ACTION = 'COMMUNITY/COMMUNITY_SET_SEARCH_ACTION';
export const COMMUNITY_SET_SEARCH_SORT_KEY = 'COMMUNITY/COMMUNITY_SET_SEARCH_SORT_KEY';
export const COMMUNITY_SET_USER_COMMUNITY = 'COMMUNITY/COMMUNITY_SET_USER_COMMUNITY';
export const COMMUNITY_SET_LIST_USER_OF_SEARCH = 'COMMUNITY/COMMUNITY_SET_LIST_USER_OF_SEARCH';
export const COMMUNITY_SET_LIST_NEWS_FEEDS_OF_SEARCH = 'COMMUNITY/COMMUNITY_SET_LIST_NEWS_FEEDS_OF_SEARCH';
export const COMMUNITY_SET_FAVOURITE_TAGS = 'COMMUNITY/COMMUNITY_SET_FAVOURITE_TAGS';
export const COMMUNITY_SET_DATA_MODIFY_POST = 'COMMUNITY/COMMUNITY_SET_DATA_MODIFY_POST';
export const COMMUNITY_CLEAR_DATA_MODIFY_POST = 'COMMUNITY/COMMUNITY_CLEAR_DATA_MODIFY_POST';
export const COMMUNITY_SET_ITEM_POST_MODIFY = 'COMMUNITY/COMMUNITY_SET_ITEM_POST_MODIFY';
export const COMMUNITY_CLEAR_ITEM_POST_MODIFY = 'COMMUNITY/COMMUNITY_CLEAR_ITEM_POST_MODIFY';
export const COMMUNITY_LOGOUT = 'COMMUNITY/COMMUNITY_LOGOUT';
export const COMMUNITY_SET_NOTIFICATIONS = 'COMMUNITY/COMMUNITY_SET_NOTIFICATIONS';
export const COMMUNITY_SET_HASH_TAG_SORT = 'COMMUNITY/COMMUNITY_SET_HASH_TAG_SORT';
export const COMMUNITY_SET_LOADING = 'COMMUNITY/COMMUNITY_SET_LOADING';
export const COMMUNITY_REFRESH_LIST_NEWS_FEEDS = 'COMMUNITY/COMMUNITY_REFRESH_LIST_NEWS_FEEDS';
export const COMMUNITY_SET_MODIFY_FOLLOW_USER = 'COMMUNITY/COMMUNITY_SET_MODIFY_FOLLOW_USER';
export const COMMUNITY_SET_ALL_HASHTAGS = 'COMMUNITY/COMMUNITY_SET_ALL_HASHTAGS';
export const COMMUNITY_SET_GAME_CAMPAIGN = 'COMMUNITY/COMMUNITY_SET_GAME_CAMPAIGN';
export class CommunityActions {
  static setUserCommunity = (userCommunity: ICommunityState['userCommunity']) => {
    return { type: COMMUNITY_SET_USER_COMMUNITY, payload: userCommunity };
  };

  static logoutCommunity = () => {
    return { type: COMMUNITY_LOGOUT };
  };

  static setListNewsFeedsOfSearch = (listNewsFeedsOfSearch: ICommunityState['listNewsFeedsOfSearch']) => {
    return { type: COMMUNITY_SET_LIST_NEWS_FEEDS_OF_SEARCH, payload: listNewsFeedsOfSearch };
  };

  static setListUserOfSearch = (listUserOfSearch: ICommunityState['listUserOfSearch']) => {
    return { type: COMMUNITY_SET_LIST_USER_OF_SEARCH, payload: listUserOfSearch };
  };

  static setListNewsFeeds = (listNewsFeeds: ICommunityState['listNewsFeeds']) => {
    return { type: COMMUNITY_SET_LIST_NEWS_FEEDS, payload: listNewsFeeds };
  };

  static setSearchHistory = (communitySearchHistory: ICommunityState['communitySearchHistory']) => {
    return { type: COMMUNITY_SET_SEARCH_HISTORY, payload: communitySearchHistory };
  };

  static setFavouriteTags = (favouriteTags: ICommunityState['favouriteTags']) => {
    return { type: COMMUNITY_SET_FAVOURITE_TAGS, payload: favouriteTags };
  };

  static setSearchText = (searchText: string) => {
    return { type: COMMUNITY_SET_SEARCH_TEXT, payload: searchText };
  };

  static setSearchAction = (searchText: string) => {
    return { type: COMMUNITY_SET_SEARCH_ACTION, payload: searchText };
  };

  static setSearchSortKey = (searchText: string) => {
    return { type: COMMUNITY_SET_SEARCH_SORT_KEY, payload: searchText };
  };

  static setHashTagSort = (tagInfo?: IItemHashtag) => {
    return { type: COMMUNITY_SET_HASH_TAG_SORT, payload: tagInfo };
  };

  static setLoading = (loading: boolean) => {
    return { type: COMMUNITY_SET_LOADING, payload: loading };
  };

  static setIsRefreshListNewsFeeds = (isRefreshListNewsFeeds: boolean) => {
    return { type: COMMUNITY_REFRESH_LIST_NEWS_FEEDS, payload: isRefreshListNewsFeeds };
  };

  static addSearchHistory = (searchText: string) => async (dispatch: Dispatch, getState: () => RootState) => {
    const { community } = getState();

    const cloneListSearchText = _.cloneDeep(community?.communitySearchHistory || []);

    // Trường hợp bị trùng search text, sẽ xoá vị trí cũ và thêm search text lên mới nhất
    const indexSearchText = cloneListSearchText.indexOf(searchText);
    if (indexSearchText >= 0) {
      cloneListSearchText.splice(indexSearchText, 1);
    }

    // Thêm search text vào mảng
    cloneListSearchText.unshift(searchText);

    // Xoá những search text đã cũ, limit 5
    if (cloneListSearchText.length > MIN_SEARCH_TEXT) {
      cloneListSearchText.pop();
    }
    dispatch(this.setSearchHistory(cloneListSearchText));
  };

  static deleteSearchHistory = (searchText: string) => async (dispatch: Dispatch, getState: () => RootState) => {
    const { community } = getState();

    const editListSearchText = community?.communitySearchHistory?.filter((item) => item !== searchText);
    dispatch(this.setSearchHistory(editListSearchText));
  };

  static searchPost = (searchText: string) => async (dispatch: Dispatch, getState: () => RootState) => {
    const { community } = getState();
    this.addSearchHistory(searchText)(dispatch, getState);

    const params: IParamSearchPostAPI = {
      searchText,
      sortKey: community?.searchParams?.sortKey,
    };

    const result = await CommunityAPI.searchPostAPI(params);
    if (result?.isSuccess) {
      dispatch(this.setListNewsFeedsOfSearch(result?.data || []));
    }
  };

  static loadMoreSearchPost = () => async (dispatch: Dispatch, getState: () => RootState) => {
    const { community } = getState();
    const searchText = community?.searchParams?.searchText || '';
    const listNewsFeedsOfSearch = community?.listNewsFeedsOfSearch || [];

    const params: IParamSearchPostAPI = {
      searchText,
      sortKey: community?.searchParams?.sortKey,
    };

    if (community?.searchParams?.sortKey === COMMUNITY_SORT_KEY.MOST_RECENT) {
      params.fromDate = _.last(listNewsFeedsOfSearch)?.createdAt;
    }

    if (community?.searchParams?.sortKey === COMMUNITY_SORT_KEY.MOST_RELEVANT) {
      params.lastScore = _.last(listNewsFeedsOfSearch)?.score;
    }

    const result = await CommunityAPI.searchPostAPI(params);
    if (result?.isSuccess && result?.data && result?.data?.length > 0) {
      dispatch(this.setListNewsFeedsOfSearch([...listNewsFeedsOfSearch, ...result?.data]));
    }
  };

  static loadMoreSearchUser = () => async (dispatch: Dispatch, getState: () => RootState) => {
    const { community } = getState();
    const searchText = community?.searchParams?.searchText || '';
    const listUserOfSearch = community?.listUserOfSearch || [];
    const params = {
      searchText,
    };
    const result = await CommunityAPI.searchUserAPI(params);

    if (result?.isSuccess && result?.data && result?.data?.length > 0) {
      dispatch(this.setListUserOfSearch([...listUserOfSearch, ...result?.data]));
    }
  };

  static searchUser = (searchText: string) => async (dispatch: Dispatch, getState: () => RootState) => {
    this.addSearchHistory(searchText)(dispatch, getState);
    const result = await CommunityAPI.searchUserAPI({ searchText });
    if (result?.isSuccess) {
      dispatch(this.setListUserOfSearch(result?.data || []));
    }
  };

  static onSearchCommunity = (searchText: string) => async (dispatch: Dispatch, getState: () => RootState) => {
    const { community } = getState();

    if (community?.searchParams?.action === COMMUNITY_SEARCH_ACTION.SEARCH_POST) {
      return this.searchPost(searchText)(dispatch, getState);
    }
    return this.searchUser(searchText)(dispatch, getState);
  };

  static getUserCommunity = () => async (dispatch: Dispatch, getState: () => RootState) => {
    const { community } = getState();

    const result = await CommunityAPI.getUserCommunityAPI();
    if (result?.isSuccess && !isEmpty(result?.data)) {
      if (!isEmpty(community.favouriteTags)) {
        dispatch(this.setFavouriteTags([]));
      }
      dispatch(this.setUserCommunity(result?.data));

      // Return response để lấy data khi cần
      return result?.data;
    }
  };

  static getListNotifications =
    (page: number = PAGINATION_DEFAULT.page, setIsLoadMore?: (isLoadMore: boolean) => void) =>
    async (dispatch: Dispatch, getState: () => RootState) => {
      const { community } = getState();
      if (isEmpty(community.userCommunity)) {
        return;
      }
      const params = {
        page,
        limit: PAGINATION_DEFAULT.limit,
      };
      const result = await CommunityAPI.getNotificationsAPI(params);
      if (!result?.isSuccess) {
        setIsLoadMore && setIsLoadMore(false);
        return;
      }
      if (result?.data && result?.data?.length < PAGINATION_DEFAULT.limit) {
        setIsLoadMore && setIsLoadMore(false);
      }
      if (page === PAGINATION_DEFAULT.page) {
        dispatch(this.setNotifications(result?.data));
        return;
      }
      const newNotifications = uniqBy([...(community.listNotifications || []), ...(result?.data || [])], '_id');
      dispatch(this.setNotifications(newNotifications));
    };

  static setDataModify = (data: ICommunityState['dataModify']) => {
    return { type: COMMUNITY_SET_DATA_MODIFY_POST, payload: data };
  };

  static clearDataModify = () => {
    return { type: COMMUNITY_CLEAR_DATA_MODIFY_POST };
  };

  static setItemModify = (data?: ICommunityState['itemModify']) => {
    return { type: COMMUNITY_SET_ITEM_POST_MODIFY, payload: data };
  };

  static clearItemModify = () => {
    return { type: COMMUNITY_CLEAR_ITEM_POST_MODIFY };
  };

  static setNotifications = (data: Maybe<INotificationItem[]>) => {
    return { type: COMMUNITY_SET_NOTIFICATIONS, payload: data };
  };

  static setModifyFollowUser = (data: Maybe<ICommunityState['modifyFollowUser']>) => {
    return { type: COMMUNITY_SET_MODIFY_FOLLOW_USER, payload: data };
  };

  static setAllHashtags = (data: Maybe<ICommunityState['allHashtags']>) => {
    return { type: COMMUNITY_SET_ALL_HASHTAGS, payload: data };
  };

  static setGameCampaign = (data: Maybe<ICommunityState['gameCampaign']>) => {
    return { type: COMMUNITY_SET_GAME_CAMPAIGN, payload: data };
  };
}
